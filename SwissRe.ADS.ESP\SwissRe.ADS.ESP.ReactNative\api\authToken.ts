import DeviceInfo from 'react-native-device-info';
import PublicClientApplication, { MSALAccount, MSALConfiguration, MSALResult } from 'react-native-msal';

// 🔹 MSAL configuration (adjust to your Azure AD app registration)
const msalConfig: MSALConfiguration = {
  auth: {
    clientId: 'c81f7bbf-cfa5-4304-922c-dd7aa9789b40',
    redirectUri: 'msauth.com.swissre.iesp.plus.rn://auth',
    authority: 'https://login.microsoftonline.com/45597f60-6e37-4be7-acfb-4c9e23b261ea/v2.0',
  },
};

// Create MSAL instance
const pca = new PublicClientApplication(msalConfig);

// Example scopes for your API
const scopes = ['c81f7bbf-cfa5-4304-922c-dd7aa9789b40/.default'];

/**
 * Detect if running on a simulator
 */
export async function isSimulator(): Promise<boolean> {
  try {
    return await DeviceInfo.isEmulator();
  } catch {
    return false;
  }
}

/**
 * Retrieve access token (real device)
 */
export async function getAccessToken(): Promise<string> {
  // Try to get existing account
  const accounts: MSALAccount[] = await pca.getAccounts();

  let result: MSALResult | undefined;

  if (accounts.length > 0) {
    try {
      // Try silent token acquisition
      result = await pca.acquireTokenSilent({
        scopes,
        account: accounts[0],
      });
    } catch {
      // Silent failed, fall back to interactive
      result = await pca.acquireToken({
        scopes,
      });
    }
  } else {
    // No account, force login
    result = await pca.acquireToken({
      scopes,
    });
  }

  if (!result) throw new Error('Unable to retrieve access token!');

  return result.accessToken;
}
