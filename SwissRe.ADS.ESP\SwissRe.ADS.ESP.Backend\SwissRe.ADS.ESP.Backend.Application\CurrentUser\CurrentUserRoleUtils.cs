﻿using Microsoft.AspNetCore.Authorization;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using System.Reflection;

namespace SwissRe.ADS.ESP.Backend.Application.CurrentUser
{
    /// <summary>
    /// Utility functions for <see cref="CurrentUserRole"/> enum.
    /// </summary>
    public static class CurrentUserRoleUtils
    {
        private static readonly Dictionary<CurrentUserRole, string> _claimsByRole;
        private static readonly Dictionary<string, CurrentUserRole> _rolesByClaim;

        static CurrentUserRoleUtils()
        {
            var currentUserRoleType = typeof(CurrentUserRole);

            var roles = Enum.GetNames<CurrentUserRole>()
                .Select(roleName => currentUserRoleType.GetField(roleName)!)
                .Select(roleField => new
                {
                    Role = (CurrentUserRole)roleField.GetValue(null)!,
                    roleField.GetCustomAttribute<MapsToClaimAttribute>()?.Claim
                })
                .Where(role => role.Claim != null)
                .ToList();

            _claimsByRole = roles.ToDictionary(role => role.Role, role => role.Claim!);
            _rolesByClaim = roles.ToDictionary(role => role.Claim!, role => role.Role);
        }

        /// <summary>
        /// Extracts the role claim value from the <see cref="MapsToClaimAttribute"/> decorating the specified role enum item or throws an exception if the role does not map to any role claim value.
        /// </summary>
        public static string ToClaim(this CurrentUserRole role) =>
            _claimsByRole.GetValueOrDefault(role) ??
            throw new InvalidOperationException($"Role '{role}' has not been decorated with '{typeof(MapsToClaimAttribute).FullName}'.");

        /// <summary>
        /// Tries to convert the specified role claim value to a role enum item using the <see cref="MapsToClaimAttribute"/> decorating the role enum item.
        /// </summary>
        /// <returns>True if the conversion has been successful, otherwise false.</returns>
        public static bool TryConvert(string claim, out CurrentUserRole role) =>
            _rolesByClaim.TryGetValue(claim, out role);

        public static AuthorizationPolicyBuilder RequireRole(this AuthorizationPolicyBuilder builder, params CurrentUserRole[] roles) =>
            builder.RequireRole(roles.Select(role => role.ToClaim()));
    }
}
