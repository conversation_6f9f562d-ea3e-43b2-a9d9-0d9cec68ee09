﻿using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using System.Linq.Expressions;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate
{
    public static class MeetingContainerEntityFrameworkExtensions
    {
        public static Expression<Func<MeetingContainer, GetMeetingDetailResponse>> MeetingContainerProjection(string srUserId)
        {
            return m => new GetMeetingDetailResponse
            {
                Id = m.Id,
                Name = m.Name,
                Description = m.Description,
                State = m.State,
                LastUpdatedOn = m.LastUpdatedOnUTC,
                FlatAgendas = m.Agendas
                    .AsQueryable()
                    .Where(AgendaPermissionExtensions.HasReadAccessFor<MeetingAgenda>(srUserId))
                    .Select(a => new GetMeetingAgendaResponse
                    {
                        Id = a.Id,
                        Name = a.Name,
                        Description = a.Description,
                        Order = a.Order,
                        ParentAgendaId = a.ParentAgendaId,
                        Files = a.Files.Select(f => new FileMetadataResponse(f.Id, f.DisplayFileName, f.FileExtension, f.Supplementary, f.VersionNumber, f.IsLatest, f.Order, f.Size, f.NumberOfPages, f.ReadStatuses.Any(rs => rs.UserId == srUserId))).ToList()
                    }).ToList()
            };
        }
    }
}
