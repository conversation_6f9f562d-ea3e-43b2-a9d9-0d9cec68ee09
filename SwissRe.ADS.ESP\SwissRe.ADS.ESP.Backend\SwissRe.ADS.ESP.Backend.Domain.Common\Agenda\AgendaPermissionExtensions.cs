﻿using System.Linq.Expressions;

namespace SwissRe.ADS.ESP.Backend.Domain.Common.Agenda
{
    public static class AgendaPermissionExtensions
    {
        /// <summary>
        /// Where clause that returns only agendas that the user has read access to.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="userId"></param>
        /// <returns></returns>
        public static Expression<Func<T, bool>> HasReadAccessFor<T>(string userId) where T : IAgendaBase
        {
            return agenda => agenda.Permissions.Any(p => p.UserId == userId && (p.CurrentPermission & AgendaPermissionEnum.Read) > 0);
        }

        /// <summary>
        /// Where clause that returns only agendas that the user has manage access to.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="userId"></param>
        /// <returns></returns>
        public static Expression<Func<T, bool>> HasManageAccessFor<T>(string userId) where T : IAgendaBase
        {
            return agenda => agenda.Permissions.Any(p => p.UserId == userId && (p.CurrentPermission & AgendaPermissionEnum.Manage) > 0);
        }

        /// <summary>
        /// Where clause that returns only agendas that the user has write access to.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="userId"></param>
        /// <returns></returns>
        public static Expression<Func<T, bool>> HasWriteAccessFor<T>(string userId) where T : IAgendaBase
        {
            return agenda => agenda.Permissions.Any(p => p.UserId == userId && (p.CurrentPermission & AgendaPermissionEnum.Write) > 0);
        }
    }
}
