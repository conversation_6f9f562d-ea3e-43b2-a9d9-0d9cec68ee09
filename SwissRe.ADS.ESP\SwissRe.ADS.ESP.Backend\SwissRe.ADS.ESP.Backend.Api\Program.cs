using HealthChecks.UI.Client;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http.Features;
using Serilog;
using SwissRe.ADS.ESP.Backend.Application;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Application.CurrentUser;
using SwissRe.ADS.ESP.Backend.Persistence;
using SwissRe.ADS.Ddd.Events.Dispatching.DomainEvents;
using SwissRe.ADS.Ddd.Events.Dispatching.IntegrationEvents;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.ServiceBus.Registration;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using Microsoft.AspNetCore.ResponseCompression;
using Azure.Messaging.ServiceBus;

namespace SwissRe.ADS.ESP.Backend.Api
{
    public class Program
    {
        private static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            AddServices(builder);

            var app = builder.Build();
            BuildPipeline(app);
            app.Run();
        }

        private static void AddServices(WebApplicationBuilder builder)
        {
            builder.Services.AddApplicationInsightsTelemetry();
            builder.Services.AddSerilog((services, config) =>
            {
                config.WriteTo.Console();
                config.WriteTo.ApplicationInsights(services.GetRequiredService<TelemetryConfiguration>(), TelemetryConverter.Traces);
            });

            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerDocument();
            builder.Services.AddMemoryCache();

            builder.Services.AddDomainEventDispatching(config => config.AddHandlersFrom(typeof(ApiRouteGroup).Assembly));
            var integrationEventsRegistration = builder.Services.AddIntegrationEventDispatching<AppDbContext>(config => config.AddHandlersFrom(typeof(ApiRouteGroup).Assembly));

            if (builder.Environment.IsDevelopment())
            {
                builder.Services.AddAdsServiceBus(builder.Configuration, builder.Environment, configure: config =>
                {
                    config.ServiceBusClientFactory = (fullyQualifiedNamespace, credential) => new ServiceBusClient(builder.Configuration.GetValue<string>("ServiceBus:ConnectionString"));
                    integrationEventsRegistration.AddIntegrationEvents(config);
                });
            }
            else
            {
                builder.Services.AddAdsServiceBus(builder.Configuration, builder.Environment, configure: config => integrationEventsRegistration.AddIntegrationEvents(config));
            }


            builder.Services.AddPersistence(builder.Configuration, integrationEventsRegistration.EntityConfigurationsAssembly);
            builder.Services.AddMinimalApiEndpoints(config => config.AddEndpointsFrom(typeof(ApiRouteGroup).Assembly));
            builder.Services.AddApplication(builder.Configuration);
            builder.Services.AddProblemDetails();
            builder.Services.AddAuth(builder.Configuration, builder.Environment);
            builder.Services.AddHttpContextAccessor();
            builder.Services.AddHealthChecks();




            builder.Services.AddResponseCompression(options =>
            {
                options.Providers.Add<GzipCompressionProvider>();
                options.EnableForHttps = true;
            });

            builder.Services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = System.IO.Compression.CompressionLevel.Optimal;
            });

            builder.Services.AddResponseCompression(options =>
            {
                options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(["application/json"]);
            });

            builder.Services.AddScoped<ICurrentUser, CurrentUser>();
        }

        private static void BuildPipeline(WebApplication app)
        {
            app.UseHealthChecks("/healthCheck", new HealthCheckOptions
            {
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

            if (app.Environment.IsDevelopment())
            {
                app.UseOpenApi();
                app.UseSwaggerUI();
            }

            app.UseExceptionHandler(exApp => exApp.Run(async context =>
            {
                var exception = context.Features.GetRequiredFeature<IExceptionHandlerFeature>().Error;
                await GlobalExceptionHandler.OnExceptionAsync(exception, context, app.Environment);
            }));

            app.UseAuthentication();
            app.UseAuthorization();

            app.MapMinimalApiEndpoints();

            if (app.Environment.IsDevelopment())
            {
                app.UseWhen(context => context.GetEndpoint() == null, // https://exploding-kitten.com/2024/08-usespa-minimal-api#it-doesnt-work
                    b => b.UseSpa(spa =>
                    {
                        spa.UseProxyToSpaDevelopmentServer(app.Configuration.GetValue<string>("AngularUrl")!);
                    }));
            }
        }
    }
}