﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class SyncedChangeEntryConfiguration : IEntityTypeConfiguration<EspSynchronizedChange>
    {
        public void Configure(EntityTypeBuilder<EspSynchronizedChange> builder)
        {
            builder.ToTable("SynchronizedChanges");

            builder.HasKey(e => e.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.Property(e => e.EntityId).IsRequired();
            builder.Property(e => e.UpdatedOn);
            builder.Property(e => e.ValueAsJson).IsRequired(false);
            builder.Property(e => e.UserLostAccess);
        }
    }
}
