using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.MeetingsAggregate.Helpers;
using System.Text.Json;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Models;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Common;
using SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using Microsoft.AspNetCore.Mvc;
namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.FileContentsAggregate.v1.Queries
{
    public class GetFileThumbnailContentsEndpoint(UnitOfWork unitOfWork, ICurrentUser currentUser, IEspEncryptionService espEncryptionService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;
        private readonly IEspEncryptionService _espEncryptionService = espEncryptionService;

        public static void BuildRoute([EndpointRouteBuilder<FileContentsV1RouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/users/fileThumbnailContents/{fileContentsId}",
                    async (GetFileThumbnailContentsEndpoint endpoint,
                           Guid fileContentsId,
                           CancellationToken cancellationToken) =>
                        await endpoint.HandleAsync(fileContentsId, cancellationToken))
                .Produces<FileContentResult>()
                .WithSummary("Retrieves the file thumbnail contents for the current user and device.")
                .WithDescription("Returns the file thumbnail contents.")
                .WithAngularName<FileContentsV1RouteGroup>("GetFileThumbnailContents");
        }

        public async Task<IResult> HandleAsync(Guid fileContentId, CancellationToken cancellationToken)
        {
            //TODO: Robert: Add permission check, so that user can only access files he has access to
            //TODO Robert: Add Last Updated On to FileConent model, so that we don't double download the same file over and over again
            //var fileContents = await _unitOfWork.Repository<FileContent>().GetAsync(fileContentId);
            //var decryptedFileContents = _espEncryptionService.DecryptFile(fileContents.EncryptedContents);

            string localThumbnail = "/Users/<USER>/Downloads/PdfThumbnail.png";

            // Asynchronously download the file and get the content as a byte array
            byte[] fakeThumbnailContents = await File.ReadAllBytesAsync(localThumbnail);

            return Results.File(fakeThumbnailContents, "application/octet-stream", "PdfThumbnail.png");
        }
    }
}