import { FileMetadataResponse } from '@/api/apiServices';
import { FileStorageService } from '@/utils/FileStorageService';
import React from 'react';
import { View, Text, StyleSheet, Image, ViewStyle } from 'react-native';

type DocumentCardContainerStyles = {
  containerStyle?: Pick<ViewStyle, 'width' | 'height' | 'marginBottom' | 'marginLeft'>;
};

export const DocumentCard = ({ fileMetadata, style }: { fileMetadata: FileMetadataResponse; style?: DocumentCardContainerStyles }) => {
  return (
    <View style={[styles.container, style?.containerStyle]}>
      {/* Show image */}
      <View style={styles.imageWrapper}>
        <Image source={{ uri: FileStorageService.getThumbnailFilePath('733832ec-ee13-4d71-b211-51f6e75220b1') }} style={styles.image} />
      </View>
      <Text style={styles.documentName}>{fileMetadata.displayName ?? 'no name'}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#7878801F',
    borderRadius: 5,
    height: 250,
    padding: 10,
  },
  documentName: {
    fontSize: 16,
    marginTop: 8,
  },
  image: {
    borderRadius: 5,
    flex: 1,
    resizeMode: 'contain',
  },
  imageWrapper: {
    backgroundColor: '#F2F2F7',
    borderRadius: 5,
    display: 'flex',
    flex: 1,
  },
});
