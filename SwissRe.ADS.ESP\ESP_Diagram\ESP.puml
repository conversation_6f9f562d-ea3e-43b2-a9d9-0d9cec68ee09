@startuml ESP Database Architecture
!define COMMENT(x) <color:grey>x</color>
!include <C4/C4_Container>
!include <azure/AzureCommon>
!include <azure/AzureC4Integration>
!include <azure/Compute/all>
!include <azure/Web/all>
!include <azure/Containers/all>
!include <azure/Databases/all>
!include <azure/Identity/all>
!include <azure/Storage/all>
!include <azure/DevOps/all>
!include <azure/Management/all>
!include <azure/Integration/all>
!include <logos/Cloudflare>
!include <azure/Security/AzureKeyVault>
!include <logos/elasticsearch>

'my own macros
!define Rel(parent,child) parent }|..|| child

AzureEntityColoring(StratumPods)
!define StratumPods(e_alias, e_label, e_techn) AzureEntity(e_alias, e_label, e_techn, AZURE_SYMBOL_COLOR, AzureContainerApp, StratumPods)
!define StratumPods(e_alias, e_label, e_techn, e_descr) AzureEntity(e_alias, e_label, e_techn, e_descr, AZURE_SYMBOL_COLOR, AzureContainerApp, StratumPods)

sprite $CronJob [64x63/16z] {
    tPVRheGW34MXfRSd__zl3rx6KLGiCfD9pdxH6DOkq88m39UoLSC3sKvzTAk3dQgFzZfOLNtqdOEvr4TVELYJ-kZ2mTDSPPQUM7XuZ9E0d_9XDlW2Rqzv-nUy
    hrmIpE934VbuNMGF-UiY_YolJ9CuzrHeUS_VEKqYAVd2KU9IwBaXpQy1r_eF-_aV3PmySrvydE017zDh0Pybu9vFQae8uqSinc-7sJ_of4eGb_ARt6ewy6IQ
    ubOWTJxC4ptn8OTJZ7kLSxZFFos97lEVdrf4l-z18LgfTpzGtRZ22oB5THBPpVCQbEpDutKyd3McYP4DJtdGk_aVMr3aT_Tt_FK0P1E1eaWyv-aNl60hlnq_
    v8N0q3J-a3PypfgiQ-6o0WMsyR6etbpLMC_VyfzdcVP2CIbAds0__DjAnXXh2-Xs-nOuybJPW2ihuBW5kNw0LCSTFO0Y1wtdbt43ZI7ayeKa9qSunpR2b30b
    bkSdmEeHj1DlDnW-p7UUlniDld1_Q30OliHttf-S1f-z__q1
}
AzureEntityColoring(CronJob)
!define CronJob(e_alias, e_label, e_techn, e_descr) AzureEntity(e_alias, e_label, e_techn, e_descr, AZURE_SYMBOL_COLOR, CronJob, CronJob)

' Define LAYOUT_WITH_LEGEND option
LAYOUT_WITH_LEGEND()

title ESP Architecture diagram

' avoid problems with angled crows feet
skinparam linetype ortho

package "Enums" {
    entity RoleTypeEnum {
        - Admin
        - ReadWrite
        - ReadOnly
        - DenyAccess
    }

    entity MeetingTypeEnum {
        - Meeting
        - ChangeResolution
    }

}

package "MDM" as MDM{
    entity Mdm_Users {
        *id : string
        --
        firstName : string
        lastName : string
    }

    entity Mdm_UserRoles{
        *id : long
        --
        userId : string
        roleId : long
    }

    entity Mdm_Roles {
        *id : long
        --
        name : string
        displayName : string
    }

    Lay_D(Mdm_Users, Mdm_UserRoles)
    Lay_D(Mdm_UserRoles, Mdm_Roles)
}

package "ESP" as ESP{
   
    class Users {
        *id : string
        --
        firstName : string
        lastName : string
        --
        COMMENT('Table of all internal and external users')
    }

    class UserRoles {
        *id : long
        --
        userId : string
        roleId : long
    }

    class Roles {
        *id : long
        --
        name : string
        displayName : string
    }

    class Domain {
        *id : long
        --
        *domainId: long <<FK>>        
        name : string
        description : string
        admin_access_roleId : long
        member_access_roleId : long
        guest_access_roleId : long
        --
        COMMENT('Table that holds meeting groups and topics information')
    }

    class Meeting {
        *id : long
        --
        *domainId: long <<FK>>
        name : string
        description : string        
        type: MeetingTypeEnum
    }

    class "MeetingUserOverrides" as MeetingUserOverrides {
        *id : long
        --
        *meetingId: long <<FK>>
        *userId: string <<FK>>
        role_override: RoleTypeEnum
        --
        COMMENT('Table that allows to explicitly override access for a specific user to a specific meeting')
    }

    class "MeetingAgenda" as MeetingAgenda {
        *id : long
        --
        *meetingId: long <<FK>>
    }

    class Files {
        *id : long
        --
        *meetingId: long <<FK>>
        name: string
    }

    class Annotations {
        *id : long
        --
        *fileId: long <<FK>>
        *userId: string <<FK>>
        annotationXML: string
    }

    class VotingQuestions {
            *id : long
            --
            *meetingId?: long <<FK>>
            question: string
    } 

    
    class VotingAnswers {
            *id : long
            --
            *votingQuestionId: long <<FK>>
            *userId: string <<FK>>
            *on_behalf_of_user_id: string <<FK>>
            answer: string
    }
 
    Rel(Users,UserRoles)
    Rel(Roles, UserRoles)
    Rel(Domain,Roles)
    Rel(Meeting,Domain)
    Rel(MeetingUserOverrides,Meeting)
    Rel(MeetingUserOverrides,Users)
    Rel(Meeting,MeetingAgenda)
    Rel(Meeting,VotingQuestions)
    Rel(Files,MeetingAgenda)
    Rel(VotingQuestions,VotingAnswers)
    Rel(VotingAnswers,Users)
    Rel(Files, Annotations)
    Rel(Users, Annotations)


    Lay_D(UserRoles,Roles)
    Lay_D(Meeting, Domain)
    Lay_R(Meeting,MeetingAgenda)
    Lay_R(MeetingAgenda, Files)
    Lay_R(VotingQuestions, VotingAnswers)
    Lay_R(Domain,VotingQuestions)
    Lay_U(Files, Annotations)
    
    Lay_R(Users,Roles)

    
}


Lay_U(MDM,Enums)
Lay_L(ESP,MDM)
@enduml