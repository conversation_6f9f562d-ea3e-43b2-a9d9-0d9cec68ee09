﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Npgsql;
using SwissRe.ADS.ESP.Backend.Persistence;

namespace SwissRe.ADS.ESP.DataMigration
{
    public class DbConnector
    {
        public AppDbContext NewEspDbContext { get; private set; }
        public NpgsqlConnection NpgsqlConnection { get; private set; }
        private readonly string ConnectionString;

        public DbConnector()
        {
            var config = PrepareConfig();
            ConnectionString = config.GetSection("Persistence:ConnectionString").Value;

            var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
            optionsBuilder.UseNpgsql(ConnectionString);

            var persistenceOptions = new PersistenceOptions
            {
                ConnectionString = ConnectionString,
                // Set other properties as needed, e.g. LogToConsole, AssembliesWithConfigurations
            };

            NpgsqlConnection = new NpgsqlConnection(ConnectionString);
            var options = Options.Create(persistenceOptions);
            NewEspDbContext = new AppDbContext(options, NpgsqlConnection);
        }

        private static IConfigurationRoot PrepareConfig()
        {
            return new ConfigurationBuilder()
                        .SetBasePath(AppContext.BaseDirectory)
                        .AddJsonFile("appsettings.json", optional: false)
                        .AddJsonFile("appsettings.Development.json", optional: true)
                        .Build();
        }

    }
}
