﻿namespace SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser
{
    /// <summary>
    /// Contains all the roles for the current user that the application recognizes.
    /// By decorating a role (enum value) with <see cref="MapsToClaimAttribute"/> the role is mapped to a claim from the token. This information is then being used to initialize <see cref="ICurrentUser.Roles"/> collection.
    /// </summary>
    public enum CurrentUserRole
    {
        [MapsToClaim("admin")]
        Admin,

        [MapsToClaim("visitor")]
        Visitor
    }
}
