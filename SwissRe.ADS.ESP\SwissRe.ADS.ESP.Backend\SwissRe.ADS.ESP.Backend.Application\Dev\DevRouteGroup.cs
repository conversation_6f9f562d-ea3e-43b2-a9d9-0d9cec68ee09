﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SwissRe.ADS.MinimalApi.Endpoints;

namespace SwissRe.ADS.ESP.Backend.Application.Dev
{
    /// <summary>
    /// For local development.
    /// Endpoints under this group are visible only in development.
    /// </summary>
    public class DevRouteGroup : IRouteGroup
    {
        public static IEndpointRouteBuilder? BuildRoute([EndpointRouteBuilder<ApiRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            services.GetRequiredService<IHostEnvironment>().IsDevelopment() ?
                builder.MapGroup("dev").WithTags("dev") :
                null;
    }
}
