﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class UserSalt1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserSalt_Users_UserId",
                schema: "espv2",
                table: "UserSalt");

            migrationBuilder.DropIndex(
                name: "IX_UserSalt_UserId",
                schema: "espv2",
                table: "UserSalt");

            migrationBuilder.CreateIndex(
                name: "IX_UserSalt_UserId",
                schema: "espv2",
                table: "UserSalt",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UserSalt_UserId",
                schema: "espv2",
                table: "UserSalt");

            migrationBuilder.CreateIndex(
                name: "IX_UserSalt_UserId",
                schema: "espv2",
                table: "UserSalt",
                column: "UserId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_UserSalt_Users_UserId",
                schema: "espv2",
                table: "UserSalt",
                column: "UserId",
                principalSchema: "espv2",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
