﻿using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Application.Common.Persistence
{
    public abstract class AggregateRootNotFoundException : Exception
    {
        public abstract string AggregateRootTypeFullName { get; }
    }

    [Serializable]
    public class AggregateRootNotFoundException<TAggregateRoot> : AggregateRootNotFoundException
        where TAggregateRoot : IAggregateRoot
    {
        public override string AggregateRootTypeFullName { get; } = typeof(TAggregateRoot).FullName!;
    }
}
