// useJobData.ts
import { useEffect, useState } from 'react';
import { JobStatus } from './types/JobStatus';
import { SyncJobManager } from './SyncJobManager';

export function useJobStatus(jobId: string) {
  const [status, setStatus] = useState<JobStatus | null>(null);

  useEffect(() => {
    const unsubscribe = SyncJobManager.getInstance().subscribe(jobId, (changed) => {
      setStatus({ changed, lastRun: Date.now() });
    });
    return unsubscribe;
  }, [jobId]);

  return status;
}
