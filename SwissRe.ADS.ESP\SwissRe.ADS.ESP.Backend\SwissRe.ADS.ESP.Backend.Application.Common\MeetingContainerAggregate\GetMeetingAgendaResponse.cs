﻿using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate
{
    public class GetMeetingAgendaResponse
    {
        public Guid Id { get; set; }
        public Guid? ParentAgendaId { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public int Order { get; set; }
        public List<FileMetadataResponse> Files { get; set; } = [];
        public List<GetMeetingAgendaResponse> Children { get; set; } = [];
       
    }
}
