-- NOTE: Annotations table will be migrated by tool
/*
    0   not def
 -100	nrOfPages
 -101	annotationFile
 -102	annotated
 -103	previewIcon
 -104	originalMailed
 -105	annotatedMailed
 -110	officeDoc
-1101	annotationFileTrashed
-1102	annotatedTrashed

-101 are the annontation data
-102 is the file itself
*/

SELECT 
    * 
from 
    esp.t_file_rel where file_tid = 136176288 -- and rel_tid in (-101, -102, -105, -1101, -1102)
order by target_tid

/*
file_tid   rel_tid  target_tid  target_value
136176288	-102	136614363     USER_ID1
136176288	-101	136614362     USER_ID1
136176288	-102	136735113     USER_ID2
136176288	-101	136735112     USER_ID2
136176288	-102	137653753
136176288	-101	137653744
*/

SELECT 
    * 
FROM 
    esp.t_file f
where 
    f.file_tid in (136176288, 136614363)


SELECT 
    f.file_tid as AnnotatedFile_ORIGINAL_DB_ID
    ,convert_from(pkg_crypto.decrypt_blob(document), 'UTF8')
    ,* 
FROM 
    esp.t_file f
join 
    esp.t_file_rel fr on fr.target_tid = f.file_tid
where 
    fr.rel_tid = -101 and f.file_tid in (136614362)


