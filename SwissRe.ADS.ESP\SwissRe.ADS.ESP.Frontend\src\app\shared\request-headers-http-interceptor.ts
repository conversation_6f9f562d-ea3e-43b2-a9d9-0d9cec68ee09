import { HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';

/** Appends extra HTTP headers to requests. */
export const requestHeadersHttpInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {
  const reqClone = req.clone({
    headers: req.headers.set('X-Requested-With', 'XMLHttpRequest') // tells authproxy to return 401 instead of 302 for unauthenticated API requests
  });

  return next(reqClone);
};
