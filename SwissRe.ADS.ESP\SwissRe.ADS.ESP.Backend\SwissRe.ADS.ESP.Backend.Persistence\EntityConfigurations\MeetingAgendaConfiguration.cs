﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    internal class MeetingAgendaConfiguration : IEntityTypeConfiguration<MeetingAgenda>
    {
        public void Configure(EntityTypeBuilder<MeetingAgenda> builder)
        {
            builder.ToTable("MeetingsAgenda");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.Property(x => x.Name).IsRequired().HasMaxLength(512);
            builder.Property(x => x.Description).HasMaxLength(5092);

            builder.OwnsMany(a => a.Permissions,
                ownedNav =>
                {
                    ownedNav.ToJson("Permissions");
                    ownedNav.Property(p => p.CurrentPermission).HasConversion<int>();
                    ownedNav.Property(p => p.InheritedPermission).HasConversion<int>();
                });

            builder.HasMany(x => x.Files)
                .WithOne()
                .HasForeignKey(x => x.MeetingAgendaId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne<MeetingAgenda>()
               .WithMany()
               .HasForeignKey(x => x.ParentAgendaId)
               .OnDelete(DeleteBehavior.Cascade);

            builder.Navigation(x => x.Files).AutoInclude();

            //builder.Navigation(x => x.Children)
            //    .UsePropertyAccessMode(PropertyAccessMode.Field)
            //    .AutoInclude();
        }
    }
}
