﻿using SwissRe.ADS.Ddd.Events.Abstractions;

namespace SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate.Events
{
    public class MeetingAccessRightsChangedForUserEvent(Guid meetingId, string userId) : IEvent
    {
        public static string TypeCode => "MeetingAccessRightsChangedForUser";

        public Guid MeetingId { get; } = meetingId;
        public string UserId { get; } = userId;
    }
}
