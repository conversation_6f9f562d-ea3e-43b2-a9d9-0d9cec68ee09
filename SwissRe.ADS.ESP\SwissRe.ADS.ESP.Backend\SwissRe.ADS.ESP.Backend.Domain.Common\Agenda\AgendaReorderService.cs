﻿namespace SwissRe.ADS.ESP.Backend.Domain.Common.Agenda
{
    public static class AgendaReorderService
    {
        /// <summary>
        /// Static method that helps to reorder agendas in a container based on a list of ordered agenda IDs.
        /// Used to remove duplication of the same logic in different containers.
        /// </summary>
        /// <typeparam name="TAgenda"></typeparam>
        /// <param name="agendas">Internal List of Agendas implemented within the container</param>
        /// <param name="orderedAgendaIds">Ordered list of Agenda GUIDS</param>
        /// <param name="setOrder">Method defined on respective Agenda (MeetingAgenda, DocumentAgenda, ...) that sets the specific order</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        public static void ReorderAgendaItems<TAgenda>(List<TAgenda> agendas, IReadOnlyList<Guid> orderedAgendaIds, Action<TAgenda, int> setOrder) where TAgenda : GuidEntity
        {
            if (orderedAgendaIds == null)
                throw new ArgumentNullException(nameof(orderedAgendaIds));
            if (agendas.Count != orderedAgendaIds.Count)
                throw new ArgumentException("The number of agenda IDs must match the number of agendas.");

            var agendaDict = agendas.ToDictionary(a => a.Id);

            foreach (var id in orderedAgendaIds)
            {
                if (!agendaDict.ContainsKey(id))
                    throw new ArgumentException($"Agenda ID {id} does not exist in this container.");
            }

            for (int i = 0; i < orderedAgendaIds.Count; i++)
            {
                var agenda = agendaDict[orderedAgendaIds[i]];
                setOrder(agenda, i);
            }
        }
    }
}
