import { Component, ElementRef, ViewChild } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-enterprise';
import { IGetAdminMeetingResponse, IGetAdminVotingResponse, IGetContainerResponseBase, IGetUserVotingResponse } from '../../apiServices';
import { MatIconModule } from '@angular/material/icon';
import { MAT_TOOLTIP_DEFAULT_OPTIONS, MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-additional-info-cell-renderer',
  imports: [MatIconModule, MatTooltipModule],
  providers: [
    {
      provide: MAT_TOOLTIP_DEFAULT_OPTIONS,
      useValue: { disableTooltipInteractivity: true }
    }
  ],
  templateUrl: './additional-info-cell-renderer.component.html',
  styleUrl: './additional-info-cell-renderer.component.scss'
})
export class AdditionalInfoCellRendererComponent implements ICellRendererAngularComp {
  @ViewChild('spanElement') spanElement?: ElementRef<HTMLSpanElement>;

  params!: ICellRendererParams<IGetContainerResponseBase>;
  data?: IGetContainerResponseBase;

  agInit(params: ICellRendererParams<IGetContainerResponseBase>): void {
    this.data = params.data;
  }

  refresh(params: ICellRendererParams<IGetContainerResponseBase>): boolean {
    this.data = params.data;
    return true;
  }

  isVotingContainer() {
    return (
      this.data &&
      (((this.data as IGetAdminVotingResponse).totalUsersWithVotingRights !== null &&
        (this.data as IGetAdminVotingResponse).totalUsersWithVotingRights !== undefined) ||
        ((this.data as IGetUserVotingResponse).totalQuestions !== null && (this.data as IGetUserVotingResponse).totalQuestions !== undefined))
    );
  }

  getLocation() {
    return (this.data as IGetAdminMeetingResponse).location;
  }

  showTooltip() {
    if (!this.spanElement) {
      return false;
    }

    if (this.spanElement.nativeElement.scrollWidth > this.spanElement.nativeElement.offsetWidth) {
      return true;
    }

    return false;
  }
}
