﻿using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Queries
{

    public class GetAdminDashboardCurrentDataResponse
    {
        public List<GetAdminMeetingResponse> Meetings { get; set; } = [];
        public List<GetAdminVotingResponse> Votings { get; set; } = [];
    }

    public class GetAdminDashboardCurrentDataEndpoint(UnitOfWork unitOfWork) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/adminDashboard/current", (GetAdminDashboardCurrentDataEndpoint endpoint, CancellationToken cancellationToken) => endpoint.HandleAsync(cancellationToken))
                .WithSummary("Get all current and future meetings that user can manage.")
                .WithDescription("Returns a list of all meeting containers and their agendas that the current user has manage access to, where the meetings are current or upcoming. The response includes meeting metadata and agenda information.")
                .WithAngularName<MeetingContainerRouteGroup>("GetAdminDashboardCurrentData");
        }

        public async Task<GetAdminDashboardCurrentDataResponse> HandleAsync(CancellationToken cancellationToken)
        {
            var allMeetings = await _unitOfWork.ReadRepository<IGetAdminDashboardMeetingsRepository>().GetCurrentAndFutureAdminMeetings(cancellationToken);
            var allVotings = await _unitOfWork.ReadRepository<IGetAdminDashboardVotingsRepository>().GetCurrentAndFutureAdminVotings(cancellationToken);

            return new()
            {
                Meetings = allMeetings,
                Votings = allVotings
            };
        }
    }
}
