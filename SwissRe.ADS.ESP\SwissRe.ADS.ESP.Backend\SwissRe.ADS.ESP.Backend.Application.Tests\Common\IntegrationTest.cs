﻿using Microsoft.Extensions.DependencyInjection;
using SwissRe.ADS.ESP.Backend.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common;

namespace SwissRe.ADS.ESP.Backend.Application.Tests.Common
{
    /// <summary>
    /// Base class for integration tests.
    /// </summary>
    public class IntegrationTest : IClassFixture<IntegrationTestsApplicationFactory>, IDisposable
    {
        private static readonly object _lock = new();
        private static bool _isDatabaseInitialized;
        private readonly IntegrationTestsApplicationFactory _applicationFactory;
        private readonly IServiceScope _scope;

        protected IServiceProvider Services => _scope.ServiceProvider;
        protected UnitOfWork UnitOfWork { get; }

        public IntegrationTest(IntegrationTestsApplicationFactory factory)
        {
            _applicationFactory = factory;
            _scope = _applicationFactory.Services.CreateScope();
            UnitOfWork = Services.GetRequiredService<UnitOfWork>();

            lock (_lock)
            {
                if (!_isDatabaseInitialized)
                {
                    var dbContext = Services.GetRequiredService<AppDbContext>();

                    dbContext.Database.EnsureDeleted();
                    dbContext.Database.EnsureCreated();

                    _isDatabaseInitialized = true;
                }
            }
        }

        public void Dispose()
        {
            _applicationFactory.Dispose();
            _scope.Dispose();
        }
    }
}
