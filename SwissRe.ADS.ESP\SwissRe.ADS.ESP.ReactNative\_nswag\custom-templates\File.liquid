//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v{{ ToolchainVersion }} (http://NSwag.org)
// </auto-generated>
//----------------------

{% template File.Header %}

{{ ExtensionCodeImport -}}
{% if ImportRequiredTypes -%}
{%-    if GenerateClientClasses -%}
{%-        if Framework.IsAngular -%}

{%-            if Framework.UseRxJs5 -%}
import 'rxjs/add/observable/fromPromise';
import 'rxjs/add/observable/of';
import 'rxjs/add/observable/throw';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/toPromise';
import 'rxjs/add/operator/mergeMap';
import 'rxjs/add/operator/catch';

import { Observable } from 'rxjs/Observable';
{%-            else -%}
import { mergeMap as {{ Framework.RxJs.ObservableMergeMapMethod }}, catchError as {{ Framework.RxJs.ObservableCatchMethod }} } from 'rxjs/operators';
import { Observable, {% if UseTransformOptionsMethod %}from as {{ Framework.RxJs.ObservableFromMethod }}, {% endif %}throwError as {{ Framework.RxJs.ObservableThrowMethod }}, of as {{ Framework.RxJs.ObservableOfMethod }} } from 'rxjs';
{%-            endif -%}
import { Injectable, Inject, Optional, {{ Framework.Angular.InjectionTokenType }} } from '@angular/core';
{%-            if Framework.Angular.UseHttpClient -%}
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase{% if IncludeHttpContext %}, HttpContext{% endif %} } from '@angular/common/http';
{%-            else -%}
import { Http, Headers, ResponseContentType, Response{% if UseTransformOptionsMethod %}, RequestOptionsArgs{% endif %} } from '@angular/http';
{%-            endif -%}
{%-        endif -%}
{%-        if Framework.IsAurelia -%}

import { inject } from 'aurelia-framework';
import { HttpClient, RequestInit } from 'aurelia-fetch-client';
{%-        endif -%}
{%-        if Framework.IsAngularJS -%}

import * as ng from 'angular';
{%-        endif -%}
{%-        if Framework.IsAxios -%}
{%-         if SupportsTypeOnlyImports -%}
import axios, { AxiosError } from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, CancelToken } from 'axios';
{%-         else -%}
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse, CancelToken } from 'axios';
{%-         endif -%}
{%-        endif -%}
{%-        if Framework.IsKnockout -%}

import * as ko from 'knockout';
{%-        endif -%}
{%-        if Framework.IsJQuery -%}

import * as jQuery from 'jquery';
{%-        endif -%}
{%-    endif -%}
{%-    if Framework.UseMomentJS -%}

import moment from 'moment';
{%-        if RequiresMomentJSDuration -%}
import 'moment-duration-format';
{%-        endif -%}
{%-    endif -%}
{%-    if Framework.UseDayJS -%}

import dayjs from 'dayjs';
{%-    endif -%}
{%-    if Framework.UseLuxon -%}

import { DateTime, Duration } from "luxon";
{%-    endif -%}
{%- endif -%}

{% if HasModuleName -%}
{% if ExportTypes %}export {% endif %}module {{ ModuleName }} {
{%- endif -%}
{% if HasNamespace -%}
namespace {{ Namespace }} {
{%- endif -%}
{% if GenerateClientClasses and Framework.IsAngular -%}
{% if ExportTypes %}export {% endif %}const {{ Framework.Angular.BaseUrlTokenName }} = new {{ Framework.Angular.InjectionTokenType }}{% if Framework.Angular.InjectionTokenType == "InjectionToken" %}<string>{% endif %}('{{ Framework.Angular.BaseUrlTokenName }}');

{%- endif -%}
{{ ExtensionCodeTop }}

{{ Clients }}

{{ Types }}

{% if WrapResponses and GenerateResponseClasses -%}
{%-    for responseClassName in ResponseClassNames -%}
{% if ExportTypes %}export {% endif %}class {{ responseClassName }}<TResult> {
    status: number;
    headers: { [key: string]: any; };
    result: TResult;

    constructor(status: number, headers: { [key: string]: any; }, result: TResult)
    {
        this.status = status;
        this.headers = headers;
        this.result = result;
    }
}

{%-    endfor -%}
{%- endif -%}
{% if RequiresFileParameterInterface -%}
{% if ExportTypes %}export {% endif %}interface FileParameter {
    data: any;
    fileName: string;
}

{%- endif -%}
{% if RequiresFileResponseInterface -%}
{% if ExportTypes %}export {% endif %}interface FileResponse {
    data: Uint8Array;
    status: number;
    fileName?: string;
    headers?: { [name: string]: any };
}

{%- endif -%}
{% if RequiresExceptionClass -%}
{% if ExportTypes %}export {% endif %}class {{ ExceptionClassName }} extends Error {
    {% if SupportsOverrideKeyword %}override {% endif %}message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected is{{ ExceptionClassName }} = true;

    static is{{ ExceptionClassName }}(obj: any): obj is {{ ExceptionClassName }} {
        return obj.is{{ ExceptionClassName }} === true;
    }
}

{%- endif -%}
{% if GenerateClientClasses -%}
{% template File.Utilities %}
{%- endif -%}

{{ ExtensionCodeBottom }}
{% if HasNamespace -%}
}
{%- endif -%}
{% if HasModuleName -%}
}
{%- endif -%}