import { Component, computed, inject } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { CurrentUserService } from '../../../shared/current-user.service';
import { CommonModule } from '@angular/common';
import { AppHeaderConfig } from '../config';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-header-right-section-desktop',
  imports: [CommonModule, MatToolbarModule, MatIconModule, MatTooltipModule, MatMenuModule, MatDividerModule, MatButtonModule],
  template: `
    @if (appVersion(); as appVersion) {
      <div class="app-version">{{ appVersion }}</div>
    }

    <div class="user-name">{{ currentUser()?.fullName }}</div>

    @for (action of mainActions(); track action.key) {
      <button mat-icon-button (click)="action.action()" [matTooltip]="action.title" [disabled]="action.enabled === false">
        <mat-icon>{{ action.icon }}</mat-icon>
      </button>
    }

    @if (menuItems(); as menuItems) {
      @if (menuItems.length) {
        <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="menu">
          <mat-icon>more_vert</mat-icon>
        </button>
      }

      <mat-menu #menu="matMenu">
        @for (menuItem of menuItems; track menuItem.key) {
          <button mat-menu-item [disabled]="menuItem.enabled === false" (click)="menuItem.action()">
            <mat-icon>{{ menuItem.icon }}</mat-icon>
            <span>{{ menuItem.title }}</span>
          </button>
        }
      </mat-menu>
    }
  `,
  styles: `
    :host {
      display: flex;
      align-items: center;
    }
    .environment-name,
    .app-version {
        font-size: 12px;
        margin-right: 8px;
    }
    .user-name {
        font-size: 16px;
        margin: 0 8px;
    }
  `
})
export class AppHeaderRightSectionDesktopComponent {
  readonly currentUser = toSignal(inject(CurrentUserService).currentUser$);

  private readonly appHeader = toSignal(inject(AppHeaderConfig).appHeader$);
  readonly appVersion = computed(() => this.appHeader()?.appVersion);
  readonly mainActions = computed(() => this.appHeader()?.mainActions?.filter(action => action.visible !== false) || []);
  readonly menuItems = computed(() => this.appHeader()?.menuItems?.filter(item => item.visible !== false) || []);
}
