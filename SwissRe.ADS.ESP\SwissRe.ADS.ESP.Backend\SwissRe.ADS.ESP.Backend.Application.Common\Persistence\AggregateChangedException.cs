﻿namespace SwissRe.ADS.ESP.Backend.Application.Common.Persistence
{
    [Serializable]
    public class AggregateChangedException(string aggregateRootTypeName, string expectedRowVersionBase64, string actualRowVersionBase64)
        : Exception("Row version has changed. This indicates that the aggregate has changed and is no longer in the expected version.")
    {
        public string AggregateRootTypeName { get; } = aggregateRootTypeName;
        public string ExpectedRowVersionBase64 { get; } = expectedRowVersionBase64;
        public string ActualRowVersionBase64 { get; } = actualRowVersionBase64;
    }
}
