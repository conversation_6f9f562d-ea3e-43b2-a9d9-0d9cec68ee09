import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RunningGlobalLoader } from './global-loader.service';
import { Observable, map, startWith, tap } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';

@Component({
  imports: [CommonModule, MatProgressSpinnerModule, MatIconModule],
  template: `
    <mat-spinner [diameter]="70"></mat-spinner>

    @for (item of items$ | async; track item.text) {
      <div class="item">
        <mat-icon></mat-icon>
        <span>{{ item.text }}</span>
        <mat-icon>{{ item.isRunning ? '' : 'done' }}</mat-icon>
      </div>
    }
  `,
  styles: `
    :host { 
        padding: 32px; 
        display: flex; 
        flex-direction: column; 
        align-items: center; 
        min-width: 200px;
    }
    .item {
        margin-top: 8px;
        &:first-of-type {
            margin-top: 32px;
        }
        display: flex;
        align-items: center;
        gap: 8px;
        mat-icon {
            color: #046e00;
        }
    }
  `
})
export class GlobalLoaderComponent {
  private itemsSnapshot: Item[] = [];

  readonly items$: Observable<Item[]> = inject<Observable<RunningGlobalLoader[]>>(MAT_DIALOG_DATA).pipe(
    map(loaders => {
      const runningLoaderTexts = [...new Set(loaders.filter(loader => loader.text).map(loader => loader.text!))];

      const currentItems = this.itemsSnapshot.map(item => ({ ...item, isRunning: runningLoaderTexts.includes(item.text) }));
      const newItems = runningLoaderTexts.filter(text => !currentItems.find(item => item.text === text)).map(text => ({ text, isRunning: true }));

      return [...currentItems, ...newItems];
    }),
    tap(items => (this.itemsSnapshot = items)),
    startWith(this.itemsSnapshot)
  );
}

interface Item {
  text: string;
  isRunning: boolean;
}
