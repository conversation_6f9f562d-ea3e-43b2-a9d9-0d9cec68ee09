-- Domains Table
-- select * from esp.t_mp_mtgroup
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."Domains") <> 0 THEN
        RAISE NOTICE 'Delete from espv2.Domains';
        DELETE FROM espv2."Domains";
    END IF;
END $$;
-- Parents

INSERT INTO espv2."Domains" (
    "Id"
    ,"ParentId"
    ,"Name"
    ,"Description"
    ,"ORIGINAL_DB_ID"
    ,"HistoricalDocumentExpirationAfter"
    ,"AnnotationsExpirationAfter"
    ,"iPadDocumentsExpirationAfter"
    ,"ChangeStatusToPastAfter"
    ,"Order"
)
VALUES
(
    '00000000-0000-0000-0000-000000000000'
    ,NULL
    ,'Dummy Parentless Domain'
    ,'Dummy Domain for meetings with no DomainId'
    ,0
    ,NULL
    ,NULL
    ,NULL
    ,NULL
    ,-999999
);

INSERT INTO espv2."Domains" (
    "Id"
    ,"ParentId"
    ,"Name"
    ,"Description"
    ,"ORIGINAL_DB_ID"
    ,"HistoricalDocumentExpirationAfter"
    ,"AnnotationsExpirationAfter"
    ,"iPadDocumentsExpirationAfter"
    ,"ChangeStatusToPastAfter"
    ,"Order"
)
SELECT 
    gen_random_uuid() AS Id
    ,NULL AS ParentId
    ,name AS Name
    ,description AS Description
    ,mtgroup_tid as ORIGINAL_DB_ID
    ,NULL AS HistoricalDocumentExpirationAfter
    ,NULL AS AnnotationsExpirationAfter
    ,NULL AS iPadDocumentsExpirationAfter
    ,NULL AS ChangeStatusToPastAfter
    ,ord AS "Order"
FROM 
    esp.t_mp_mtgroup;

-- Children

INSERT INTO espv2."Domains" (
    "Id"
    ,"ParentId"
    ,"Name"
    ,"Description"
    ,"ORIGINAL_DB_ID"
    ,"HistoricalDocumentExpirationAfter"
    ,"AnnotationsExpirationAfter"
    ,"iPadDocumentsExpirationAfter"
    ,"ChangeStatusToPastAfter"
    ,"Order"
)
SELECT 
    gen_random_uuid() AS Id
    ,(SELECT "Id" FROM espv2."Domains" WHERE "ORIGINAL_DB_ID" = t.mtgroup_tid) AS ParentId
    ,t.name AS Name
    ,t.description AS Description
    ,t.type_tid AS ORIGINAL_DB_ID
    ,t.hist_exp AS HistoricalDocumentExpirationAfter
    ,t.anno_exp AS AnnotationsExpirationAfter
    ,t.ipad_exp AS iPadDocumentsExpirationAfter
    ,t.past_exp AS ChangeStatusToPastAfter
    ,(CASE when t.ord is null then 1 else t.ord end) AS "Order"
FROM 
    esp.t_mp_type t
JOIN
    esp.t_mp_mtgroup g ON g.mtgroup_tid = t.mtgroup_tid
ORDER BY g.name;

INSERT INTO espv2."Domains" (
    "Id"
    ,"ParentId"
    ,"Name"
    ,"Description"
    ,"ORIGINAL_DB_ID"
    ,"HistoricalDocumentExpirationAfter"
    ,"AnnotationsExpirationAfter"
    ,"iPadDocumentsExpirationAfter"
    ,"ChangeStatusToPastAfter"
    ,"Order"
)
SELECT 
    gen_random_uuid() AS Id
    ,(SELECT "Id" FROM espv2."Domains" WHERE "ORIGINAL_DB_ID" = t.mtgroup_tid) AS ParentId
    ,t.name AS Name
    ,t.description AS Description
    ,t.type_tid AS ORIGINAL_DB_ID
    ,t.hist_exp AS HistoricalDocumentExpirationAfter
    ,t.anno_exp AS AnnotationsExpirationAfter
    ,t.ipad_exp AS iPadDocumentsExpirationAfter
    ,t.past_exp AS ChangeStatusToPastAfter
    ,(CASE when t.ord is null then 1 else t.ord end) AS "Order"
FROM 
    esp.t_mp_type t
where t.item_type = 'GD'

--TODO: there are GDs with mtgroup_tid NULL DO NOT FORGET!!!
--select * from esp.t_mp_type where mtgroup_tid is null
--SELECT * FROM espv2."Domains"