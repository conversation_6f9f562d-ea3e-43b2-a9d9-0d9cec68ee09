﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class EspDomainRoleConfiguration : IEntityTypeConfiguration<EspDomainRole>
    {
        public void Configure(EntityTypeBuilder<EspDomainRole> builder)
        {
            builder.ToTable("DomainRoles");
            builder.Has<PERSON>ey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.Property(x => x.RoleType).IsRequired().HasConversion<string>().HasMaxLength(255);

            builder.HasOne(o => o.Domain)
                .WithMany(o=>o.DomainRoles)
                .HasForeignKey(x => x.DomainId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(o => o.Role)
                .WithMany()
                .HasForeignKey(x => x.RoleId)
                .OnDelete(DeleteBehavior.Restrict);

        }
    }
}
