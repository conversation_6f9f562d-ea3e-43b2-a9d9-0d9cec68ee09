import { GetMeetingAgendaResponse } from '@/api/apiServices';
import { DocumentCard } from '@/components/DocumentCard';
import { ThemedText } from '@/components/ThemedText';
import { useEspAppTheme } from '@/hooks/useEspTheme';
import { RefObject, useState } from 'react';
import { ScrollView, View, Text, NativeSyntheticEvent, StyleSheet, NativeScrollEvent, LayoutChangeEvent, useWindowDimensions } from 'react-native';

type AgendaListProps = {
  meetingTitle: string;
  meetingDescription?: string;
  agendas: Array<{ agenda: GetMeetingAgendaResponse; level: number }>;
  scrollViewRef: RefObject<ScrollView>;
  onScroll: (_event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  onAgendaLayout: (_agendaId: string, _coordinatesY: number) => void;
};

const AGENDALIST_SCROLLCONTAINER_PADDING_HORIZONTAL = 30;
const AGENDALIST_FILEGRIDCONTAINER_PADDING = 20;

/**
 * Component: AgendaList
 * ----------------------
 * Renders a flat list of agendas in a ScrollView.
 *
 * Responsibilities:
 * - Displays agenda name, description, and optional filler content.
 * - Supports indentation for hierarchical levels via marginLeft.
 * - Reports onLayout positions back to the parent via onAgendaLayout.
 *
 * Props:
 * - agendas: FlattenedAgenda[] - list of agendas with hierarchy level.
 * - scrollViewRef: RefObject<ScrollView> - reference to parent ScrollView.
 * - onScroll: Scroll event handler.
 * - onAgendaLayout: Callback when an agenda is laid out { id, y }.
 *
 * Notes:
 * - Fully decouples agenda rendering from the MeetingDetail screen.
 * - Can be reused in any screen requiring a scrollable agenda list.
 */
export function AgendaList({ meetingTitle, meetingDescription, agendas, scrollViewRef, onScroll, onAgendaLayout }: AgendaListProps) {
  const { width } = useWindowDimensions();
  const theme = useEspAppTheme();

  const [cardWidth, setCardWidth] = useState<number>(100);
  const margin = 10;

  const getNumColumns = (): number => {
    if (width >= 1200) return 3; // iPad Pro Max
    if (width >= 900) return 3; // regular iPad Pro
    if (width >= 600) return 2; // iPad Air
    return 1;
  };

  const columns = getNumColumns();

  const onContainerLayout = (e: LayoutChangeEvent) => {
    const containerWidth = e.nativeEvent.layout.width;
    const availableWidth = containerWidth - AGENDALIST_SCROLLCONTAINER_PADDING_HORIZONTAL * 2 - AGENDALIST_FILEGRIDCONTAINER_PADDING * 2 - (margin / 2) * (columns + 1);
    setCardWidth(availableWidth / columns);
  };
  return (
    <ScrollView ref={scrollViewRef} onScroll={onScroll} scrollEventThrottle={16} onLayout={onContainerLayout} style={styles.scrollContainer}>
      <ThemedText type="subtitle">{meetingTitle}</ThemedText>
      <ThemedText type="description">{meetingDescription}</ThemedText>

      {agendas.map(({ agenda, level }) => (
        <View style={styles.agendaWrapper} key={agenda.id} onLayout={(e) => onAgendaLayout(agenda.id, e.nativeEvent.layout.y)}>
          <Text style={styles.agendaTitle}>{agenda.name}</Text>
          <View style={[styles.fileGrid, { backgroundColor: theme.colors.backgroundColors.tertiary }]}>
            {agenda.files.map((file, index) => {
              const isFirst = index % columns === 0;
              const totalItems = agenda.files.length;
              const lastRowStart = Math.floor((totalItems - 1) / columns) * columns;
              const isLastRow = index >= lastRowStart;
              return (
                <DocumentCard
                  key={file.id}
                  fileMetadata={file}
                  style={{ containerStyle: { width: cardWidth, marginLeft: isFirst ? 0 : margin, marginBottom: isLastRow ? 0 : margin, height: cardWidth * 0.7 } }}
                />
              );
            })}
          </View>
        </View>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  agendaTitle: {
    fontSize: 13,
    fontWeight: 'normal',
    paddingBottom: 7,
    paddingLeft: 20,
    paddingRight: 20,
    textTransform: 'uppercase',
  },
  agendaWrapper: {
    marginTop: 24,
  },
  fileGrid: {
    borderRadius: 5,
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: AGENDALIST_FILEGRIDCONTAINER_PADDING,
  },

  scrollContainer: {
    paddingHorizontal: AGENDALIST_SCROLLCONTAINER_PADDING_HORIZONTAL,
    paddingVertical: 20,
  },
});
