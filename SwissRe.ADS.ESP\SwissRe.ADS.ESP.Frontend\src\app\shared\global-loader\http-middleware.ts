import { HttpRequest, HttpContextToken, HttpInterceptorFn, HttpHandlerFn, HttpEventType } from '@angular/common/http';
import { inject } from '@angular/core';
import { tap } from 'rxjs';
import { HttpContextModifier } from '../http-context';
import { GlobalLoaderService } from './global-loader.service';

/**
 * Displays a global loader at the start of the http request and hides it as soon as the request ends (success/fail).
 * To be used with 'useHttpContext' function.
 */
export function withGlobalLoader(text?: string | null): HttpContextModifier {
  return httpContext => httpContext.set(GLOBAL_LOADER_HTTP_CONTEXT_TOKEN, { text });
}

export const globalLoaderHttpInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {
  const tokenValue = req.context.get(GLOBAL_LOADER_HTTP_CONTEXT_TOKEN);
  const hideLoader = tokenValue && inject(GlobalLoaderService).show(tokenValue.text);

  return next(req).pipe(
    tap({
      next: event => event.type === HttpEventType.Response && hideLoader?.(),
      error: () => hideLoader?.()
    })
  );
};

const GLOBAL_LOADER_HTTP_CONTEXT_TOKEN = new HttpContextToken(() => null as GlobalLoaderHttpContextTokenValue | null);

interface GlobalLoaderHttpContextTokenValue {
  text: string | null;
}
