@if (data) {
  <div class="additional-info">
    @if (isVotingContainer()) {
      <mat-icon fontIcon="sticky_note_2" inline></mat-icon>
      <span #spanElement [matTooltipDisabled]="!showTooltip()" matTooltip="Resolution / Attestation" matTooltipPosition="above"
        >Resolution / Attestation</span
      >
    } @else if (getLocation()) {
      <mat-icon fontIcon="location_on" inline></mat-icon>
      <span #spanElement [matTooltipDisabled]="!showTooltip()" [matTooltip]="getLocation()" matTooltipPosition="above">{{ getLocation() }}</span>
    }
  </div>
}
