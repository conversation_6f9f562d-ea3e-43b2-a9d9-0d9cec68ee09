﻿using SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata;

namespace SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate
{
    public class DocumentFileMetadata : FileMetadataBase
    {
        public Guid DocumentAgendaId { get; private set; }

        private DocumentFileMetadata() { }

        public static DocumentFileMetadata Create(Guid documentAgendaId,
            string originalFileName,
            Guid fileGroupId,
            Guid fileContentId,
            int versionNumber,
            string fileExtension,
            int size,
            int numberOfPages,
            int order,
            string createdByUserId,
            bool supplementary = false)
        {
            var result = new DocumentFileMetadata();
            result.Initialize(originalFileName, fileGroupId, fileContentId, versionNumber, fileExtension, size, numberOfPages, order, createdByUserId, supplementary);
            result.DocumentAgendaId = documentAgendaId;
            return result;
        }
    }
}
