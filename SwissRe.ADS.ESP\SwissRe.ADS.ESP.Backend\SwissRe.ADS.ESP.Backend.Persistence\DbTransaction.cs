﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Persistence
{
    public class DbTransaction(AppDbContext dbContext) : IDbTransaction
    {
        private readonly DbContext _dbContext = dbContext;

        public virtual Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            SetAggregateRootsAsModified();
            return _dbContext.SaveChangesAsync(cancellationToken);
        }

        protected virtual void SetAggregateRootsAsModified() =>
            _dbContext.ChangeTracker.Entries()
                .Where(entry => entry.Entity is IAggregateRoot && entry.State == EntityState.Unchanged)
                .ToList()
                .ForEach(entry => entry.State = EntityState.Modified); // to make sure RowVersion is checked and updated for each loaded aggregate
    }
}
