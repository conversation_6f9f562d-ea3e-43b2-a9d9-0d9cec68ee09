using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using Microsoft.AspNetCore.Builder;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Commands
{
    public class MarkMeetingFileAsReadEndpoint(
        UnitOfWork unitOfWork,
        IAgendaAuthorizationService agendaPermissionService,
        ICurrentUser currentUser
    ) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapPost("/containers/{containerId}/agendas/{agendaId}/files/{fileId}/read", async (Guid containerId, Guid agendaId, Guid fileId, MarkMeetingFileAsReadEndpoint endpoint) =>
                    await endpoint.HandleAsync(containerId, agendaId, fileId))
                .WithSummary("Mark a meeting agenda file as read for the current user.")
                .WithDescription("Marks the specified file in a meeting agenda as read for the current user. Requires view permission on the agenda.")
                .DisableAntiforgery()
                .WithAngularName<MeetingContainerRouteGroup>("MarkMeetingFileAsRead");

        public async Task HandleAsync(Guid containerId, Guid agendaId, Guid fileId)
        {
            // Permission check: user must be able to view the agenda
            var canViewAgenda = await _agendaPermissionService.CanUserViewAgendaAsync(containerId, agendaId, AgendaTypeEnum.MeetingAgenda);
            if (!canViewAgenda)
                throw new UnauthorizedAccessException("Current user does not have permission to view this agenda");

            var container = await _unitOfWork.Repository<MeetingContainer>().GetAsync(containerId, true);
            container.MarkFileAsRead(agendaId, fileId, _currentUser.SrUserId);

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
