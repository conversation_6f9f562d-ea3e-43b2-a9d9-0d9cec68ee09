import { getDatabase } from '../databaseClient';
import { deserializeDatabaseEntity, serializeDatabaseEntity } from '../helpers/databaseEntitySerializerHelper';
import { ColumnSchema } from '../types/ColumnMapping';

export class BaseRepository<T> {
  public readonly db = getDatabase();

  public readonly tableName: string;
  public readonly schema: ColumnSchema<T>;

  constructor(tableName: string, schema: ColumnSchema<T>) {
    this.tableName = tableName;
    this.schema = schema;
  }

  async insertOrReplace(entity: T) {
    const data = serializeDatabaseEntity(entity, this.schema);
    const columns = Object.keys(data).join(', ');
    const placeholders = Object.keys(data)
      .map(() => '?')
      .join(', ');
    const values = Object.values(data);

    await this.db.executeBatch([[`INSERT OR REPLACE INTO ${this.tableName} (${columns}) VALUES (${placeholders})`, values]]);
  }

  async insertOrReplaceMany(entities: T[]) {
    const stmts = entities.map<[string, any[]]>((entity) => {
      const data = serializeDatabaseEntity(entity, this.schema);
      const columns = Object.keys(data).join(', ');
      const placeholders = Object.keys(data)
        .map(() => '?')
        .join(', ');
      const values = Object.values(data);

      return [`INSERT OR REPLACE INTO ${this.tableName} (${columns}) VALUES (${placeholders})`, values];
    });

    await this.db.executeBatch(stmts);
  }

  async getAll(whereClause?: string): Promise<T[]> {
    let sqlQuery = `SELECT * FROM ${this.tableName}`;

    if (whereClause) sqlQuery += ` WHERE ${whereClause}`;

    const rows = await this.db.getAll<Record<string, any>>(sqlQuery);
    return rows.map((row) => deserializeDatabaseEntity(row, this.schema));
  }

  async getById(id: string | number): Promise<T | undefined> {
    const row = await this.db.getFirst<Record<string, any>>(`SELECT * FROM ${this.tableName} WHERE id = ?`, [id]);
    return row ? deserializeDatabaseEntity(row, this.schema) : undefined;
  }

  async deleteById(id: string | number) {
    await this.db.executeBatch([[`DELETE FROM ${this.tableName} WHERE id = ?`, [id]]]);
  }
}
