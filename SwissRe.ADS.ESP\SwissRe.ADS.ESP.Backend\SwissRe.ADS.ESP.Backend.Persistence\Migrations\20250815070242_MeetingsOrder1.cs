﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class MeetingsOrder1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "Order",
                schema: "espv2",
                table: "MeetingsContainer",
                type: "integer",
                nullable: false,
                defaultValue: -999999,
                oldClrType: typeof(int),
                oldType: "integer");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "Order",
                schema: "espv2",
                table: "MeetingsContainer",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer",
                oldDefaultValue: -999999);
        }
    }
}
