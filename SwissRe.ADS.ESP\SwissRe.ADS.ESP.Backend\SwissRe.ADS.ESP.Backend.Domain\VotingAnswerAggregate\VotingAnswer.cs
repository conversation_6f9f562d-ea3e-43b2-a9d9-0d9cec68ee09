﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.Events;
using SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.Events;

namespace SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate
{
    public class VotingAnswer : GuidEntity, IAggregateRoot
    {
        public Guid VotingQuestionId { get; private set; }

        public byte[] AnswerEncrypted { get; private set; } = null!;
        public byte[]? CommentEncrypted { get; private set; }
        public DateTime CreatedOn { get; private set; }
        public string CreatedByUserId { get; private set; } = null!;
        public bool IsLatest { get; private set; }

        public string? OnBehalfOfUserId { get; private set; }

        // TODO: Remove this property after migration to new file content system
        public long? ORIGINAL_ONBEHALF_FILE_ID { get; private set; }

        public Guid? OnBehalfOfDocumenMetadatatId { get; private set; }
        public VotingAnswerSupportingDocumentFileMetadata? OnBehalfOfDocumentMetadata { get; private set; }

        public static VotingAnswer Create(
            Guid votingQuestionId,
            byte[] answer,
            byte[]? comment,
            string currentUserId)
        {
            Guard.Against.NullOrEmpty(answer, nameof(answer), "Answer cannot be blank.");

            var result = new VotingAnswer()
            {
                VotingQuestionId = votingQuestionId,
                AnswerEncrypted = answer,
                CommentEncrypted = comment,
                CreatedOn = DateTime.UtcNow,
                CreatedByUserId = currentUserId,
                IsLatest = true
            };

            result.RaiseEvent(new UserVotedEvent(votingQuestionId));

            return result;
        }

        public static VotingAnswer CreateOnBehalfOf(
            Guid votingQuestionId,
            byte[] answer,
            byte[]? comment,
            string currentUserId,
            string onBehalfOfUser,
            byte[] onBehalfOfFileContents,
            string fileName,
            string extension,
            int size,
            int order,
            int numberOfPages,
            string createdByUserId,
            bool isSupplementaryFile = false)
        {
            Guard.Against.Null(onBehalfOfUser, nameof(onBehalfOfUser), "On behalf of user cannot be null.");
            var votingAnswer = Create(votingQuestionId, answer, comment, currentUserId);

            var fileContentId = SequentialGuidGenerator.Next();
            var document = VotingAnswerSupportingDocumentFileMetadata.Create(fileName, Guid.NewGuid(), fileContentId, 1, extension, size, numberOfPages, order, createdByUserId, isSupplementaryFile);

            votingAnswer.RaiseEvent(new FileMetadataAddedEvent(onBehalfOfFileContents, fileContentId));

            votingAnswer.OnBehalfOfUserId = onBehalfOfUser;
            votingAnswer.OnBehalfOfDocumenMetadatatId = document.Id;
            return votingAnswer;
        }

        /// <summary>
        /// Marks the voting answer as historical, indicating that it is no longer the latest answer.
        /// </summary>
        public void MarkAnswerAsHistorical() { IsLatest = false; }

        private VotingAnswer() { }
    }
}
