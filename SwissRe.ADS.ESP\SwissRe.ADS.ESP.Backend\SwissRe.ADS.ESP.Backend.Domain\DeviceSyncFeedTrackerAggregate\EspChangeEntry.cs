﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate
{
    /// <summary>
    /// Represents a change entry for an entity that requires synchronization between the backend and a client device.
    /// Stores the primary key of the target entity and the timestamp of its last update,
    /// allowing the synchronization process to track which entities have pending changes.
    /// </summary>
    public class EspChangeEntry : GuidEntity
    {
        /// <summary>
        /// Primary key of the Entity that we are syncinng (DocumentContainer, MeetingContainer, VotingContainer, etc.).
        /// </summary>
        public Guid EntityId { get; private set; }

        /// <summary>
        /// Last updated on timestamp of the entity.
        /// TODO: Do we really need this value?
        /// </summary>
        public DateTime UpdatedOn { get; private set; } //LastUpdatedOn of the entity from the sync

        /// <summary>
        /// Stringified data object representing the entity.
        /// </summary>
        public string? ValueAsJson { get; private set; } //stringified DTO object 

        /// <summary>
        /// Indicates whether the user has lost access to this entity and it should be deleted on the target device.
        /// </summary>
        public bool UserLostAccess { get; private set; }

        private EspChangeEntry() { }

        /// <summary>
        /// Creates a new instance of SyncEntityBatchData with the specified entity ID and JSON value. Use this when user still has value to the target entity  
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="valueAsJson"></param>
        /// <returns></returns>
        public static EspChangeEntry CreateWithValue(Guid entityId, DateTime entityLastUpdatedOn, string valueAsJson)
        {
            Guard.Against.NullOrEmpty(valueAsJson, nameof(valueAsJson), "ValueAsJson cannot be null or empty.");

            return new EspChangeEntry
            {
                EntityId = entityId,
                UpdatedOn = entityLastUpdatedOn,
                UserLostAccess = false,
                ValueAsJson = valueAsJson
            };
        }

        /// <summary>
        /// Creates a new instance of SyncEntityBatchData with the specified entity ID indicating that the user has lost access to this entity.
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        public static EspChangeEntry CreateUserLostAccess(Guid entityId, DateTime entityLastUpdatedOn)
        {
            return new EspChangeEntry
            {
                EntityId = entityId,
                UpdatedOn = entityLastUpdatedOn,
                UserLostAccess = true,
                ValueAsJson = null
            };
        }

        internal void EntityValueChanged(string newValueAsJson, DateTime entityLastUpdatedOn)
        {
            ValueAsJson = newValueAsJson;
            UserLostAccess = false;
            UpdatedOn = entityLastUpdatedOn;
        }

        internal void UserAccessRemovedToEntity()
        {
            UserLostAccess = true;
            ValueAsJson = null;
            UpdatedOn = DateTime.UtcNow;
        }
    }
}
