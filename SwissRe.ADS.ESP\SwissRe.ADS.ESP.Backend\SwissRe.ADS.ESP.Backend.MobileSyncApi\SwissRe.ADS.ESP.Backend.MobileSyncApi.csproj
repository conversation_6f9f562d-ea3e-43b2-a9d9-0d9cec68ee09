﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>aspnet-SwissRe.ADS.ESP.Backend.MobileSyncApi-e354008f-c63d-4c01-a1ee-a9fb0ee82a78</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.2" NoWarn="NU1605" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.2" NoWarn="NU1605" />
		<PackageReference Include="Microsoft.Identity.Web" Version="3.0.0" />
		<PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="3.0.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
		<PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Application.Common\SwissRe.ADS.ESP.Backend.Application.Common.csproj" />
		<ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Domain\SwissRe.ADS.ESP.Backend.Domain.csproj" />
		<ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Persistence\SwissRe.ADS.ESP.Backend.Persistence.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Endpoints\MeetingsAggregate\Commands\" />
	</ItemGroup>

</Project>
