﻿using SwissRe.ADS.ServiceBus;
using SwissRe.ADS.ServiceBus.MessageConfiguration;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate.ServiceBusMessages
{
    public record MeetingMetadataChangedMessage(Guid MeetingId) : IMessageBody
    {
        public static void ConfigureMessage(MessageConfigBuilder builder)
        {
            builder
                .ToQueue("meeting-metadata-changed");
        }

    }
}
