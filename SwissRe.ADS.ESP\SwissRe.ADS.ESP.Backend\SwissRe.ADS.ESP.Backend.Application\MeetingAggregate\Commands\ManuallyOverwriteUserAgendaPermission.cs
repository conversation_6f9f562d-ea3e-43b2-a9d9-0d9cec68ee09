﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using Microsoft.AspNetCore.Http;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Commands
{

    public class ManuallyOverwriteUserAgendaPermissionEndpoint(UnitOfWork unitOfWork, IAgendaAuthorizationService agendaPermissionService, ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
             builder
                .MapPost("/containers/{containerId}/agendas/{rootAgendaId}/permissions", (Guid containerId, Guid rootAgendaId, AgendaPermissionEnum newPermission, ManuallyOverwriteUserAgendaPermissionEndpoint endpoint) => endpoint.HandleAsync(containerId, rootAgendaId, newPermission))
                .WithSummary("Manually overwrite the current user's agenda permission.")
                .WithDescription("Manually sets the current user's permission for the specified agenda and all its child agendas within a meeting container. Requires manage permission on the agenda.")
                .WithAngularName<MeetingContainerRouteGroup>("ManuallyOverwriteAgendaPermission");

        public async Task HandleAsync(Guid containerId, Guid rootAgendaId, AgendaPermissionEnum newPermission)
        {
            var canManageAgenda = await _agendaPermissionService.CanUserManageAgendaAsync(containerId, rootAgendaId, AgendaTypeEnum.MeetingAgenda);
            if (canManageAgenda == false)
                throw new UnauthorizedAccessException("Current user does not have permission to modify Agenda Permissions");

            var meeting = await _unitOfWork.Repository<MeetingContainer>().GetAsync(containerId, true);
            meeting.OverwriteUserAccessForAgendaAndItsChildren(rootAgendaId, _currentUser.SrUserId, newPermission);

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
