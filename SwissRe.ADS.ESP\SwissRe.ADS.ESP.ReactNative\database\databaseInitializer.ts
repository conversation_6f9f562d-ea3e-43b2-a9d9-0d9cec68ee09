import { SQLiteDatabase } from 'expo-sqlite';
import { ALL_DATABASE_MIGRATIONS } from './migrations';

export class DatabaseInitializer {
  public static async initializeDatabase(db: SQLiteDatabase): Promise<void> {
    const check_if_feature_exists = async (db: SQLiteDatabase, featureName: string): Promise<boolean> => {
      const result = await db.getFirstAsync('SELECT 1 FROM __MigrationsHistory WHERE MigrationName = ?', [featureName]);

      console.log(`Check if feature ${featureName} exists:`, result);
      return result !== null;
    };

    //actual migrations
    try {
      const migrationsTableExists = await db.getFirstAsync("SELECT name FROM sqlite_master WHERE type='table' AND name='__MigrationsHistory';");

      if (migrationsTableExists === null) {
        console.log('Creating migrations table as it does not exist');
        await db.execAsync(`
                    CREATE TABLE __MigrationsHistory (
                        MigrationName NVARCHAR(255) PRIMARY KEY,
                        MigratedOn DATETIME DEFAULT (CURRENT_TIMESTAMP)
                    );
            `);
      }

      for (let iMigration = 0; iMigration < ALL_DATABASE_MIGRATIONS.length; iMigration++) {
        const currentMigration = ALL_DATABASE_MIGRATIONS[iMigration];
        if (await check_if_feature_exists(db, currentMigration.migrationName)) continue;

        //apply migrations in a single transaction
        console.log(`Applying migration ${currentMigration.migrationName}`);

        await db.withExclusiveTransactionAsync(async (tx) => {
          await tx.execSync(currentMigration.sql);
          await tx.runAsync('INSERT INTO __MigrationsHistory (MigrationName) VALUES (?)', currentMigration.migrationName);
        });
      }
    } catch (exception) {
      console.error('Exception occured while trying to create db migrations', exception);
      throw exception;
    }
  }
}
