﻿using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate
{
    /// <summary>
    /// Response DTO representing voting-specific metadata for a user's voting container.
    /// Used to display summary information about a voting session on the home dashboard.
    /// </summary>
    public class GetUserVotingResponse : GetContainerResponseBase
    {
        /// <summary>
        /// Total number of voting questions available in the voting container.
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// Number of questions the user has already voted on.
        /// </summary>
        public int TotalUserVotes { get; set; }

        /// <summary>
        /// Number of questions the user has not yet voted on.
        /// Calculated as the difference between <see cref="TotalQuestions"/> and <see cref="TotalUserVotes"/>.
        /// </summary>
        public int UnvotedQuestions => TotalQuestions - TotalUserVotes;
    }
}
