// src/db/DatabaseClient.ts
import * as SQLite from 'expo-sqlite';
import { DatabaseInitializer } from './databaseInitializer';

type SQLStatement = [string, SQLite.SQLiteBindParams?];

class DatabaseClient {
  private static instance: DatabaseClient | null = null;
  private db: SQLite.SQLiteDatabase;
  private queue: (() => Promise<void>)[] = [];
  private isRunning = false;

  private constructor() {
    this.db = SQLite.openDatabaseSync('espDatabase5.db');
  }

  public static getInstance(): DatabaseClient {
    if (!DatabaseClient.instance) {
      const client = new DatabaseClient();
      DatabaseClient.instance = client;
    }
    return DatabaseClient.instance;
  }

  public async runDatabaseMigrations(): Promise<void> {
    await DatabaseInitializer.initializeDatabase(this.db);
  }

  /** Reads (SELECT) — no queue needed */
  public async getAll<T = any>(sql: string, params: SQLite.SQLiteBindParams = []): Promise<T[]> {
    return this.db.getAllAsync<T>(sql, params);
  }

  public async getFirst<T = any>(sql: string, params: SQLite.SQLiteBindParams = []): Promise<T | null> {
    const row = await this.db.getFirstAsync<T>(sql, params);
    return row ?? null;
  }

  /** Single statement (auto-wrapped in transaction for consistency) */
  public async execute(sql: string, params: SQLite.SQLiteBindParams = []): Promise<SQLite.SQLiteRunResult> {
    const results = await this.runInQueue([[sql, params]]);
    return results[0];
  }

  /** Multiple statements in a single transaction */
  public async executeBatch(statements: SQLStatement[]): Promise<SQLite.SQLiteRunResult[]> {
    return this.runInQueue(statements);
  }

  /** Internal serialized + transactional execution */
  private async runInQueue(statements: SQLStatement[]): Promise<SQLite.SQLiteRunResult[]> {
    return new Promise((resolve, reject) => {
      const task = async () => {
        try {
          const results: SQLite.SQLiteRunResult[] = [];
          await this.db.withExclusiveTransactionAsync(async (tx) => {
            for (const [sql, params = []] of statements) {
              const result = await tx.runAsync(sql, params);
              results.push(result);
            }
          });
          resolve(results);
        } catch (err) {
          reject(err);
        } finally {
          this.isRunning = false;
          this.runNext();
        }
      };

      this.queue.push(task);
      this.runNext();
    });
  }

  private runNext() {
    if (this.isRunning || this.queue.length === 0) return;
    this.isRunning = true;
    const task = this.queue.shift();
    if (task) task();
  }
}

export function getDatabase() {
  return DatabaseClient.getInstance();
}
