﻿using SwissRe.ADS.Ddd.Events.Dispatching.Abstractions;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using SwissRe.ADS.ESP.Backend.Application.Common.PDFTron;
using SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate;
using SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.Events;

namespace SwissRe.ADS.ESP.Backend.Application.FileContentAggregate.Events
{
    public class CreateFileContentOnFileMetadataAddedEventHandler(
        UnitOfWork unitOfWork,
        IPdfThumbnailGeneratorService pdfThumbnailGeneratorService,
        IEspEncryptionService espEncryptionService) : DomainEventHandler<FileMetadataAddedEvent>
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IPdfThumbnailGeneratorService _pdfThumbnailGeneratorService = pdfThumbnailGeneratorService;
        private readonly IEspEncryptionService _espEncryptionService = espEncryptionService;

        public override Task HandleAsync(FileMetadataAddedEvent evt)
        {
            //encrypt the file contents
            var encryptedContents = _espEncryptionService.Encrypt(evt.FileContents);

            //generate thumbnail
            var generateRawThumbnail = _pdfThumbnailGeneratorService.GeneratePngThumbnail(evt.FileContents);

            var contents = FileContent.Create(evt.FileContentId, encryptedContents, generateRawThumbnail);
            _unitOfWork.Repository<FileContent>().Insert(contents);

            return Task.CompletedTask;
        }
    }
}
