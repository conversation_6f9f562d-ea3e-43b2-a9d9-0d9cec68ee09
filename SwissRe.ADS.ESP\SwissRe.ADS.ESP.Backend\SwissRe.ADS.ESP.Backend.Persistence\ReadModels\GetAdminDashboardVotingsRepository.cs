﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.Common.Containers;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using System.Linq.Expressions;

namespace SwissRe.ADS.ESP.Backend.Persistence.ReadModels
{
    public class GetAdminDashboardVotingsRepository(AppDbContext context, ICurrentUser currentUser) : IGetAdminDashboardVotingsRepository
    {
        private readonly AppDbContext _context = context;
        private readonly ICurrentUser _currentUser = currentUser;

        public async Task<List<GetAdminVotingResponse>> GetCurrentAndFutureAdminVotings(CancellationToken cancellationToken = default)
        {
            return await GetAdminDashboardMeetingsByState(true, cancellationToken);
        }

        public async Task<List<GetAdminVotingResponse>> GetPastAdminVotings(CancellationToken cancellationToken = default)
        {
            return await GetAdminDashboardMeetingsByState(false, cancellationToken);
        }

        private async Task<List<GetAdminVotingResponse>> GetAdminDashboardMeetingsByState(bool isCurrent, CancellationToken cancellationToken = default)
        {
            
            var accessibleVotingContainerIds = _context.Set<VotingAgenda>()
                .Where(AgendaPermissionExtensions.HasManageAccessFor<VotingAgenda>(_currentUser.SrUserId))
                .Select(a => a.ParentContainerId)
                .Distinct();

            Expression<Func<VotingContainer, bool>> expression = isCurrent
                ? x => accessibleVotingContainerIds.Contains(x.Id) && (x.State == ContainerStateEnum.Draft || x.State == ContainerStateEnum.Published)
                : x => accessibleVotingContainerIds.Contains(x.Id) && x.State == ContainerStateEnum.PastMeeting;

            // TODO: Refine TotalUsersVoted calculation.
            // Current logic counts users who, for any agenda in the container have write access, 
            // have voted on all questions in the container. Therefore user might have write access to
            // one Agenda and not to another but would be counted only if he voted on all questions
            // for both Agendas.
            var votingQuery = from voting in _context.Set<VotingContainer>().Where(expression)
                              join domain in _context.Set<EspDomain>() on voting.DomainId equals domain.Id
                              join parentDomain in _context.Set<EspDomain>() on domain.ParentId equals parentDomain.Id into parentDomains
                              from parentDomain in parentDomains.DefaultIfEmpty()
                              let allAgendas = _context.Set<VotingAgenda>().Where(a => a.ParentContainerId == voting.Id).ToList()
                              let usersWithVotingRights = allAgendas
                                  .SelectMany(a => a.Permissions
                                      .Where(p => (p.CurrentPermission & AgendaPermissionEnum.Write) > 0)
                                      .Select(p => new { AgendaId = a.Id, p.UserId }))
                                  .ToList()
                              let usersVotedAll = (
                                  from user in usersWithVotingRights.Select(x => x.UserId).Distinct()
                                  let agendasWithWrite = usersWithVotingRights.Where(x => x.UserId == user).Select(x => x.AgendaId).Distinct().ToList()
                                  where agendasWithWrite.All(agendaId =>
                                      _context.Set<VotingQuestion>().Count(q => q.AgendaId == agendaId) > 0 &&
                                      _context.Set<VotingQuestion>().Where(q => q.AgendaId == agendaId)
                                          .All(q =>
                                              _context.Set<VotingAnswer>().Any(a =>
                                                  a.IsLatest &&
                                                  a.VotingQuestionId == q.Id &&
                                                  ((a.OnBehalfOfUserId ?? a.CreatedByUserId) == user)
                                              )
                                          )
                                  ) && agendasWithWrite.Any()
                                  select user
                              ).ToList()
                              let allQuestionIds = _context.Set<VotingQuestion>()
                                  .Where(q => allAgendas.Select(a => a.Id).Contains(q.AgendaId))
                                  .Select(q => q.Id)
                                  .ToList()
                              let totalQuestions = allQuestionIds.Count()
                              select new GetAdminVotingResponse
                              {
                                  ContainerType = ContainerTypeEnum.VotingContainer,
                                  ContainerId = voting.Id,
                                  Title = voting.Name,
                                  TotalUsersWithVotingRights = usersWithVotingRights.Select(x => x.UserId).Distinct().Count(),
                                  TotalUsersVoted = usersVotedAll.Count(),
                                  State = voting.State,
                                  Domain = new GetDomainResponse
                                  {
                                      Id = domain.Id,
                                      Name = domain.Name,
                                      Parent = parentDomain != null
                                          ? new GetDomainResponse
                                          {
                                              Id = parentDomain.Id,
                                              Name = parentDomain.Name
                                          }
                                          : null
                                  }
                              };

            return await votingQuery.AsNoTracking().ToListAsync(cancellationToken);
        }
    }
}
