﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class VotingFiles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "VotingQuestionSupportingDocumentFileMetadatas",
                schema: "espv2");

            migrationBuilder.CreateTable(
                name: "VotingFileMetadatas",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    VotingAgendaId = table.Column<Guid>(type: "uuid", nullable: false),
                    VotingAgendaId1 = table.Column<Guid>(type: "uuid", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    FileContentId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    DisplayFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileExtension = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Supplementary = table.Column<bool>(type: "boolean", nullable: false),
                    FileGroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    VersionNumber = table.Column<int>(type: "integer", nullable: false),
                    IsLatest = table.Column<bool>(type: "boolean", nullable: false),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    Size = table.Column<int>(type: "integer", nullable: false),
                    NumberOfPages = table.Column<int>(type: "integer", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LastModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadStatuses = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingFileMetadatas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingFileMetadatas_FileContents_FileContentId",
                        column: x => x.FileContentId,
                        principalSchema: "espv2",
                        principalTable: "FileContents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingFileMetadatas_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingFileMetadatas_Users_LastModifiedByUserId",
                        column: x => x.LastModifiedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId1",
                        column: x => x.VotingAgendaId1,
                        principalSchema: "espv2",
                        principalTable: "VotingsAgenda",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingFileMetadatas_VotingsContainer_VotingAgendaId",
                        column: x => x.VotingAgendaId,
                        principalSchema: "espv2",
                        principalTable: "VotingsContainer",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_VotingFileMetadatas_CreatedByUserId",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingFileMetadatas_FileContentId",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "FileContentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VotingFileMetadatas_LastModifiedByUserId",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "LastModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingFileMetadatas_VotingAgendaId",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "VotingAgendaId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingFileMetadatas_VotingAgendaId1",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "VotingAgendaId1");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "VotingFileMetadatas",
                schema: "espv2");

            migrationBuilder.CreateTable(
                name: "VotingQuestionSupportingDocumentFileMetadatas",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DisplayFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileContentId = table.Column<Guid>(type: "uuid", nullable: false),
                    FileExtension = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    FileGroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsLatest = table.Column<bool>(type: "boolean", nullable: false),
                    LastModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NumberOfPages = table.Column<int>(type: "integer", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    OriginalFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Size = table.Column<int>(type: "integer", nullable: false),
                    Supplementary = table.Column<bool>(type: "boolean", nullable: false),
                    VersionNumber = table.Column<int>(type: "integer", nullable: false),
                    VotingQuestionId = table.Column<Guid>(type: "uuid", nullable: false),
                    VotingQuestionId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    ReadStatuses = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingQuestionSupportingDocumentFileMetadatas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_FileContents_~",
                        column: x => x.FileContentId,
                        principalSchema: "espv2",
                        principalTable: "FileContents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_Users_Created~",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_Users_LastMod~",
                        column: x => x.LastModifiedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_VotingQuestio~",
                        column: x => x.VotingQuestionId,
                        principalSchema: "espv2",
                        principalTable: "VotingQuestions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1",
                        column: x => x.VotingQuestionId1,
                        principalSchema: "espv2",
                        principalTable: "VotingQuestions",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_CreatedByUser~",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_FileContentId",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "FileContentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_LastModifiedB~",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "LastModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "VotingQuestionId1");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_VotingQuestio~",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "VotingQuestionId");
        }
    }
}
