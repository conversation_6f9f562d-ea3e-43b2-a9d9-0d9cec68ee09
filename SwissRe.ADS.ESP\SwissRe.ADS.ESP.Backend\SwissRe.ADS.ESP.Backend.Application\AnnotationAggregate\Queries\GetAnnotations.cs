﻿using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using Microsoft.AspNetCore.Builder;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.DomainServices;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using Microsoft.AspNetCore.Http;

namespace SwissRe.ADS.ESP.Backend.Application.AnnotationAggregate.Queries
{
    public record GetAnnotationResult(Guid id, Guid annotationUniqueId, string annotationXml);

    public class GetAnnotationsEndpoint(UnitOfWork unitOfWork,
        IAgendaInfoProviderService agendaInfoProviderService,
        IAgendaAuthorizationService agendaPermissionService,
        IEspEncryptionService espEncryptionService,
        ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaInfoProviderService _agendaInfoProviderService = agendaInfoProviderService;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly IEspEncryptionService _espEncryptionService = espEncryptionService;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<AnnotationRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/files/{fileMetadataId}/annotations", (GetAnnotationsEndpoint endpoint, Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType) => endpoint.HandleAsync(fileMetadataId, fileMetadataType))
                .WithSummary("Get all annotations for a file.")
                .WithDescription("Returns all annotations created by the current user for the specified file (meeting or document). Requires permission to view or add annotations to the file.")
                .WithAngularName<AnnotationRouteGroup>("GetAnnotations");
        }

        public async Task<List<GetAnnotationResult>> HandleAsync(Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType)
        {
            var agendaInfo = await _agendaInfoProviderService.GetAgendaInfoAsync(fileMetadataId, fileMetadataType);
            AgendaTypeEnum agendaType = fileMetadataType switch
            {
                FileMetadataTypeEnum.Documents => AgendaTypeEnum.DocumentAgenda,
                FileMetadataTypeEnum.Meetings => AgendaTypeEnum.MeetingAgenda,
                _ => throw new ArgumentException("Invalid file metadata type.", nameof(fileMetadataType))
            };

            var canAddAnnotation = await _agendaPermissionService.CanUserAddAnnotationAsync(agendaInfo.ParentContainerId, agendaInfo.AgendaId, agendaType);
            if (canAddAnnotation == false)
                throw new UnauthorizedAccessException("Current user does not have permission to add or view annotations to this file.");

            var retrievedAnnotations = await _unitOfWork.Repository<Annotation>()
                .GetAllAsync(annotations =>
                            annotations.Where(annotation => annotation.FileMetadataId == fileMetadataId && annotation.CreatedByUserId == _currentUser.SrUserId)
                                        .Select(m =>
                                                new { m.Id, m.AnnotationUniqueId, m.EncryptedAnnotationXML }));

            return retrievedAnnotations.Select(m => new GetAnnotationResult(m.Id, m.AnnotationUniqueId, _espEncryptionService.Decrypt(m.EncryptedAnnotationXML))).ToList();
        }
    }
}
