﻿using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DomainAggregate
{
    public class DomainPermissionService
    {
        public bool CanUserCreateNewContainer(EspDomain domain, IEnumerable<EspRole> currentUserRoles)
        {
            return domain.DomainRoles
                .Any(role => currentUserRoles.Any(userRole => userRole.Id == role.Id && role.RoleType == DomainRoleTypeEnum.Admin));
        }
    }
}