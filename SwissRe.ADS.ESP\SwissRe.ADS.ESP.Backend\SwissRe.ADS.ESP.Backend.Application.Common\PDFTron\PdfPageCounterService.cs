﻿using pdftron.PDF;

namespace SwissRe.ADS.ESP.Backend.Application.Common.PDFTron
{
    public interface IPdfPageCounterService
    {
        int GetTotalPages(Stream pdfStream);
    }

    public class PdfPageCounterService : IPdfPageCounterService
    {
        public int GetTotalPages(Stream pdfStream)
        {
            using (PDFDoc doc = new PDFDoc(pdfStream))
            {
                doc.InitSecurityHandler();
                return doc.GetPageCount();
            }
        }
    }
}
