import { Directive, TemplateRef, ViewContainerRef, inject, input, effect } from '@angular/core';
import { AppMediaStateName } from './AppMediaState';
import { AppMediaStatesProvider } from './app-media-states-provider';
import { AppMediaStateObserver } from './app-media-state-observer.service';
import { toSignal } from '@angular/core/rxjs-interop';

/**
 * Like *ngIf but based on AppMediaStateName (desktop, tablet, mobile...).
 * Adds or removes an element based on media state.
 *
 * Directive accepts an object with two properties:
 *    - from - optional inclusive state name
 *    - until - optional exclusive state name
 *
 * Examples:
 *    - <div *mediaIf="{from: 'desktop'}"> - desktop or larger
 *    - <div *mediaIf="{until: 'desktop'}"> - smaller than desktop
 *    - <div *mediaIf="{from: 'mobile', until: 'desktop'}"> - mobile or larger and smaller than desktop
 */
@Directive({
  selector: '[mediaIf]',
  standalone: true
})
export class MediaIfDirective {
  readonly input = input.required<MediaIfDirectiveInput>({ alias: 'mediaIf' });

  private readonly templateRef = inject(TemplateRef<unknown>);
  private readonly viewContainer = inject(ViewContainerRef);
  private readonly appMediaStatesProvider = inject(AppMediaStatesProvider);
  private readonly appMediaState = toSignal(inject(AppMediaStateObserver).state$, { requireSync: true });
  private hasView = false;

  constructor() {
    effect(() => {
      const { from, until } = this.input();
      const fromIndex = (from && this.appMediaStatesProvider.get(from)?.index) || 0;
      const untilIndex = (until && this.appMediaStatesProvider.get(until)?.index) || this.appMediaStatesProvider.largest.index + 1;

      const show = this.appMediaState().index >= fromIndex && this.appMediaState().index < untilIndex;

      if (show && !this.hasView) {
        this.viewContainer.createEmbeddedView(this.templateRef);
        this.hasView = true;
      } else if (!show && this.hasView) {
        this.viewContainer.clear();
        this.hasView = false;
      }
    });
  }
}

export interface MediaIfDirectiveInput {
  from?: AppMediaStateName;
  until?: AppMediaStateName;
}
