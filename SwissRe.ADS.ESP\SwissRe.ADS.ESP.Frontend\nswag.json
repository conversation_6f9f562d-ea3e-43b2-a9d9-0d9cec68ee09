{"runtime": "Net80", "documentGenerator": {"fromDocument": {"url": "https://localhost:5001/swagger/v1/swagger.json", "output": null, "newLineBehavior": "Auto"}}, "codeGenerators": {"openApiToTypeScriptClient": {"className": "{controller}Client", "moduleName": "", "namespace": "", "typeScriptVersion": 5.3, "template": "Angular", "promiseType": "Promise", "httpClass": "HttpClient", "withCredentials": true, "useSingletonProvider": true, "injectionTokenType": "InjectionToken", "rxJsVersion": 7, "dateTimeType": "Date", "nullValue": "null", "generateClientClasses": true, "generateClientInterfaces": false, "generateOptionalParameters": true, "exportTypes": true, "wrapDtoExceptions": false, "exceptionClass": "ApiException", "clientBaseClass": null, "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "protectedMethods": [], "configurationClass": null, "useTransformOptionsMethod": false, "useTransformResultMethod": false, "generateDtoTypes": true, "operationGenerationMode": "MultipleClientsFromOperationId", "markOptionalProperties": true, "generateCloneMethod": false, "typeStyle": "class", "enumStyle": "Enum", "useLeafType": false, "classTypes": [], "extendedClasses": [], "extensionCode": null, "generateDefaultValues": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateTypeCheckFunctions": false, "generateConstructorInterface": true, "convertConstructorInterfaceData": false, "importRequiredTypes": true, "useGetBaseUrlMethod": false, "baseUrlTokenName": "API_BASE_URL", "queryNullValue": "", "useAbortSignal": false, "inlineNamedDictionaries": false, "inlineNamedAny": false, "includeHttpContext": true, "templateDirectory": null, "serviceHost": null, "serviceSchemes": null, "output": "src\\app\\shared\\apiServices.ts", "newLineBehavior": "Auto"}}}