import { useEspAppTheme } from '@/hooks/useEspTheme';
import { Stack } from 'expo-router';

export default function MeetingsLayout() {
  const theme = useEspAppTheme();
  return (
    <Stack screenOptions={{ headerShown: true }}>
      <Stack.Screen
        name="index"
        options={{
          title: 'Meetings',
          headerTitleAlign: 'center',
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerShadowVisible: false,
          headerTintColor: '#333',
          headerLargeTitle: true,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: 'Meeting Details',
        }}
      />
    </Stack>
  );
}
