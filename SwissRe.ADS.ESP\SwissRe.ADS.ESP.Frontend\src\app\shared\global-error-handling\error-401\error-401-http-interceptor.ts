import { inject } from '@angular/core';
import { HttpErrorResponse, HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { EMPTY, catchError, switchMap, tap } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { Error401Component } from './error-401.component';

export const error401HttpInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {
  const matDialog = inject(MatDialog);

  return next(req).pipe(
    catchError(err => {
      if (err instanceof HttpErrorResponse && err.status === 401) {
        return matDialog
          .open(Error401Component)
          .afterClosed()
          .pipe(
            tap(() => window.location.reload()),
            switchMap(() => EMPTY)
          );
      }

      throw err;
    })
  );
};
