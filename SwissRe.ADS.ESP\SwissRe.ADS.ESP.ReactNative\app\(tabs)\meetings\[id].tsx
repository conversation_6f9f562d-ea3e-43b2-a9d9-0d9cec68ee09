// app/meetings/[id].tsx
import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams } from 'expo-router';
import { useMemo } from 'react';
import { ActivityIndicator, Text, View, StyleSheet } from 'react-native';
import AgendaSidebar from '@/components/AgendaSidebarComponent/AgendaSidebar';
import { GetMeetingAgendaResponse } from '@/api/apiServices';
import { flattenAgendas } from '@/utils/FlattenAgendas';
import { useMeetingByIdSync } from '@/syncJobs/hooks/useMeetingByIdSync';
import { useHideTabBar } from '@/hooks/useHideTabBar';
import { useAgendaHighlight } from './hooks/useAgendaHighlight';
import { useMeetingById } from './hooks/useMeetingById';
import { AgendaList } from './components/AgendaList';

export default function MeetingDetail() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const queryClient = useQueryClient();

  const { meeting, isLoading, error } = useMeetingById(id);
  const agendasTree: GetMeetingAgendaResponse[] = meeting?.agendas ?? [];
  const flatAgendas = useMemo(() => flattenAgendas(agendasTree), [agendasTree]);

  useMeetingByIdSync(id, meeting, queryClient);
  useHideTabBar();
  const { scrollViewRef, currentAgendaId, handleScroll, handleSelectAgenda, handleAgendaLayout } = useAgendaHighlight(flatAgendas);

  if (isLoading) return <ActivityIndicator size="large" />;
  if (error) return <Text>Error loading meeting</Text>;
  if (!meeting) return <Text>No meeting found</Text>;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <AgendaList
          meetingTitle={meeting.name}
          meetingDescription={meeting.description ?? ''}
          agendas={flatAgendas}
          scrollViewRef={scrollViewRef}
          onScroll={handleScroll}
          onAgendaLayout={handleAgendaLayout}
        />
      </View>

      <AgendaSidebar agendas={meeting.agendas} onSelectAgenda={handleSelectAgenda} activeAgendaId={currentAgendaId} />
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flex: 1,
  },
  content: {
    flex: 1,
  },
});
