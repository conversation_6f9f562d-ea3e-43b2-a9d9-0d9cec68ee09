﻿using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.MeetingsAggregate.Helpers
{
    public interface IMeetingSyncDataService
    {
        Task<List<GetMeetingDetailResponse>> GetAllUserMeetings();
    }

    public class MeetingSyncDataService(UnitOfWork unitOfWork, ICurrentUser currentUser) : IMeetingSyncDataService
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;

        public async Task<List<GetMeetingDetailResponse>> GetAllUserMeetings()
        {
            return await _unitOfWork.Repository<MeetingContainer>()
                                        .GetAllAsync(meetings => meetings
                                                                .Where(meeting => meeting.Agendas.AsQueryable().Any(AgendaPermissionExtensions.HasReadAccessFor<MeetingAgenda>(_currentUser.SrUserId)))
                                        .Select(MeetingContainerEntityFrameworkExtensions.MeetingContainerProjection(_currentUser.SrUserId)));
        }
    }
}
