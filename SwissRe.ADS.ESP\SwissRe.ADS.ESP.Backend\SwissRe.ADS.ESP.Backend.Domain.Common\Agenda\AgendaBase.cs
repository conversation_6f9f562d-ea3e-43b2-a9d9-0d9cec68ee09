﻿using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.Common.Agenda
{
    public abstract class AgendaBase : GuidEntity, IAgendaBase
    {
        private List<AgendaPermission> _permissions = new();

        public IReadOnlyList<AgendaPermission> Permissions => _permissions;


        /// <summary>
        /// Adds or updates the agenda permission for the specified user.
        /// If the user already has a permission entry, it will be updated to the provided permission if different.
        /// If the user does not have a permission entry, a new one will be created.
        /// </summary>
        /// <param name="userId">The unique identifier of the user whose permission is being set.</param>
        /// <param name="permission">The permission to assign to the user.</param>
        /// <returns>
        /// <c>true</c> if the permission was added or updated (i.e., a change occurred); 
        /// <c>false</c> if the permission was already set to the specified value and no change was made.
        /// </returns>
        internal bool UpsertUserPermission(string userId, AgendaPermissionEnum permission)
        {
            var existing = _permissions.FirstOrDefault(x => x.UserId == userId);
            if (existing != null)
                return existing.UpdateInheritedPermissionIfChangedFromSync(permission);
            else
            {
                _permissions.Add(AgendaPermission.Create(userId, permission));
                return true;
            }
        }

        /// <summary>
        /// User has lost access to the domain associated with Container, therefore all access is now lost
        /// </summary>
        /// <param name="userId"></param>
        internal void RemoveAllUserAccess(string userId)
        {
            var existing = _permissions.FirstOrDefault(x => x.UserId == userId);
            if (existing != null)
                _permissions.Remove(existing);
        }

        /// <summary>
        /// Overwrite user's access to the agenda with a manually specified role. 
        /// All other roles will be removed
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="permission"></param>
        internal void ManuallyOverwriteUserAccess(string userId, AgendaPermissionEnum permission)
        {
            var existing = _permissions.FirstOrDefault(x => x.UserId == userId);
            if (existing != null)
                existing.ManuallyOverwritePermission(permission);
        }


    }
}
