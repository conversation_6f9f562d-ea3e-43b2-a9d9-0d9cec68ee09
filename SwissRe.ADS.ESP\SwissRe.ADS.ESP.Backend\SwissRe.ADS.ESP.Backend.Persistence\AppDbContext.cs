﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Options;
using Npgsql;
using System.Diagnostics;

namespace SwissRe.ADS.ESP.Backend.Persistence
{
    public class AppDbContext(IOptions<PersistenceOptions> options, NpgsqlConnection npgsqlConnection) : DbContext
    {
        private const string _defaultSchema = "espv2";
        private readonly PersistenceOptions _options = options.Value;
        private readonly NpgsqlConnection _npgsqlConnection = npgsqlConnection;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.HasDefaultSchema(_defaultSchema);

            _options.AssembliesWithConfigurations.ForEach(assembly => modelBuilder.ApplyConfigurationsFromAssembly(assembly));
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);

            optionsBuilder.UseNpgsql(_npgsqlConnection, npgsql =>
            {
                npgsql.MigrationsAssembly(typeof(AppDbContext).Assembly.FullName);
                npgsql.MigrationsHistoryTable(HistoryRepository.DefaultTableName, _defaultSchema);
                npgsql.CommandTimeout(120);
                npgsql.EnableRetryOnFailure(3);
            });

            if (_options.LogToConsole)
            {
                optionsBuilder.LogTo(message => Debug.WriteLine(message));
                optionsBuilder.EnableSensitiveDataLogging();
            }
        }
    }
}
