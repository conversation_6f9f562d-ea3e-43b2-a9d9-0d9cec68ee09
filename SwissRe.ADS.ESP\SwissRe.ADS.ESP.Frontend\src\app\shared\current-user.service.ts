import { Injectable, inject } from '@angular/core';
import { Observable, map, shareReplay } from 'rxjs';
import { CurrentUserClient, IGetCurrentUserResponse } from './apiServices';
import { useHttpContext } from './http-context';
import { withGlobalLoader } from './global-loader/http-middleware';

@Injectable({
  providedIn: 'root'
})
export class CurrentUserService {
  readonly currentUser$: Observable<IGetCurrentUserResponse> = inject(CurrentUserClient)
    .get(useHttpContext(withGlobalLoader()))
    .pipe(
      map(currentUser => ({
        ...currentUser
      })),
      shareReplay(1)
    );
}
