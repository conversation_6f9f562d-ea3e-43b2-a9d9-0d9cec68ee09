# Docs

[See the docs in the repo](https://dev.azure.com/swissre/APPDEVSVC-ADS/_git/SwissRe.ADS.Templates?path=/templates/BackendSkeleton/docs/index.md&anchor=creating-an-endpoint)

# Entity Framework

- Install tools
dotnet tool install dotnet-ef --tool-path C:\SRDev\dotnet\tools\

- Update tools
dotnet tool update dotnet-ef --tool-path C:\SRDev\dotnet\tools\

- Add migration
dotnet ef migrations add InitialMigrations --startup-project SwissRe.ADS.ESP.Backend.Api --project SwissRe.ADS.ESP.Backend.Persistence

- Create migration script
dotnet ef migrations script -i --startup-project SwissRe.ADS.ESP.Backend.Api --project SwissRe.ADS.ESP.Backend.Persistence -o SwissRe.ADS.ESP.Backend.Persistence\Migrations\migrationScript.sql

- Update DB
dotnet ef database update --startup-project SwissRe.ADS.ESP.Backend.Api --project SwissRe.ADS.ESP.Backend.Persistence
