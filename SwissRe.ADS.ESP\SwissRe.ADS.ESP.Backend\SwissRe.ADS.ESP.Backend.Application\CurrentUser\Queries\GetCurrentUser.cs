﻿using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Builder;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;

namespace SwissRe.ADS.ESP.Backend.Application.CurrentUser.Queries
{
    public record GetCurrentUserResponse(string FullName);

    public class GetCurrentUserEndpoint(ICurrentUser currentUser) : IEndpoint
    {
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<CurrentUserRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapGet("Get", (GetCurrentUserEndpoint endpoint) => endpoint.Handle())
                .WithAngularName<CurrentUserRouteGroup>("Get");

        public GetCurrentUserResponse Handle() =>
            new(_currentUser.FullName);
    }
}
