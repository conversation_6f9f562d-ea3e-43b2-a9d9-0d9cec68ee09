﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.Common.Containers;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using System.Linq.Expressions;

namespace SwissRe.ADS.ESP.Backend.Persistence.ReadModels
{
    public class GetAdminDashboardMeetingsRepository(AppDbContext context, ICurrentUser currentUser) : IGetAdminDashboardMeetingsRepository
    {
        private readonly AppDbContext _context = context;
        private readonly ICurrentUser _currentUser = currentUser;

        public async Task<List<GetAdminMeetingResponse>> GetCurrentAndFutureAdminMeetings(CancellationToken cancellationToken = default)
        {
            return await GetAdminDashboardMeetingsByState(true, cancellationToken);
        }

        public async Task<List<GetAdminMeetingResponse>> GetPastAdminMeetings(CancellationToken cancellationToken = default)
        {
            return await GetAdminDashboardMeetingsByState(false, cancellationToken);
        }

        private async Task<List<GetAdminMeetingResponse>> GetAdminDashboardMeetingsByState(bool isCurrent, CancellationToken cancellationToken = default)
        {

            var accessibleContainerIds = _context.Set<MeetingAgenda>()
                          .Where(AgendaPermissionExtensions.HasManageAccessFor<MeetingAgenda>(_currentUser.SrUserId))
                          .Select(a => a.ParentContainerId)
                          .Distinct();

            Expression<Func<MeetingContainer, bool>> expression = isCurrent
                ? x => accessibleContainerIds.Contains(x.Id) && (x.State == ContainerStateEnum.Draft || x.State == ContainerStateEnum.Published)
                : x => accessibleContainerIds.Contains(x.Id) && x.State == ContainerStateEnum.PastMeeting;

            var meetingQuery = from meeting in _context.Set<MeetingContainer>().Where(expression)
                               join domain in _context.Set<EspDomain>() on meeting.DomainId equals domain.Id
                               join parentDomain in _context.Set<EspDomain>() on domain.ParentId equals parentDomain.Id into parentDomains
                               from parentDomain in parentDomains.DefaultIfEmpty()
                               select new GetAdminMeetingResponse
                               {
                                   ContainerType = ContainerTypeEnum.MeetingContainer,
                                   ContainerId = meeting.Id,
                                   Title = meeting.Name,
                                   Location = meeting.Location,
                                   StartTime = meeting.StartTime,
                                   State = meeting.State,
                                   Domain = new GetDomainResponse
                                   {
                                       Id = domain.Id,
                                       Name = domain.Name,
                                       Parent = parentDomain != null
                                           ? new GetDomainResponse
                                           {
                                               Id = parentDomain.Id,
                                               Name = parentDomain.Name
                                           }
                                           : null
                                   }
                               };

            return await meetingQuery.AsNoTracking().ToListAsync(cancellationToken);
        }
    }
}
