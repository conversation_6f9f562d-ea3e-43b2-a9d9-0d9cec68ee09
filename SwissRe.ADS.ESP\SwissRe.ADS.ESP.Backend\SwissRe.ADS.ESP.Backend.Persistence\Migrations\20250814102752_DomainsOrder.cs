﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class DomainsOrder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Order",
                schema: "espv2",
                table: "Domains",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Order",
                schema: "espv2",
                table: "Domains");
        }
    }
}
