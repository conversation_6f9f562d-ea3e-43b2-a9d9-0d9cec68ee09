﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SwissRe.ADS.ESP.Backend.Application.Common;
using Npgsql;
using System.Reflection;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using Microsoft.Extensions.Options;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Persistence.ReadModels;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.VotingsAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence
{
    public static class Registrations
    {
        public static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration configuration, Assembly? extraEntityConfigurationsAssembly)
        {
            ConfigureOptions(services, configuration, extraEntityConfigurationsAssembly);
            var connectionString = GetConnectionString(configuration);

            services.AddScoped(_ => new SqlConnection(connectionString).WithCredentials());
            services.AddDbContext<AppDbContext>();
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            services.AddScoped<ISqlQueryService, SqlQueryService>();
            services.AddScoped<IDbTransaction, DbTransaction>();
            services.AddScoped<IEventsContainerProvider, EventsContainerProvider>();
            services.AddScoped<IDomainReadModelRepository, DomainReadModelRepository>();
            services.AddScoped<IVotingReadModelRepository, VotingReadModelRepository>();
            services.AddScoped<IGetUserDashboardMeetingsRepository, GetUserDashboardMeetingsRepository>();
            services.AddScoped<IGetUserDashboardVotingsRepository, GetUserDashboardVotingsRepository>();
            services.AddScoped<IGetAdminDashboardMeetingsRepository, GetAdminDashboardMeetingsRepository>();
            services.AddScoped<IGetAdminDashboardVotingsRepository, GetAdminDashboardVotingsRepository>();

            services.AddScoped(provider =>
            {
                var options = provider.GetRequiredService<IOptions<PersistenceOptions>>();
                return new NpgsqlConnection(options.Value.ConnectionString);
            });

            services.AddHealthChecks().AddSqlServer(connectionString, configure: connection => connection.WithCredentials());
            //            var sp = services.BuildServiceProvider();
            //            var options = sp.GetRequiredService<IOptions<PostgreSqlOptions>>();

            return services;
        }

        private static void ConfigureOptions(IServiceCollection services, IConfiguration configuration, Assembly? extraEntityConfigurationsAssembly)
        {
            services.Configure<PersistenceOptions>(configuration.GetRequiredSection(PersistenceOptions.SectionName));

            services.Configure<PersistenceOptions>(options =>
            {
                options.AssembliesWithConfigurations.Add(typeof(AppDbContext).Assembly);

                if (extraEntityConfigurationsAssembly != null)
                {
                    options.AssembliesWithConfigurations.Add(extraEntityConfigurationsAssembly);
                }
            });
        }

        private static string GetConnectionString(IConfiguration configuration)
        {
            var connectionStringKey = $"{PersistenceOptions.SectionName}:{nameof(PersistenceOptions.ConnectionString)}";

            return configuration.GetValue<string>(connectionStringKey)?.Trim() is { Length: > 0 } connectionString ?
                connectionString :
                throw new InvalidOperationException($"'{connectionStringKey}' is missing in app settings.");
        }
    }
}
