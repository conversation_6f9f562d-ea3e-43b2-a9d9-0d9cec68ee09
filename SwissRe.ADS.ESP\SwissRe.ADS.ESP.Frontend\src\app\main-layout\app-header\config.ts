import { Injectable } from '@angular/core';
import { shareReplay } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { AppHeader } from './model';
import { metadata } from '../../shared/metadata';

/**
 * Setup your header model here.
 *
 * Creates and provides the app header model as an observable.
 */
@Injectable({
  providedIn: 'root'
})
export class AppHeaderConfig {
  readonly appHeader$ = this.create().pipe(shareReplay(1));

  private create(): Observable<AppHeader> {

    // READ ME!
    // ---------
    // This sample implementation of this method just returns a static app header (the app header model does not change in time). 
    // But note that this method returns an observable so you can make any part of your app header dynamic.
    //
    // I.e. you can inject currentUserService into this class and base your observable on this.currentUserService.currentUser$ like so:
    //
    // return this.currentUserService.currentUser$.pipe(map(currentUser => {
    //   const appHeader: AppHeader = {
    //     ...
    //     mainActions: [
    //       {
    //         ...
    //         visible: currentUser.isAdmin    <<< This shows/hides this main action based on whether the current user is admin
    //       }
    //     ]
    //   };
    //   return appHeader;
    // }));

    const appHeader: AppHeader = {
      appName: 'My app name',
      environmentName: metadata.environmentName,
      appVersion: metadata.appVersion,
      environment: metadata.environment,
      mainActions: [ // optional
        {
          key: 'sample-action', // Unique constant identifier of the main action among all main actions.
          icon: 'android',
          title: 'Sample action',
          enabled: true, // optional
          visible: true, // optional
          action: () => alert('Sample action clicked!')
        }
      ],
      menuItems: [ // optional
        {
          key: 'sample-menu-item', // Unique constant identifier of the menu item among all menu items.
          icon: 'android',
          title: 'Sample menu item',
          enabled: true, // optional
          visible: true, // optional
          action: () => alert('Sample menu item clicked!')
        }
      ]
    };

    return of(appHeader);
  }
}
