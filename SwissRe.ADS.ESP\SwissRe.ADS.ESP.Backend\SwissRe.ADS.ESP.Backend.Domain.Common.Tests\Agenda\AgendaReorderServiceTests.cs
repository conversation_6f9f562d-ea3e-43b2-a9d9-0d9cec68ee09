﻿using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Domain.Common.Tests.Agenda
{
    [TestFixture]
    public class AgendaReorderServiceTests
    {
        private class TestAgenda : GuidEntity
        {
            public int Order { get; set; }
        }

        [Test]
        public void ReorderAgendaItems_ReordersSuccessfully()
        {
            // Arrange
            var agenda1 = new TestAgenda();
            var agenda2 = new TestAgenda();
            var agenda3 = new TestAgenda();
            var agendas = new List<TestAgenda> { agenda1, agenda2, agenda3 };
            var orderedIds = new List<Guid> { agenda2.Id, agenda3.Id, agenda1.Id };

            // Act
            AgendaReorderService.ReorderAgendaItems(
                agendas,
                orderedIds,
                (agenda, order) => agenda.Order = order
            );

            // Assert
            Assert.That(agenda2.Order, Is.EqualTo(0));
            Assert.That(agenda3.Order, Is.EqualTo(1));
            Assert.That(agenda1.Order, Is.EqualTo(2));
        }

        [Test]
        public void ReorderAgendaItems_ThrowsArgumentNullException_WhenOrderedAgendaIdsIsNull()
        {
            var agendas = new List<TestAgenda> { new TestAgenda() };
            Assert.Throws<ArgumentNullException>(() =>
                AgendaReorderService.ReorderAgendaItems(
                    agendas,
                    null,
                    (agenda, order) => agenda.Order = order
                )
            );
        }

        [Test]
        public void ReorderAgendaItems_ThrowsArgumentException_WhenCountsDoNotMatch()
        {
            var agendas = new List<TestAgenda> { new TestAgenda() };
            var orderedIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };
            Assert.Throws<ArgumentException>(() =>
                AgendaReorderService.ReorderAgendaItems(
                    agendas,
                    orderedIds,
                    (agenda, order) => agenda.Order = order
                )
            );
        }

        [Test]
        public void ReorderAgendaItems_ThrowsArgumentException_WhenAgendaIdWasNotFound()
        {
            var agendas = new List<TestAgenda> { new TestAgenda() };
            var orderedIds = new List<Guid> { Guid.NewGuid() };
            Assert.Throws<ArgumentException>(() =>
                AgendaReorderService.ReorderAgendaItems(
                    agendas,
                    orderedIds,
                    (agenda, order) => agenda.Order = order
                )
            );
        }
    }
}