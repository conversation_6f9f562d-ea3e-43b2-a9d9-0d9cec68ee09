﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate
{
    public class DocumentAgenda : AgendaBase
    {
        private List<DocumentFileMetadata> _files = [];

        /// <summary>
        /// Foreign Key reference to MeetingContainer / DocumentContainer / VotingContainer
        /// </summary>
        public Guid ParentContainerId { get; private set; }

        public string Name { get; private set; } = null!;
        public string? Description { get; private set; }
        public int Order { get; private set; }

        public IReadOnlyCollection<DocumentFileMetadata> Files => _files;

        private DocumentAgenda() { }

        public static DocumentAgenda Create(Guid parentContainerId, string name, string? description = null)
        {
            return new DocumentAgenda()
            {
                ParentContainerId = parentContainerId,
                Name = name,
                Description = description,
                Order = 0
            };
        }

        public void AddFile()
        {
            throw new NotImplementedException();
            //var file = FileMetadata.CreateForDocumentAgenda(Id, Guid.NewGuid(), 1, "TestDocumentDocumentAgenda", ".pdf", 1024, 1, 0, "user123", false);
            //_files.Add(file);
        }

        public void SetOrder(int order)
        {
            Guard.Against.Negative(order, nameof(order), "Order cannot be less than zero.");
            Order = order;
        }
    }
}
