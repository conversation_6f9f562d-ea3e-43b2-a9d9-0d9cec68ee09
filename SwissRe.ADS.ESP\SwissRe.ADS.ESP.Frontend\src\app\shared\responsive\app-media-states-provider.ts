import { Injectable } from '@angular/core';
import { APP_MEDIA_STATE_NAMES, AppMediaState, AppMediaStateName } from './AppMediaState';

/**
 * Extracts media states from css and provides them to typescript.
 */
@Injectable({
  providedIn: 'root'
})
export class AppMediaStatesProvider {
  readonly states = this.getStatesFromCss();
  private readonly statesByName = Object.fromEntries(this.states.map(state => [state.name, state]));

  get largest(): AppMediaState {
    return this.states[this.states.length - 1];
  }

  getLarger(state: AppMediaState): AppMediaState | undefined {
    return this.states[state.index + 1];
  }

  get(stateName: AppMediaStateName): AppMediaState | undefined {
    return this.statesByName[stateName];
  }

  private getStatesFromCss(): AppMediaState[] {
    const bodyStyle = getComputedStyle(document.body);

    return APP_MEDIA_STATE_NAMES
      .map(name => {
        const minWidth = +bodyStyle.getPropertyValue(`--media-state-${name}`);
        return { name, minWidth };
      })
      .sort((stateA, stateB) => stateA.minWidth - stateB.minWidth)
      .map((state, index) => ({
        name: state.name,
        minWidth: state.minWidth,
        index
      }));
  }
}
