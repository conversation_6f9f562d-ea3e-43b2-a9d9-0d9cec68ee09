{"tables": [{"id": "tThzgZVYjuOUP4OLXR4DZ", "name": "ReadStatus (ValueObject)", "x": -192, "y": -192, "locked": false, "fields": [{"id": "9654GkLRYlL6KFO6n87zr", "name": "userId", "type": "VARCHAR", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#fcb8b8"}, {"id": "fNiS-DyIlgx1huC3G8-5_", "name": "VotingAnswerFileMetadata: FMB", "x": 432, "y": -1056, "locked": false, "fields": [{"id": "r0e_kTJLbv23h0Rh7Co93", "name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "Test Comment\n", "indices": [], "color": "#40de8f", "inherits": []}, {"id": "-kYHLfWwFsa-mPD5jsPem", "name": "VotingQuestionFileMetadata: FMB", "x": 1632, "y": -696, "locked": false, "fields": [{"name": "votingQuestionId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "_1j3Naoo7j9hbNRzmPSK0", "size": "", "values": []}], "comment": "Test Comment\n", "indices": [], "color": "#005c2e", "inherits": []}, {"id": "gRBl2e1G1ZEEvBz_9PkT7", "name": "DocumentFileMetadata: FMB", "x": 1704, "y": 576, "locked": false, "fields": [{"name": "documentAgendaId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "_1j3Naoo7j9hbNRzmPSK0", "size": "", "values": []}], "comment": "Test Comment\n", "indices": [], "color": "#b30444", "inherits": []}, {"id": "nZyzDU-ysbTjuhTewjCIy", "name": "FMB: FilesMetadataBase", "x": 2280, "y": -216, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "C4p-cZrswJAqwzwpcKE-g", "size": "", "values": []}, {"id": "06AqX42uvCxcpk11rZO33", "name": "fileContentId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "I99MEezppUOVp6uFwmiMc", "name": "originalFileName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "vjXu--72Se9ZTH90zw7FD", "name": "displayName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "7dcoRTA-HhNf0mAkSNTax", "name": "supplementary", "type": "BIT", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 1}, {"id": "7SINMhIeapcaJpxJJgSrN", "name": "order", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "-s4twqL8a6hjtTxOpkco9", "name": "size", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "82kPgf4P41OyBD0l1iHUt", "name": "numberOfPages", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "HJ11WGNZLC8sw9ccuKA5s", "name": "readStatus", "type": "LIST<READSTATUS>", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "C0bfz7-bimNfAJOPzzZ1L", "name": "Annotations (Aggregate)", "x": -264, "y": -1488, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "ddgLRL4q8L-dcS0F3htas", "size": "", "values": []}, {"id": "5t8L7hz2DBBLcwMH9Q9dP", "name": "fileMetadataId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "WgM0dDU5K6417wuQpQb0o", "name": "userId", "type": "VARCHAR", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255, "values": []}, {"id": "-4TwEn8yi2FArj2iLNz6Y", "name": "annotationXML", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#bf08cc"}, {"id": "BrLEfEmiWtd3U0ErmG4ZU", "name": "Azure_Users", "x": -1632, "y": -408, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "UWabVzODPLJ7GdCl2KBwY", "size": "", "values": []}, {"id": "UXn5Wpv62BCg9_fK2AAhw", "name": "displayName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "Vl-ls2mDrQ3CYx5R0yWxn", "name": "userPrincipalName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "uWJVpaXWMgIaEIMP442Kk", "name": "userId", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 255}], "comment": "This is a test comment", "indices": [], "color": "#00c907"}, {"id": "dipAisjDmkC81OKvYDHjt", "name": "Azure_Roles", "x": -1632, "y": 96, "locked": false, "fields": [{"id": "u8lbulkRT3_y3zohKnx9t", "name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "4HlJznJCKbuEBdMyeN890", "name": "description", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 512}], "comment": "", "indices": [{"id": 0, "name": "UserRole_index_0", "unique": false, "fields": ["id"]}], "color": "#00c907"}, {"id": "GPgUfFdv_oIclQlzfAIjl", "name": "Azure_UserRoles", "x": -1632, "y": -120, "locked": false, "fields": [{"name": "user_Id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "IDwLmX7xU7FQUEpwlLMf8", "size": "", "values": []}, {"id": "hQxCMokhrJL-zEVBkdNIS", "name": "role_Id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#00c907"}, {"id": "F67C3amoZCRiaRgQRmG_H", "name": "AgendaPermissionEnum", "x": -1344, "y": -1488, "locked": false, "fields": [{"name": "Manage", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "1yFbdDBdiOkiP5Wf6i4T8", "size": ""}, {"id": "-bnSpcC0QBe6IeMTqzLnc", "name": "Read", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}, {"id": "fr7rsu85Qo_xWtacCQmfX", "name": "Write", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}, {"id": "DV5GAHnvUrQGMowjXFYTU", "name": "<PERSON><PERSON>", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": ""}], "comment": "", "indices": [], "color": "#ed0231"}, {"id": "Qw-Ud_jk1CZtatM7BLIUd", "name": "Users (Aggregate)", "x": -912, "y": -576, "locked": false, "fields": [{"name": "id", "type": "VARCHAR", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "1gAiVI3HAMCWlg45Mg7iq", "size": 255, "values": []}, {"id": "5xQJh9YIdomc7iz0e5kfr", "name": "srUserId", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "n6eVLmyh0Y2UVR5qqWhhV", "name": "firstName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "yQAIpsyqQsTLYZsy04tnr", "name": "lastName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "Qr0z_2Y7VhYFZmqkNTD6j", "name": "Roles", "x": -936, "y": 48, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "Nl5siQJtKamxAtD-wRezv", "size": "", "values": []}, {"id": "L8QxE32kpRiBIcOEvD07f", "name": "description", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 512}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "7gaAoYt-ay3sUOffhwrTx", "name": "UserRoles", "x": -888, "y": -216, "locked": false, "fields": [{"name": "userId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "PLtt52luvpn0iw8HaDnMr", "size": "", "values": []}, {"id": "x3a-_c4IX8ZnkjJ776Wrk", "name": "roleId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "ZW7Sd5-VpJ2KkzE9FscDf", "name": "Domains (Aggregate)", "x": -216, "y": 480, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "ZPIhlQ4mptwZ3dCgdjfFg", "size": "", "values": []}, {"id": "O1-FpJqS2u9_jGD_vQvvB", "name": "parent_Id", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "vWXOZwhaeRMfEbrp_JYku", "name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "AVE3etF7ho4ZmHAV9GPoc", "name": "desccription", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "sYxwiZcub00B4s34WQ-P0", "name": "VotingContainer (Aggregate)", "x": 480, "y": -240, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "379TywFRq4bSiH4OIBEsw", "size": "", "values": []}, {"id": "9bvFmFYWtjQYKp9LCak7M", "name": "domainId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "6AfTEqqmZW0LNL3NfOXv-", "name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "8WIrkle910NuqS9CVvNGX", "name": "desccription", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 255}, {"id": "zedTgXJxtLppZvB4vssrq", "name": "type", "type": "MEETINGTYPEENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "pAR63q6TStvnKxYQ1vIZ9", "name": "LastUpdatedOn", "type": "DATE", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#005c2e"}, {"id": "gi7UBK7DU8bsF1dSqzFfv", "name": "VotingAgenda", "x": 1176, "y": -336, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "deDjdSqcoFflym7nQ2FIX", "size": "", "values": []}, {"id": "G2k762ahG0xG5xp12pWxF", "name": "containerId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "HplSiVmgPWN0bhNjPhw2L", "name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "kDbOIjZtGMbfD3Jw6agWZ", "name": "order", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "ykTC9sJK5r4RLnDQk4HVW", "name": "defaultVotingOptions", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "QWfP5IcLBU9iB5j1L61a5", "name": "permissions", "type": "LIST<AGENDAPERMISSION>", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [{"id": 0, "name": "Agenda (Aggregate)_index_0", "unique": false, "fields": []}], "color": "#005c2e"}, {"id": "tMC6lvGqM0GEgrCgcY2qT", "name": "VotingQuestions", "x": 1152, "y": -696, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "4oA5DhOyWNz8opmaz1RfS", "size": "", "values": []}, {"id": "dv3pq5y8P8lgSCazWs1Jg", "name": "meetingAgendaId", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "mHfqcfp99rrbUk1bRAj8z", "name": "question", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "0MP4nELWFL8eGKqDp01Bh", "name": "votingOptions", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#005c2e"}, {"id": "s8YfU3GEUDd2es6LDG61Y", "name": "VotingAnswers (Aggregate)", "x": 432, "y": -864, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "t-sbQFwJ9Yz9fUGJ5rKGK", "size": "", "values": []}, {"id": "jI8rcZ7QffoMp1Baz6uP-", "name": "votingQuestionId", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "RNQ4z5kx7j5IOvpaq08RH", "name": "on_behalf_of_userId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "ue_0bKp9ST6r_7fQbbRBH", "name": "answer", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "oSKBzJfiQ5_znVnpZhZ2L", "name": "comment", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 2048}, {"id": "4xodeWNCzhkSBc4mmrQyu", "name": "supportingDocumentFileMetadataId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": false, "increment": false, "comment": "Required when on_behalf_of_userId is populated", "size": "", "values": []}, {"id": "GeN5L4SlxJ0-e5naKCoze", "name": "fileName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#40de8f"}, {"id": "03-Eo1PtRCsfg-uetsFbx", "name": "AuditLog (Aggregate)", "x": 120, "y": -1488, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "nCrA_C46_j40KFbUZj3ad", "size": "", "values": []}, {"id": "_aYV6JD4Y1GV76p9-hhpx", "name": "entityName", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "8gpjTTX92sDLDMUhTp-pC", "name": "period", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 36}, {"id": "lRvCq4RQa8PgAQ7dqllrt", "name": "entityId", "type": "UUID", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "784iHMNvjQAXSLBaTOYKx", "name": "message", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "mBQHYrtH4A_pFhtnD25cB", "name": "payload", "type": "JSON", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#bf08cc"}, {"id": "-P6xZrRhU_hIcQeIVg1aF", "name": "UserSalt", "x": -864, "y": -864, "locked": false, "fields": [{"name": "id", "type": "INTEGER", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": true, "comment": "", "id": "C0DmgchXAB-Z6eZ2RD5rO"}, {"id": "1E1OxVPaXqXF4o1XA7MHF", "name": "userId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "I0m-Jt_I-bCuf_elvTPX8", "name": "salt", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "qeheFaOEJVxXiI7oJoPI3", "name": "DomainRoles", "x": -792, "y": 432, "locked": false, "fields": [{"id": "Sxk59tTjfnCmAvImaD5Oa", "name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"name": "role_Id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "x4-VFdhZjDX8HBMO1Lpwl", "size": "", "values": []}, {"id": "YGCLmn_Fy-Tw7XqlQu_Gk", "name": "domain_Id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "OfImgj3llEWolNZa6FMAQ", "name": "type", "type": "DOMAINROLEENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "-Jt_nSkuXDxyVpD5jm4r0", "name": "DomainRoleEnum", "x": -912, "y": -1488, "locked": false, "fields": [{"name": "Admin", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "UInfhT1ppYRRqTuxLGGY7", "size": 255}, {"id": "LZYg7ffh0Cm5Hv9KuJIjt", "name": "Member", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "gk-johqH8d7Ptj4es6eOi", "name": "Guest", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#ed0231"}, {"id": "yO_aaq3wmCQAEFzQCo1HB", "name": "FileContents (Aggregate)", "x": 2712, "y": -216, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "QkKukTPN-pM_KUlTO9r__", "size": "", "values": []}, {"id": "mh2Ev9bRTzSgxoDmxu7bj", "name": "contents", "type": "BYTEA", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "fPUakwIbf5_PMVjFAolOX", "name": "thumbnail", "type": "BYTEA", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#175e7a"}, {"id": "V37WDUKjoNzXkqDsMxVqI", "name": "MeetingContainer (Aggregate)", "x": 480, "y": 240, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "GSgHrEExhK24JqsXHSVUV", "size": "", "values": []}, {"id": "vZGxU375ICttqREb3WpCJ", "name": "domainId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "kNajbbOfPfzr-WqLAgyTj", "name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "Yt9i1NXQngwVzIrI5Lrq9", "name": "description", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#62177a"}, {"id": "aEnFPBFpMa6NWDNpQH3L1", "name": "DocumentContainer (Aggregate)", "x": 480, "y": 576, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "bkOFmW7zx6ObHXZoJMLuq", "size": "", "values": []}, {"id": "nfPcX6ojv5BvjmzJXKbdp", "name": "domainId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "tXK_1rlOSGfjOri8BvtEO", "name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "kwFdBpT3OoXQInaZDi3fW", "name": "description", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 255}], "comment": "", "indices": [], "color": "#b30444"}, {"id": "-s7yDNfAN6TAada36F-uT", "name": "MeetingAgenda", "x": 1200, "y": 144, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "KeDV69-4xPMyWWacKsi40", "size": "", "values": []}, {"id": "9uOY9CJ1z3MazU8juc3BL", "name": "containerId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "Efy652mbv9lJa0AEWoljQ", "name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": 255}, {"id": "wunONa8u7pZrmDppXtWS0", "name": "order", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": false, "increment": false, "comment": "", "size": "", "values": []}, {"id": "jdz-RGXcxJzfkI3etPjDE", "name": "permissions", "type": "LIST<AGENDAPERMISSION>", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#62177a"}, {"id": "LiCViAhWCl3diP32ACHDL", "name": "DocumentAgenda", "x": 1224, "y": 552, "locked": false, "fields": [{"name": "id", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "AnMQprh-Zq0T3ZpNPvclD", "size": "", "values": []}, {"id": "wGSwCps8HAwKHZCTk7J6z", "name": "containerId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "2Gar8rPmoxRJzmHZGCYpE", "name": "name", "type": "VARCHAR", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "sceWNqPeKIXxcrytmtCJw", "name": "order", "type": "INTEGER", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "7OFS447rzGprjbFXO1AMj", "name": "permissions", "type": "LIST<AGENDAPERMISSION>", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#b30444"}, {"id": "rIFi4-8pwqIqYViGjeGHw", "name": "MeetingFileMetadata: FMB", "x": 1656, "y": 144, "locked": false, "fields": [{"name": "meetingAgendaId", "type": "UUID", "default": "", "check": "", "primary": true, "unique": true, "notNull": true, "increment": false, "comment": "", "id": "_1j3Naoo7j9hbNRzmPSK0", "size": "", "values": []}], "comment": "Test Comment\n", "indices": [], "color": "#62177a", "inherits": []}, {"id": "FW0YO_QTuUZdcft9uDNQm", "name": "AgendaPermission (ValueObject)", "x": -192, "y": -384, "locked": false, "fields": [{"id": "9654GkLRYlL6KFO6n87zr", "name": "userId", "type": "VARCHAR", "default": "", "check": "", "primary": true, "unique": false, "notNull": true, "increment": false, "comment": "", "size": 255}, {"id": "3-UV81bck8jLnrWEfaJFN", "name": "currentPermission", "type": "AGENDAPERMISSIONENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}, {"id": "r12HzM-Ymv5cU9dPONMSo", "name": "inheritedPermission", "type": "AGENDAPERMISSIONENUM", "default": "", "check": "", "primary": false, "unique": false, "notNull": true, "increment": false, "comment": "", "size": "", "values": []}], "comment": "", "indices": [], "color": "#fcb8b8"}], "relationships": [{"startTableId": "GPgUfFdv_oIclQlzfAIjl", "startFieldId": "IDwLmX7xU7FQUEpwlLMf8", "endTableId": "BrLEfEmiWtd3U0ErmG4ZU", "endFieldId": "UWabVzODPLJ7GdCl2KBwY", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_Azure_UserRoles_user_Id_Azure_Users", "id": 0}, {"startTableId": "GPgUfFdv_oIclQlzfAIjl", "startFieldId": "hQxCMokhrJL-zEVBkdNIS", "endTableId": "dipAisjDmkC81OKvYDHjt", "endFieldId": "u8lbulkRT3_y3zohKnx9t", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_Azure_UserRoles_role_Id_Azure_Roles", "id": 1}, {"startTableId": "7gaAoYt-ay3sUOffhwrTx", "startFieldId": "PLtt52luvpn0iw8HaDnMr", "endTableId": "Qw-Ud_jk1CZtatM7BLIUd", "endFieldId": "1gAiVI3HAMCWlg45Mg7iq", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_UserRoles_userId_Users", "id": 2}, {"startTableId": "7gaAoYt-ay3sUOffhwrTx", "startFieldId": "x3a-_c4IX8ZnkjJ776Wrk", "endTableId": "Qr0z_2Y7VhYFZmqkNTD6j", "endFieldId": "Nl5siQJtKamxAtD-wRezv", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_UserRoles_roleId_Roles", "id": 3}, {"startTableId": "ZW7Sd5-VpJ2KkzE9FscDf", "startFieldId": "O1-FpJqS2u9_jGD_vQvvB", "endTableId": "ZW7Sd5-VpJ2KkzE9FscDf", "endFieldId": "ZPIhlQ4mptwZ3dCgdjfFg", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_Domain_domainId_Domain", "id": 4}, {"startTableId": "sYxwiZcub00B4s34WQ-P0", "startFieldId": "9bvFmFYWtjQYKp9LCak7M", "endTableId": "ZW7Sd5-VpJ2KkzE9FscDf", "endFieldId": "ZPIhlQ4mptwZ3dCgdjfFg", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_Meetings_domainId_Domains", "id": 5}, {"startTableId": "s8YfU3GEUDd2es6LDG61Y", "startFieldId": "jI8rcZ7QffoMp1Baz6uP-", "endTableId": "tMC6lvGqM0GEgrCgcY2qT", "endFieldId": "4oA5DhOyWNz8opmaz1RfS", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_VotingAnswers_votingQuestionId_VotingQuestions", "id": 8}, {"startTableId": "tMC6lvGqM0GEgrCgcY2qT", "startFieldId": "dv3pq5y8P8lgSCazWs1Jg", "endTableId": "gi7UBK7DU8bsF1dSqzFfv", "endFieldId": "deDjdSqcoFflym7nQ2FIX", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_VotingQuestions_meetingAgendaId_MeetingAgenda", "id": 9}, {"startTableId": "s8YfU3GEUDd2es6LDG61Y", "startFieldId": "RNQ4z5kx7j5IOvpaq08RH", "endTableId": "Qw-Ud_jk1CZtatM7BLIUd", "endFieldId": "1gAiVI3HAMCWlg45Mg7iq", "cardinality": "one_to_many", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_VotingAnswers_on_behalf_of_userId_Users", "id": 10}, {"startTableId": "-P6xZrRhU_hIcQeIVg1aF", "startFieldId": "1E1OxVPaXqXF4o1XA7MHF", "endTableId": "Qw-Ud_jk1CZtatM7BLIUd", "endFieldId": "1gAiVI3HAMCWlg45Mg7iq", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_UserSalt_userId_Users", "id": 11}, {"startTableId": "Qr0z_2Y7VhYFZmqkNTD6j", "startFieldId": "Nl5siQJtKamxAtD-wRezv", "endTableId": "qeheFaOEJVxXiI7oJoPI3", "endFieldId": "x4-VFdhZjDX8HBMO1Lpwl", "cardinality": "one_to_many", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_Roles_id_DomainRoles", "id": 13}, {"startTableId": "gi7UBK7DU8bsF1dSqzFfv", "startFieldId": "G2k762ahG0xG5xp12pWxF", "endTableId": "sYxwiZcub00B4s34WQ-P0", "endFieldId": "379TywFRq4bSiH4OIBEsw", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_Agenda (Aggregate)_agendaId_AgendaGroup (Aggregate)", "id": 17}, {"startTableId": "LiCViAhWCl3diP32ACHDL", "startFieldId": "wGSwCps8HAwKHZCTk7J6z", "endTableId": "aEnFPBFpMa6NWDNpQH3L1", "endFieldId": "bkOFmW7zx6ObHXZoJMLuq", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_DocumentAgenda_containerId_DocumentContainer (Aggregate)", "id": 18}, {"startTableId": "V37WDUKjoNzXkqDsMxVqI", "startFieldId": "GSgHrEExhK24JqsXHSVUV", "endTableId": "-s7yDNfAN6TAada36F-uT", "endFieldId": "9uOY9CJ1z3MazU8juc3BL", "cardinality": "one_to_many", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_MeetingContainer (Aggregate)_id_MeetingAgenda", "id": 19}, {"startTableId": "V37WDUKjoNzXkqDsMxVqI", "startFieldId": "vZGxU375ICttqREb3WpCJ", "endTableId": "ZW7Sd5-VpJ2KkzE9FscDf", "endFieldId": "ZPIhlQ4mptwZ3dCgdjfFg", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_MeetingContainer (Aggregate)_domainId_Domains (Aggregate)", "id": 20}, {"startTableId": "aEnFPBFpMa6NWDNpQH3L1", "startFieldId": "bkOFmW7zx6ObHXZoJMLuq", "endTableId": "ZW7Sd5-VpJ2KkzE9FscDf", "endFieldId": "ZPIhlQ4mptwZ3dCgdjfFg", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_DocumentContainer (Aggregate)_id_Domains (Aggregate)", "id": 21}, {"startTableId": "nZyzDU-ysbTjuhTewjCIy", "startFieldId": "06AqX42uvCxcpk11rZO33", "endTableId": "yO_aaq3wmCQAEFzQCo1HB", "endFieldId": "QkKukTPN-pM_KUlTO9r__", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_FilesMetadataBase_fileContentId_FileContents (Aggregate)", "id": 31}, {"startTableId": "rIFi4-8pwqIqYViGjeGHw", "startFieldId": "_1j3Naoo7j9hbNRzmPSK0", "endTableId": "-s7yDNfAN6TAada36F-uT", "endFieldId": "KeDV69-4xPMyWWacKsi40", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_MeetingFileMetadata_meetingAgendaId_MeetingAgenda", "id": 29}, {"startTableId": "-kYHLfWwFsa-mPD5jsPem", "startFieldId": "_1j3Naoo7j9hbNRzmPSK0", "endTableId": "tMC6lvGqM0GEgrCgcY2qT", "endFieldId": "4oA5DhOyWNz8opmaz1RfS", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_VotingQuestionFileMetadata: FMB_meetingAgendaId_VotingQuestions", "id": 26}, {"startTableId": "gRBl2e1G1ZEEvBz_9PkT7", "startFieldId": "_1j3Naoo7j9hbNRzmPSK0", "endTableId": "LiCViAhWCl3diP32ACHDL", "endFieldId": "AnMQprh-Zq0T3ZpNPvclD", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_DocumentFileMetadata: FMB_meetingAgendaId_DocumentAgenda", "id": 28}, {"startTableId": "qeheFaOEJVxXiI7oJoPI3", "startFieldId": "YGCLmn_Fy-Tw7XqlQu_Gk", "endTableId": "ZW7Sd5-VpJ2KkzE9FscDf", "endFieldId": "ZPIhlQ4mptwZ3dCgdjfFg", "cardinality": "many_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_DomainRoles_domain_Id_Domains (Aggregate)", "id": 28}, {"startTableId": "FW0YO_QTuUZdcft9uDNQm", "startFieldId": "9654GkLRYlL6KFO6n87zr", "endTableId": "Qw-Ud_jk1CZtatM7BLIUd", "endFieldId": "1gAiVI3HAMCWlg45Mg7iq", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_AgendaPermission_userId_Users (Aggregate)", "id": 29}, {"startTableId": "tThzgZVYjuOUP4OLXR4DZ", "startFieldId": "9654GkLRYlL6KFO6n87zr", "endTableId": "Qw-Ud_jk1CZtatM7BLIUd", "endFieldId": "1gAiVI3HAMCWlg45Mg7iq", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_ReadStatus (ValueObject)_userId_Users (Aggregate)", "id": 23}, {"startTableId": "C0bfz7-bimNfAJOPzzZ1L", "startFieldId": "WgM0dDU5K6417wuQpQb0o", "endTableId": "Qw-Ud_jk1CZtatM7BLIUd", "endFieldId": "1gAiVI3HAMCWlg45Mg7iq", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_Annotations (Aggregate)_userId_Users (Aggregate)", "id": 24}, {"startTableId": "s8YfU3GEUDd2es6LDG61Y", "startFieldId": "4xodeWNCzhkSBc4mmrQyu", "endTableId": "fNiS-DyIlgx1huC3G8-5_", "endFieldId": "r0e_kTJLbv23h0Rh7Co93", "cardinality": "one_to_one", "updateConstraint": "No action", "deleteConstraint": "No action", "name": "fk_VotingAnswers (Aggregate)_supportingDocumentFileMetadataId_VotingAnswerFileMetadata: FMB", "id": 24}], "notes": [], "subjectAreas": [], "database": "postgresql", "types": [{"name": "LIST<AgendaPermission>", "fields": [], "comment": ""}, {"name": "LIST<ReadStatus>", "fields": [], "comment": ""}], "enums": [{"name": "MeetingTypeEnum", "values": ["Meeting", "ChangeResolution"]}, {"name": "AgendaPermissionEnum", "values": ["Admin", "ReadWrite", "Read<PERSON>nly", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"name": "DomainRoleEnum", "values": ["Admin", "Member", "Guest"]}], "title": "ESP"}