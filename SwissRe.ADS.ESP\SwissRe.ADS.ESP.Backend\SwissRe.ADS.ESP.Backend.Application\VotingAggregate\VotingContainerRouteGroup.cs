﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAggregate
{
    public class VotingContainerRouteGroup : IRouteGroup
    {
        public static IEndpointRouteBuilder? BuildRoute([EndpointRouteBuilder<ApiRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder.MapGroup("Voting").WithTags("Voting");
    }
}
