{"name": "app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "genApiServicesFromLocalhost": "nswag run"}, "private": true, "dependencies": {"@angular/animations": "^20.1.3", "@angular/cdk": "20.1.3", "@angular/common": "^20.1.3", "@angular/compiler": "^20.1.3", "@angular/core": "^20.1.3", "@angular/forms": "^20.1.3", "@angular/material": "20.1.3", "@angular/platform-browser": "^20.1.3", "@angular/platform-browser-dynamic": "^20.1.3", "@angular/router": "^20.1.3", "ag-grid-angular": "33.2.4", "ag-grid-enterprise": "33.2.4", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.1"}, "devDependencies": {"@ads-shared/angular-schematics": "^20.0.0", "@angular/build": "^20.1.3", "@angular/cli": "^20.1.3", "@angular/compiler-cli": "^20.1.3", "@types/jasmine": "~5.1.4", "jasmine-core": "~5.1.2", "karma": "~6.4.3", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "nswag": "14.0.3", "prettier": "3.2.4", "typescript": "~5.8.3"}}