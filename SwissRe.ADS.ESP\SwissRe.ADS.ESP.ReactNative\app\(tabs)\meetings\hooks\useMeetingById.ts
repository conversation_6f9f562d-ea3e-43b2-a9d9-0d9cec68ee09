import { useMemo } from 'react';
import { MeetingRepository } from '@/database/repositories/meetingRepository';
import { GetMeetingDetailResponse } from '@/api/apiServices';
import { useQuery } from '@tanstack/react-query';

/**
 * Hook: useMeeting
 * ----------------
 * Encapsulates fetching a meeting by its ID using React Query and the MeetingRepository.
 *
 * Responsibilities:
 * - Creates an instance of MeetingRepository (memoized).
 * - Fetches meeting data by ID.
 * - Provides loading and error states.
 * - Returns a structured object { meeting, isLoading, error } for use in components.
 *
 * Usage:
 * const { meeting, isLoading, error } = useMeeting(meetingId);
 *
 * Notes:
 * - Keeps repository usage out of the component, making the UI layer cleaner.
 * - Integrates easily with Expo Router and React Query.
 */
export function useMeetingById(id: string | undefined) {
  const meetingRepository = useMemo(() => new MeetingRepository(), []);

  const { data, isLoading, error } = useQuery<GetMeetingDetailResponse | undefined>({
    queryKey: ['meeting', id],
    queryFn: () => meetingRepository.getMeetingById(id!),
    enabled: !!id, // only run when id is available
  });

  return {
    meeting: data,
    isLoading,
    error,
  };
}
