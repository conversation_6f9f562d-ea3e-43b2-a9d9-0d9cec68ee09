import { useEffect, useMemo } from 'react';
import { QueryClient } from '@tanstack/react-query';
import { MeetingRepository } from '@/database/repositories/meetingRepository';
import { SyncJobManager } from '../SyncJobManager';
import { FileContentsV1Client, GetMeetingDetailResponse } from '@/api/apiServices';
import { FileStorageService } from '@/utils/FileStorageService';

/**
 * Hook: useMeetingByIdSync
 * ---------------------
 * Handles real-time synchronization for a single meeting.
 *
 * Responsibilities:
 * - Subscribes to SyncJobManager for meeting data changes.
 * - Checks if the meeting has changed using MeetingRepository.
 * - Invalidates the React Query cache for the meeting if changes are detected.
 *
 * Usage:
 * useMeetingSync(meetingId, meetingData, queryClient);
 *
 * Notes:
 * - Encapsulates all sync logic, keeping the component clean.
 * - Automatically cleans up subscription on unmount.
 * - Should be used in combination with useMeeting for fetching.
 */
export function useMeetingByIdSync(meetingId: string | undefined, data: GetMeetingDetailResponse | undefined, queryClient: QueryClient) {
  const meetingRepository = useMemo(() => new MeetingRepository(), []);
  // const fileRepository = useMemo(() => new FileContentsV1Client(), []);

  useEffect(() => {
    if (!meetingId || !data) return;

    // fileRepository
    //   .getFileThumbnailContents(meetingId)
    //   .then(async (data) => {
    //     console.log(`Received new thumbnail contents for meeting ${meetingId}:`);
    //     console.log('data size', data.data.size);
    //     const uint8Array = new Uint8Array(data.data as any);
    //     console.log('uint8Array length', uint8Array.length);
    //     FileStorageService.saveFileFromBytes(uint8Array, `${meetingId}.png`).then((fileName) => {
    //       console.log(`File preview saved to ${fileName}`);
    //     });
    //   })
    //   .catch((error) => {
    //     console.error(`Error fetching file thumbnail contents: ${error}`);
    //   });

    const unsubscribe = SyncJobManager.getInstance().subscribe('meetingDataSyncJob', async (changed) => {
      console.log(`Received new data in the database: ${changed}`);

      if (await meetingRepository.hasMeetingByIdChanged(meetingId, data.lastUpdatedOn)) {
        console.log('Meeting has changed, invalidating query...');
        queryClient.invalidateQueries({ queryKey: ['meeting', meetingId] });
      }
    });

    return unsubscribe;
  }, [meetingId, data, meetingRepository, queryClient]);
}
