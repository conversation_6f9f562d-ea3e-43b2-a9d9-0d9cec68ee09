import { View, StyleSheet } from 'react-native';
import { MeetingFilterGroupByEnum } from './MeetingsFilter';
import { MeetingEntity } from '@/database/types/MeetingEntity';
import MeetingsGroup from './MeetingsGroup';
import { useEspAppTheme } from '@/hooks/useEspTheme';

export default function MeetingsList({ groupBy, meetings }: { groupBy: MeetingFilterGroupByEnum; meetings: MeetingEntity[] }) {
  const theme = useEspAppTheme();

  const groupMeetingsFunction = (meetings: MeetingEntity[]) => {
    // Group meetings by start time
    return meetings.reduce(
      (acc, meeting) => {
        const month = meeting.lastUpdatedOn?.getMonth();
        const year = meeting.lastUpdatedOn?.getFullYear();

        //convert month number to month name
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const monthName = monthNames[month];

        const key = `${monthName} ${year}`;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(meeting);
        return acc;
      },
      {} as Record<string, MeetingEntity[]>,
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.backgroundColors.tertiary }]}>
      {Object.entries(groupMeetingsFunction(meetings)).map(([key, group], index) => (
        <MeetingsGroup
          title={key}
          meetingItems={group}
          key={index}
          applyBottomBorder={index !== Object.entries(groupMeetingsFunction(meetings)).length - 1}
          applyTopPadding={index !== 0}
          applyBottomPadding={index !== Object.entries(groupMeetingsFunction(meetings)).length - 1}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    flex: 1,
    padding: 20,
  },
});
