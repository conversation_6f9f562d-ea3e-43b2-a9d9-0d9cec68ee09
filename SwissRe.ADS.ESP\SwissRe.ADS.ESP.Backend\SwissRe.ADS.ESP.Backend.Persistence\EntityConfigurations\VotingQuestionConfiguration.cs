﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class VotingQuestionConfiguration : IEntityTypeConfiguration<VotingQuestion>
    {
        public void Configure(EntityTypeBuilder<VotingQuestion> builder)
        {
            builder.ToTable("VotingQuestions");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.Property(x => x.Question).IsRequired().HasMaxLength(2056);
            builder.Property(x => x.DescriptionEncrypted);
            builder.Property(x => x.AvailableOptions).HasVotingOptionsConverter();

            builder.Property(x => x.CreatedOn).IsRequired();
            builder.Property(x => x.CreatedByUserId).IsRequired().HasMaxLength(128);
            builder.Property(x => x.LastModifiedOn).IsRequired();
            builder.Property(x => x.LastModifiedByUserId).IsRequired().HasMaxLength(128);

            builder.HasOne<User>()
                .WithMany()
                .HasForeignKey(x=>x.CreatedByUserId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne<User>()
                    .WithMany()
                    .HasForeignKey(x => x.LastModifiedByUserId)
                    .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
