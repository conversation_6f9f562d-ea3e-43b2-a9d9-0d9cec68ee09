﻿namespace SwissRe.ADS.ESP.Backend.Domain.Common
{
    /// <summary>
    /// Base class for entities with GUID Id.
    /// The Id is being generated as a sequential GUID in the constructor.
    /// </summary>
    /// 
    /// <remarks>
    /// Sequential GUID does not cause fragmentation in SQL server's clustered indexes.
    /// Some resources on the topic:
    /// - https://www.sqlshack.com/designing-effective-sql-server-clustered-indexes/
    /// - https://www.sqlskills.com/blogs/kimberly/guids-as-primary-keys-andor-the-clustering-key/
    /// - https://masstransit.io/documentation/patterns/newid
    /// </remarks>
    public abstract class StringEntity : Entity<string>
    {
        /// <summary>
        /// Original database ID for legacy systems. Will be removed post migration
        /// </summary>
        public long? ORIGINAL_DB_ID { get; private set; }
    }
}
