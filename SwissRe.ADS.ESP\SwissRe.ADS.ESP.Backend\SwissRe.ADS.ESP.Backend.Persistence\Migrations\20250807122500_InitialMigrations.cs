﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialMigrations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "espv2");

            migrationBuilder.EnsureSchema(
                name: "events");

            migrationBuilder.Sql(@"
        CREATE SEQUENCE ""User_SequenceId_seq""
            START WITH 1
            INCREMENT BY 1
            NO MINVALUE
            NO MAXVALUE
            CACHE 1;
    ");

            migrationBuilder.CreateTable(
                name: "__JobExecutionMetadata",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    LastSuccessfulRunUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    JobRunWindowEndUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastProcessedTimestampUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastProcessedSequenceId = table.Column<long>(type: "bigint", nullable: false),
                    LastUpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK___JobExecutionMetadata", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DeviceSyncFeeds",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    DeviceId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    EntityType = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    LastSuccessfulSync = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastSyncRequestedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeviceSyncFeeds", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Domains",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: true),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    HistoricalDocumentExpirationAfter = table.Column<int>(type: "integer", nullable: true),
                    AnnotationsExpirationAfter = table.Column<int>(type: "integer", nullable: true),
                    iPadDocumentsExpirationAfter = table.Column<int>(type: "integer", nullable: true),
                    ChangeStatusToPastAfter = table.Column<int>(type: "integer", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Domains", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Domains_Domains_ParentId",
                        column: x => x.ParentId,
                        principalSchema: "espv2",
                        principalTable: "Domains",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "FileContents",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EncryptedContents = table.Column<byte[]>(type: "bytea", nullable: false),
                    ThumbnailImageContents = table.Column<byte[]>(type: "bytea", nullable: false),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileContents", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OutboxedIntegrationEvents",
                schema: "events",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Event = table.Column<string>(type: "text", nullable: false),
                    TypeCode = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OutboxedIntegrationEvents", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AzureUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    FullName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    JobTitle = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    LastModifiedOnUTC = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SequenceId = table.Column<long>(type: "bigint", nullable: false, defaultValueSql: "nextval('\"User_SequenceId_seq\"')"),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BatchedSyncChangeEntries",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EntityId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ValueAsJson = table.Column<string>(type: "text", nullable: true),
                    UserLostAccess = table.Column<bool>(type: "boolean", nullable: false),
                    DeviceSyncFeedTrackerId = table.Column<Guid>(type: "uuid", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BatchedSyncChangeEntries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BatchedSyncChangeEntries_DeviceSyncFeeds_DeviceSyncFeedTrac~",
                        column: x => x.DeviceSyncFeedTrackerId,
                        principalSchema: "espv2",
                        principalTable: "DeviceSyncFeeds",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PendingSyncChangeEntries",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EntityId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ValueAsJson = table.Column<string>(type: "text", nullable: true),
                    UserLostAccess = table.Column<bool>(type: "boolean", nullable: false),
                    DeviceSyncFeedTrackerId = table.Column<Guid>(type: "uuid", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PendingSyncChangeEntries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PendingSyncChangeEntries_DeviceSyncFeeds_DeviceSyncFeedTrac~",
                        column: x => x.DeviceSyncFeedTrackerId,
                        principalSchema: "espv2",
                        principalTable: "DeviceSyncFeeds",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SynchronizedChanges",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DeviceSyncFeedTrackerId = table.Column<Guid>(type: "uuid", nullable: false),
                    EntityId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SyncedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ValueAsJson = table.Column<string>(type: "text", nullable: true),
                    UserLostAccess = table.Column<bool>(type: "boolean", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SynchronizedChanges", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SynchronizedChanges_DeviceSyncFeeds_DeviceSyncFeedTrackerId",
                        column: x => x.DeviceSyncFeedTrackerId,
                        principalSchema: "espv2",
                        principalTable: "DeviceSyncFeeds",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DocumentsContainer",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DomainId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    State = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    LastUpdatedOnUTC = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DocumentsContainer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DocumentsContainer_Domains_DomainId",
                        column: x => x.DomainId,
                        principalSchema: "espv2",
                        principalTable: "Domains",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "MeetingsContainer",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DomainId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    State = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    StartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Location = table.Column<string>(type: "text", nullable: true),
                    IncludeGuestRole = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    AutomaticallyExcludeNewUsers = table.Column<bool>(type: "boolean", nullable: false),
                    LastUpdatedOnUTC = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MeetingsContainer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MeetingsContainer_Domains_DomainId",
                        column: x => x.DomainId,
                        principalSchema: "espv2",
                        principalTable: "Domains",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "VotingsContainer",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DomainId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    State = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    DefaultVotingOptions = table.Column<string>(type: "character varying(2056)", nullable: false),
                    IncludeGuestRole = table.Column<bool>(type: "boolean", nullable: false),
                    LastUpdatedOnUTC = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingsContainer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingsContainer_Domains_DomainId",
                        column: x => x.DomainId,
                        principalSchema: "espv2",
                        principalTable: "Domains",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "DomainRoles",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    DomainId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleType = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DomainRoles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DomainRoles_Domains_DomainId",
                        column: x => x.DomainId,
                        principalSchema: "espv2",
                        principalTable: "Domains",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_DomainRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "espv2",
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Annotations",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FileMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    FileMetadataType = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    AnnotationUniqueId = table.Column<Guid>(type: "uuid", nullable: false),
                    EncryptedAnnotationXML = table.Column<byte[]>(type: "bytea", maxLength: 5092, nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Annotations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Annotations_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                schema: "espv2",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "text", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "espv2",
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "VotingAnswerSupportingDocumentFileMetadatas",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    FileContentId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    DisplayFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileExtension = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Supplementary = table.Column<bool>(type: "boolean", nullable: false),
                    FileGroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    VersionNumber = table.Column<int>(type: "integer", nullable: false),
                    IsLatest = table.Column<bool>(type: "boolean", nullable: false),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    Size = table.Column<int>(type: "integer", nullable: false),
                    NumberOfPages = table.Column<int>(type: "integer", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LastModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadStatuses = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingAnswerSupportingDocumentFileMetadatas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingAnswerSupportingDocumentFileMetadatas_FileContents_Fi~",
                        column: x => x.FileContentId,
                        principalSchema: "espv2",
                        principalTable: "FileContents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingAnswerSupportingDocumentFileMetadatas_Users_CreatedBy~",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingAnswerSupportingDocumentFileMetadatas_Users_LastModif~",
                        column: x => x.LastModifiedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DocumentsAgenda",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentContainerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    Permissions = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DocumentsAgenda", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DocumentsAgenda_DocumentsContainer_ParentContainerId",
                        column: x => x.ParentContainerId,
                        principalSchema: "espv2",
                        principalTable: "DocumentsContainer",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MeetingsAgenda",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentContainerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    ParentAgendaId = table.Column<Guid>(type: "uuid", nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    Permissions = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MeetingsAgenda", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MeetingsAgenda_MeetingsAgenda_ParentAgendaId",
                        column: x => x.ParentAgendaId,
                        principalSchema: "espv2",
                        principalTable: "MeetingsAgenda",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MeetingsAgenda_MeetingsContainer_ParentContainerId",
                        column: x => x.ParentContainerId,
                        principalSchema: "espv2",
                        principalTable: "MeetingsContainer",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VotingsAgenda",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentContainerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Description = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    Permissions = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingsAgenda", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingsAgenda_VotingsContainer_ParentContainerId",
                        column: x => x.ParentContainerId,
                        principalSchema: "espv2",
                        principalTable: "VotingsContainer",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DocumentFileMetadatas",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentAgendaId = table.Column<Guid>(type: "uuid", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    FileContentId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    DisplayFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileExtension = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Supplementary = table.Column<bool>(type: "boolean", nullable: false),
                    FileGroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    VersionNumber = table.Column<int>(type: "integer", nullable: false),
                    IsLatest = table.Column<bool>(type: "boolean", nullable: false),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    Size = table.Column<int>(type: "integer", nullable: false),
                    NumberOfPages = table.Column<int>(type: "integer", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LastModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadStatuses = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DocumentFileMetadatas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DocumentFileMetadatas_DocumentsAgenda_DocumentAgendaId",
                        column: x => x.DocumentAgendaId,
                        principalSchema: "espv2",
                        principalTable: "DocumentsAgenda",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DocumentFileMetadatas_FileContents_FileContentId",
                        column: x => x.FileContentId,
                        principalSchema: "espv2",
                        principalTable: "FileContents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DocumentFileMetadatas_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DocumentFileMetadatas_Users_LastModifiedByUserId",
                        column: x => x.LastModifiedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "MeetingFileMetadatas",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MeetingAgendaId = table.Column<Guid>(type: "uuid", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    FileContentId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    DisplayFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileExtension = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Supplementary = table.Column<bool>(type: "boolean", nullable: false),
                    FileGroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    VersionNumber = table.Column<int>(type: "integer", nullable: false),
                    IsLatest = table.Column<bool>(type: "boolean", nullable: false),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    Size = table.Column<int>(type: "integer", nullable: false),
                    NumberOfPages = table.Column<int>(type: "integer", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LastModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadStatuses = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MeetingFileMetadatas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MeetingFileMetadatas_FileContents_FileContentId",
                        column: x => x.FileContentId,
                        principalSchema: "espv2",
                        principalTable: "FileContents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MeetingFileMetadatas_MeetingsAgenda_MeetingAgendaId",
                        column: x => x.MeetingAgendaId,
                        principalSchema: "espv2",
                        principalTable: "MeetingsAgenda",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MeetingFileMetadatas_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MeetingFileMetadatas_Users_LastModifiedByUserId",
                        column: x => x.LastModifiedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VotingQuestions",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AgendaId = table.Column<Guid>(type: "uuid", nullable: false),
                    Question = table.Column<string>(type: "character varying(2056)", maxLength: 2056, nullable: false),
                    DescriptionEncrypted = table.Column<string>(type: "character varying(5092)", maxLength: 5092, nullable: true),
                    AvailableOptions = table.Column<string>(type: "character varying(2056)", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LockedForEditing = table.Column<bool>(type: "boolean", nullable: false),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingQuestions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingQuestions_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingQuestions_Users_LastModifiedByUserId",
                        column: x => x.LastModifiedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingQuestions_VotingsAgenda_AgendaId",
                        column: x => x.AgendaId,
                        principalSchema: "espv2",
                        principalTable: "VotingsAgenda",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VotingAnswers",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    VotingQuestionId = table.Column<Guid>(type: "uuid", nullable: false),
                    AnswerEncrypted = table.Column<byte[]>(type: "bytea", maxLength: 1024, nullable: false),
                    CommentEncrypted = table.Column<byte[]>(type: "bytea", maxLength: 5012, nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    IsLatest = table.Column<bool>(type: "boolean", nullable: false),
                    OnBehalfOfUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ORIGINAL_ONBEHALF_FILE_ID = table.Column<long>(type: "bigint", nullable: true),
                    OnBehalfOfDocumenMetadatatId = table.Column<Guid>(type: "uuid", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingAnswers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingAnswers_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingAnswers_Users_OnBehalfOfUserId",
                        column: x => x.OnBehalfOfUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingAnswers_VotingAnswerSupportingDocumentFileMetadatas_O~",
                        column: x => x.OnBehalfOfDocumenMetadatatId,
                        principalSchema: "espv2",
                        principalTable: "VotingAnswerSupportingDocumentFileMetadatas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingAnswers_VotingQuestions_VotingQuestionId",
                        column: x => x.VotingQuestionId,
                        principalSchema: "espv2",
                        principalTable: "VotingQuestions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "VotingQuestionSupportingDocumentFileMetadatas",
                schema: "espv2",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    VotingQuestionId = table.Column<Guid>(type: "uuid", nullable: false),
                    VotingQuestionId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    ORIGINAL_DB_ID = table.Column<long>(type: "bigint", nullable: true),
                    FileContentId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    DisplayFileName = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileExtension = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Supplementary = table.Column<bool>(type: "boolean", nullable: false),
                    FileGroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    VersionNumber = table.Column<int>(type: "integer", nullable: false),
                    IsLatest = table.Column<bool>(type: "boolean", nullable: false),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    Size = table.Column<int>(type: "integer", nullable: false),
                    NumberOfPages = table.Column<int>(type: "integer", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: false),
                    LastModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadStatuses = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VotingQuestionSupportingDocumentFileMetadatas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_FileContents_~",
                        column: x => x.FileContentId,
                        principalSchema: "espv2",
                        principalTable: "FileContents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_Users_Created~",
                        column: x => x.CreatedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_Users_LastMod~",
                        column: x => x.LastModifiedByUserId,
                        principalSchema: "espv2",
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_VotingQuestio~",
                        column: x => x.VotingQuestionId,
                        principalSchema: "espv2",
                        principalTable: "VotingQuestions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1",
                        column: x => x.VotingQuestionId1,
                        principalSchema: "espv2",
                        principalTable: "VotingQuestions",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Annotations_CreatedByUserId",
                schema: "espv2",
                table: "Annotations",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Annotations_FileMetadataId_CreatedByUserId",
                schema: "espv2",
                table: "Annotations",
                columns: new[] { "FileMetadataId", "CreatedByUserId" });

            migrationBuilder.CreateIndex(
                name: "IX_BatchedSyncChangeEntries_DeviceSyncFeedTrackerId",
                schema: "espv2",
                table: "BatchedSyncChangeEntries",
                column: "DeviceSyncFeedTrackerId");

            migrationBuilder.CreateIndex(
                name: "IX_DeviceSyncFeeds_UserId_DeviceId_EntityType",
                schema: "espv2",
                table: "DeviceSyncFeeds",
                columns: new[] { "UserId", "DeviceId", "EntityType" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFileMetadatas_CreatedByUserId",
                schema: "espv2",
                table: "DocumentFileMetadatas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFileMetadatas_DocumentAgendaId",
                schema: "espv2",
                table: "DocumentFileMetadatas",
                column: "DocumentAgendaId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFileMetadatas_FileContentId",
                schema: "espv2",
                table: "DocumentFileMetadatas",
                column: "FileContentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFileMetadatas_LastModifiedByUserId",
                schema: "espv2",
                table: "DocumentFileMetadatas",
                column: "LastModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentsAgenda_ParentContainerId",
                schema: "espv2",
                table: "DocumentsAgenda",
                column: "ParentContainerId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentsContainer_DomainId",
                schema: "espv2",
                table: "DocumentsContainer",
                column: "DomainId");

            migrationBuilder.CreateIndex(
                name: "IX_DomainRoles_DomainId",
                schema: "espv2",
                table: "DomainRoles",
                column: "DomainId");

            migrationBuilder.CreateIndex(
                name: "IX_DomainRoles_RoleId",
                schema: "espv2",
                table: "DomainRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Domains_ParentId",
                schema: "espv2",
                table: "Domains",
                column: "ParentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MeetingFileMetadatas_CreatedByUserId",
                schema: "espv2",
                table: "MeetingFileMetadatas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_MeetingFileMetadatas_FileContentId",
                schema: "espv2",
                table: "MeetingFileMetadatas",
                column: "FileContentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MeetingFileMetadatas_LastModifiedByUserId",
                schema: "espv2",
                table: "MeetingFileMetadatas",
                column: "LastModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_MeetingFileMetadatas_MeetingAgendaId",
                schema: "espv2",
                table: "MeetingFileMetadatas",
                column: "MeetingAgendaId");

            migrationBuilder.CreateIndex(
                name: "IX_MeetingsAgenda_ParentAgendaId",
                schema: "espv2",
                table: "MeetingsAgenda",
                column: "ParentAgendaId");

            migrationBuilder.CreateIndex(
                name: "IX_MeetingsAgenda_ParentContainerId",
                schema: "espv2",
                table: "MeetingsAgenda",
                column: "ParentContainerId");

            migrationBuilder.CreateIndex(
                name: "IX_MeetingsContainer_DomainId",
                schema: "espv2",
                table: "MeetingsContainer",
                column: "DomainId");

            migrationBuilder.CreateIndex(
                name: "IX_PendingSyncChangeEntries_DeviceSyncFeedTrackerId",
                schema: "espv2",
                table: "PendingSyncChangeEntries",
                column: "DeviceSyncFeedTrackerId");

            migrationBuilder.CreateIndex(
                name: "IX_SynchronizedChanges_DeviceSyncFeedTrackerId",
                schema: "espv2",
                table: "SynchronizedChanges",
                column: "DeviceSyncFeedTrackerId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                schema: "espv2",
                table: "UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_AzureUserId",
                schema: "espv2",
                table: "Users",
                column: "AzureUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingAnswers_CreatedByUserId",
                schema: "espv2",
                table: "VotingAnswers",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingAnswers_OnBehalfOfDocumenMetadatatId",
                schema: "espv2",
                table: "VotingAnswers",
                column: "OnBehalfOfDocumenMetadatatId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VotingAnswers_OnBehalfOfUserId",
                schema: "espv2",
                table: "VotingAnswers",
                column: "OnBehalfOfUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingAnswers_VotingQuestionId",
                schema: "espv2",
                table: "VotingAnswers",
                column: "VotingQuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingAnswerSupportingDocumentFileMetadatas_CreatedByUserId",
                schema: "espv2",
                table: "VotingAnswerSupportingDocumentFileMetadatas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingAnswerSupportingDocumentFileMetadatas_FileContentId",
                schema: "espv2",
                table: "VotingAnswerSupportingDocumentFileMetadatas",
                column: "FileContentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VotingAnswerSupportingDocumentFileMetadatas_LastModifiedByU~",
                schema: "espv2",
                table: "VotingAnswerSupportingDocumentFileMetadatas",
                column: "LastModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestions_AgendaId",
                schema: "espv2",
                table: "VotingQuestions",
                column: "AgendaId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestions_CreatedByUserId",
                schema: "espv2",
                table: "VotingQuestions",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestions_LastModifiedByUserId",
                schema: "espv2",
                table: "VotingQuestions",
                column: "LastModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_CreatedByUser~",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_FileContentId",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "FileContentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_LastModifiedB~",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "LastModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "VotingQuestionId1");

            migrationBuilder.CreateIndex(
                name: "IX_VotingQuestionSupportingDocumentFileMetadatas_VotingQuestio~",
                schema: "espv2",
                table: "VotingQuestionSupportingDocumentFileMetadatas",
                column: "VotingQuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingsAgenda_ParentContainerId",
                schema: "espv2",
                table: "VotingsAgenda",
                column: "ParentContainerId");

            migrationBuilder.CreateIndex(
                name: "IX_VotingsContainer_DomainId",
                schema: "espv2",
                table: "VotingsContainer",
                column: "DomainId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "__JobExecutionMetadata",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "Annotations",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "BatchedSyncChangeEntries",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "DocumentFileMetadatas",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "DomainRoles",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "MeetingFileMetadatas",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "OutboxedIntegrationEvents",
                schema: "events");

            migrationBuilder.DropTable(
                name: "PendingSyncChangeEntries",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "SynchronizedChanges",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "UserRoles",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "VotingAnswers",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "VotingQuestionSupportingDocumentFileMetadatas",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "DocumentsAgenda",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "MeetingsAgenda",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "DeviceSyncFeeds",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "Roles",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "VotingAnswerSupportingDocumentFileMetadatas",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "VotingQuestions",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "DocumentsContainer",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "MeetingsContainer",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "FileContents",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "Users",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "VotingsAgenda",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "VotingsContainer",
                schema: "espv2");

            migrationBuilder.DropTable(
                name: "Domains",
                schema: "espv2");
        }
    }
}
