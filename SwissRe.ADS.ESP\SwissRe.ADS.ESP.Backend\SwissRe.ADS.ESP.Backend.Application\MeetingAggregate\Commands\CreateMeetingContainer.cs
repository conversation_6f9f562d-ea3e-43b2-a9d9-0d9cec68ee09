﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using Microsoft.AspNetCore.Http;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Commands
{
    public record CreateMeetingContainerCommand(string name, string description, Guid domainId, List<CreateMeetingAgendaCommand> agendas);
    public record CreateMeetingAgendaCommand(string name, string? description, int order, List<CreateMeetingAgendaCommand> children);

    public class CreateMeetingContainerEndpoint(UnitOfWork unitOfWork, IContainerAuthorizationService containerPermissionService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IContainerAuthorizationService _containerPermissionService = containerPermissionService;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
                    builder
                        .MapPost("/containers", (CreateMeetingContainerCommand command, CreateMeetingContainerEndpoint endpoint) => endpoint.HandleAsync(command))
                        .WithSummary("Create a new meeting container.")
                        .WithDescription("Creates a new meeting container in the specified domain, including its agendas. Requires permission to create containers in the domain.")
                        .WithAngularName<MeetingContainerRouteGroup>("CreateMeetingContainer");

        public async Task HandleAsync(CreateMeetingContainerCommand command)
        {
            var canCreateContainer = await _containerPermissionService.CanUserCreateContainerAsync(command.domainId);
            if (canCreateContainer == false)
                throw new UnauthorizedAccessException("Current user does not have permission to create a new container in this domain.");


            var repo = _unitOfWork.Repository<MeetingContainer>();
            var container = MeetingContainer.Create(command.name, command.domainId, null, null, null, command.description);
            var agendaUserAccess = await _unitOfWork.ReadRepository<IDomainReadModelRepository>().GetDomainAgendaPermissions(command.domainId);

            foreach (var agenda in command.agendas)
            {
                AddAgendaRecursive(container, agenda, null, agendaUserAccess);
            }

            repo.Insert(container);

            await _unitOfWork.CommitTransactionAsync();
        }

        private void AddAgendaRecursive(MeetingContainer container, CreateMeetingAgendaCommand createAgendaCommand, MeetingAgenda? parent, List<(AgendaPermissionEnum accessType, string userId)> agendaUserAccess)
        {
            var agenda = container.AddAgenda(createAgendaCommand.name, createAgendaCommand.description, createAgendaCommand.order, agendaUserAccess, parent);

            foreach (var childDto in createAgendaCommand.children.OrderBy(c => c.order))
            {
                AddAgendaRecursive(container, childDto, agenda, agendaUserAccess);
            }
        }
    }
}
