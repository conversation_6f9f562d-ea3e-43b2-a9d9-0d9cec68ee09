﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Web;
using System.Net;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi
{
    public static class Auth
    {
        public static void AddAuth(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                var useMacOsDevAuth = configuration.GetValue<bool>("AzureAd:USE_MACOS_SPECIFIC_AUTHENTICATION_FOR_LOCAL_DEVELOPMENT_ONLY");
                if (useMacOsDevAuth)
                    AddDevelopmentAuthenticationForMacOsOnly(services, configuration);
                else
                    AddDevAuthentication(services, configuration);
            }
            else
            {
                services.AddMicrosoftIdentityWebApiAuthentication(configuration, "AzureAd");
            }

            services.AddAuthorizationBuilder()
                .SetFallbackPolicy(new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build());
        }

        private static void AddDevAuthentication(IServiceCollection services, IConfiguration configuration)
        {
            services.AddMicrosoftIdentityWebAppAuthentication(configuration, "AzureAd");

            services.Configure<OpenIdConnectOptions>(OpenIdConnectDefaults.AuthenticationScheme, options =>
            {
                options.TokenValidationParameters.NameClaimType = CurrentUser.AzureUserIdClaimType;

                options.Events = new OpenIdConnectEvents
                {
                    OnRedirectToIdentityProvider = ctx =>
                    {
                        if (ctx.Request.Path.StartsWithSegments("/api") && ctx.Response.StatusCode == 200)
                        {
                            ctx.Response.StatusCode = 401;
                            ctx.HandleResponse();
                        }
                        return Task.CompletedTask;
                    }
                };

                var shouldUseProxyForLocalDev = configuration.GetValue<bool>("Authentication:ShouldUseProxyForLocalDevelopment");

                if (shouldUseProxyForLocalDev)
                {
                    options.BackchannelHttpHandler = new HttpClientHandler()
                    {
                        UseProxy = true,
                        UseDefaultCredentials = true,
                        Proxy = new WebProxy
                        {
                            Credentials = CredentialCache.DefaultNetworkCredentials,
                            Address = new Uri(configuration.GetValue<string>("Authentication:ProxyUrl")!)
                        }
                    };
                }
            });
        }

        private static void AddDevelopmentAuthenticationForMacOsOnly(IServiceCollection services, IConfiguration configuration)
        {
            services
            .AddAuthentication(options =>
                {
                    options.DefaultAuthenticateScheme = "FakeBearer";
                    options.DefaultChallengeScheme = "FakeBearer";
                })
            .AddCookie(options =>
            {
                options.Events.OnRedirectToLogin = ctx =>
                {
                    // Instead of redirecting, return 401 for API calls
                    if (ctx.Request.Path.StartsWithSegments("/api"))
                    {
                        ctx.Response.StatusCode = 401;
                        return Task.CompletedTask;
                    }

                    ctx.Response.Redirect(ctx.RedirectUri);
                    return Task.CompletedTask;
                };
            })
            .AddScheme<AuthenticationSchemeOptions, FakeBearerAuthenticationForMacOsLocalDevelopmentHandler>("FakeBearer", options => { }); ;

            // Fake logged-in user
            services.AddSingleton<IClaimsTransformation>(_ => new ClaimsTransformationForMacOsLocalDevelopmentOnly());
        }


        // This creates a fake user principal
        public class ClaimsTransformationForMacOsLocalDevelopmentOnly : IClaimsTransformation
        {
            public Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
            {
                var claims = new List<Claim>
                {
                    new Claim("swissreuid", "IOS_SIMULATOR"),
                    new Claim(CurrentUser.AzureUserIdClaimType, "00000000-0000-0000-0000-000000000000"),
                    new Claim("name", "Simulator User"),
                    new Claim("preferred_username", "<EMAIL>"),
                    new Claim(ClaimTypes.Role, "Developer"),
                    new Claim(ClaimTypes.Role, "Admin")
                };


                var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                return Task.FromResult(new ClaimsPrincipal(identity));
            }
        }

        // Add this new fake bearer handler to accept any bearer token and set fake user
        public class FakeBearerAuthenticationForMacOsLocalDevelopmentHandler : AuthenticationHandler<AuthenticationSchemeOptions>
        {
            public FakeBearerAuthenticationForMacOsLocalDevelopmentHandler(
                IOptionsMonitor<AuthenticationSchemeOptions> options,
                ILoggerFactory logger,
                UrlEncoder encoder,
                ISystemClock clock) : base(options, logger, encoder, clock)
            {
            }

            protected override Task<AuthenticateResult> HandleAuthenticateAsync()
            {
                var claims = new List<Claim>
                {
                    new Claim("swissreuid", "IOS_SIMULATOR"),
                    new Claim(CurrentUser.AzureUserIdClaimType, "00000000-0000-0000-0000-000000000000"),
                    new Claim("name", "Simulator User"),
                    new Claim("preferred_username", "<EMAIL>"),
                    new Claim(ClaimTypes.Role, "Developer"),
                    new Claim(ClaimTypes.Role, "Admin")
                };

                var identity = new ClaimsIdentity(claims, Scheme.Name);
                var principal = new ClaimsPrincipal(identity);
                var ticket = new AuthenticationTicket(principal, Scheme.Name);

                return Task.FromResult(AuthenticateResult.Success(ticket));
            }
        }
    }
}
