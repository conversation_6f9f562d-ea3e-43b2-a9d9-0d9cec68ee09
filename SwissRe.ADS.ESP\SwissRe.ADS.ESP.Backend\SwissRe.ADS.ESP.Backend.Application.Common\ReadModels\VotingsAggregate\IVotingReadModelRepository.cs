﻿namespace SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.VotingsAggregate
{
    public interface IVotingReadModelRepository: IReadModelRepository
    {
        Task<GetVotingDetailResponse> GetVotingDetailById(Guid meetingContainerId, string SrUserId, CancellationToken cancellationToken = default);


        /// <summary>
        /// Retrieves a paginated list of voting details for the specified user that have been updated since the provided synchronization timestamp.
        /// Only votings for which the user has access are included. The result supports pagination and indicates if more results are available.
        /// </summary>
        /// <param name="SrUserId">The unique identifier of the user (SR User ID).</param>
        /// <param name="lastSyncedOn">The timestamp representing the last successful synchronization. Only meetings updated after this time are returned.</param>
        /// <param name="take">The maximum number of votings to return (default is 50).</param>
        /// <param name="skip">The number of votings to skip for pagination (default is 0).</param>
        /// <param name="cancellationToken">Optional cancellation token for async operation.</param>
        /// <returns>
        /// A <see cref="PagedMeetingDetailResponse"/> containing the list of updated votings and a flag indicating if more results are available.
        /// </returns>

        Task<PagedVotingDetailResponse> GetPagedVotingDetailsForUser(string SrUserId, DateTime lastSyncedOn, int take = 50, int skip = 0, CancellationToken cancellationToken = default);
    }
}
