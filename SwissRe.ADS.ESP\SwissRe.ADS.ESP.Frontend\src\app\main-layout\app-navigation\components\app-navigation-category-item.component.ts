import { CommonModule } from '@angular/common';
import { Component, computed, forwardRef, inject, input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { AppNavigationItemListComponent } from './app-navigation-item-list.component';
import { AppNavigationCategoryItem } from '../model';
import { AppNavigationItemExpandService } from '../services/app-navigation-item-expand.service';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-navigation-category-item',
  imports: [MatIconModule, CommonModule, forwardRef(() => AppNavigationItemListComponent)],
  template: `
    <div class="header" (click)="isExpanded() ? collapse() : expand()">
      <mat-icon>{{ item().icon }}</mat-icon>
      {{ item().title }}
      <mat-icon>{{ isExpanded() ? 'expand_less' : 'expand_more' }}</mat-icon>
    </div>
    @if (isExpanded()) {
      <app-navigation-item-list class="children" [items]="item().children"></app-navigation-item-list>
    }
  `,
  styles: `
    :host {
      width: 100%;
      cursor: pointer;
    }
    .header {
      display: grid;
      align-items: center;
      grid-template-columns: 24px 1fr auto;
      gap: 16px;
    }
    .children {
      padding-top: 16px;
    }
  `
})
export class AppNavigationCategoryItemComponent {
  readonly item = input.required<AppNavigationCategoryItem>();
  readonly isExpanded = computed(() => this.expandedItemKeys().includes(this.item().key));
  private itemExpandService = inject(AppNavigationItemExpandService);
  private expandedItemKeys = toSignal(this.itemExpandService.expandedItemKeys$, { initialValue: [] });

  expand() {
    this.itemExpandService.expand(this.item());
  }

  collapse() {
    this.itemExpandService.collapse(this.item());
  }
}
