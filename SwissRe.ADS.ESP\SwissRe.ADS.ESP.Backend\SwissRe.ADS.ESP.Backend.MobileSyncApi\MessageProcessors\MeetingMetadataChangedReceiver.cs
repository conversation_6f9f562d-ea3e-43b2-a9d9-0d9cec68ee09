﻿using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate.ServiceBusMessages;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Common;
using SwissRe.ADS.ServiceBus.Receiving;
using System.Text.Json;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.MessageProcessors
{
    public class MeetingMetadataChangedReceiver(UnitOfWork unitOfWork) : Receiver<MeetingMetadataChangedMessage>
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;

        protected override void Configure(ReceiverConfig config)
        {
            config.MaxConcurrentCalls = 1; //TODO: test with multiple receivers, should be fine as they are scoped
        }

        protected override async Task<IHandleMessageResult<MeetingMetadataChangedMessage>> HandleMessageAsync(HandleMessageArgs<MeetingMetadataChangedMessage> args)
        {
            var distinctUsersWithAccessToMeeting = await _unitOfWork.Repository<MeetingContainer>()
                                            .GetAllAsync(meetings =>
                                                         meetings.Where(x => x.Id == args.Message.MeetingId)
                                                                .SelectMany(meeting => meeting.Agendas
                                                                            .SelectMany(agenda => agenda.Permissions
                                                                                    .Where(perm => perm.CurrentPermission.HasFlag(AgendaPermissionEnum.Deny) == false)
                                                                                    .Select(perm => perm.UserId)
                                                                                    .Distinct()))
                                                                , false);

            if (distinctUsersWithAccessToMeeting == null || !distinctUsersWithAccessToMeeting.Any())
            {
                //TODO: Handle this exception better
                throw new ArgumentOutOfRangeException("No users with access to the meeting found.");
            }


            foreach (var user in distinctUsersWithAccessToMeeting)
            {
                var userDeviceSync = await _unitOfWork.Repository<DeviceSyncFeedTracker>().GetFirstOrNullAsync(x => x.UserId == user && x.EntityType == EspSyncEntityType.Meetings, true);
                if (userDeviceSync is null) continue;

                var userMeetingData = await _unitOfWork.Repository<MeetingContainer>()
                                            .GetFirstOrNullAsync(meetings =>
                                                                 meetings.Where(m => m.Id == args.Message.MeetingId)
                                            .Select(MeetingContainerEntityFrameworkExtensions.MeetingContainerProjection(user)));

                if (userMeetingData is null)
                {
                    //TODO: add logging here instead of exception
                    throw new Exception("Meeting data for the user was not found. This should never happend as user was actually taken from the meeting itself");
                }

                var stringifiedMeetingData = JsonSerializer.Serialize(userMeetingData, SharedJsonSerializerSettings.Shared_Json_Serializer_Options);
                userDeviceSync.UpsertPendingChange(args.Message.MeetingId, userMeetingData.LastUpdatedOn, stringifiedMeetingData);
            }

            await _unitOfWork.CommitTransactionAsync();

            return Complete();
        }

    }
}
