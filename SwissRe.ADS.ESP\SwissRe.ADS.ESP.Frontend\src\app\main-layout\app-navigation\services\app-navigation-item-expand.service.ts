import { Router, NavigationEnd } from '@angular/router';
import { Observable, Subject, startWith, pairwise, map, filter, withLatestFrom, tap, shareReplay, merge } from 'rxjs';
import { AppNavigationCategoryItem, AppNavigationItem, AppNavigation, AppNavigationItemKey, AppNavigationRouterLink } from '../model';
import { Injectable, inject } from '@angular/core';
import { AppNavigationConfig } from '../config';

/**
 * Maintains an array of expanded category navigation items.
 * The array represents a single source of thruth for the expanded state of navigation items.
 *
 * Also allows for imperative expansion and collapsing of navigation items.
 */
@Injectable({
  providedIn: 'root'
})
export class AppNavigationItemExpandService {
  readonly expandedItemKeys$: Observable<AppNavigationItemKey[]>;
  private expandedItemKeysSnapshot: AppNavigationItemKey[] = [];

  private readonly imperativeExpandsSubject = new Subject<AppNavigationItemKey>();
  private readonly imperativeCollapsesSubject = new Subject<AppNavigationItemKey>();

  private appNavigationConfig = inject(AppNavigationConfig);
  private router = inject(Router);

  constructor() {
    const categoryItemChanges$ = this.appNavigationConfig.appNavigation$.pipe(
      startWith(null),
      pairwise(),
      map(([prevModel, currentModel]) => this.getAddedRemovedCategoryItems(prevModel, currentModel!))
    );

    this.expandedItemKeys$ = merge(this.handleExpanding(categoryItemChanges$), this.handleCollapsing(categoryItemChanges$)).pipe(
      startWith([]),
      tap(itemKeys => (this.expandedItemKeysSnapshot = itemKeys)),
      shareReplay(1)
    );
  }

  /** Expands a category navigation item. */
  expand(item: AppNavigationCategoryItem) {
    this.imperativeExpandsSubject.next(item.key);
  }

  /** Collapses a category navigation item. */
  collapse(item: AppNavigationCategoryItem) {
    this.imperativeCollapsesSubject.next(item.key);
  }

  private getAddedRemovedCategoryItems(prevModel: AppNavigation | null, currentModel: AppNavigation): CategoryItemChanges {
    const prevCategoryItemsByKey = this.getCategoryItems(prevModel?.items ?? [], new Map<AppNavigationItemKey, AppNavigationCategoryItem>());
    const currentCategoryItemsByKey = this.getCategoryItems(currentModel.items, new Map<AppNavigationItemKey, AppNavigationCategoryItem>());

    return {
      addedItems: [...currentCategoryItemsByKey.values()].filter(item => !prevCategoryItemsByKey.has(item.key)),
      removedItems: [...prevCategoryItemsByKey.values()].filter(item => !currentCategoryItemsByKey.has(item.key))
    };
  }

  /** Creates an observable that emits arrays of all expanded item keys after any event that triggers a navigation item expansion. */
  private handleExpanding(categoryItemChanges$: Observable<CategoryItemChanges>): Observable<AppNavigationItemKey[]> {
    const newItemsWithDefaultExpand$ = categoryItemChanges$.pipe(
      map(({ addedItems }) => addedItems.filter(item => item.defaultExpanded).map(item => item.key))
    );

    const itemsWithActivatedRoute$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      withLatestFrom(this.appNavigationConfig.appNavigation$),
      map(([_, model]) => this.getCategoryItemKeysWithActivatedChild([], model.items))
    );

    const imperativeExpands$ = this.imperativeExpandsSubject.pipe(map(itemKey => [itemKey]));

    return merge(newItemsWithDefaultExpand$, itemsWithActivatedRoute$, imperativeExpands$).pipe(
      map(categoryItemKeysToExpand => [...new Set([...this.expandedItemKeysSnapshot, ...categoryItemKeysToExpand])])
    );
  }

  /** Creates an observable that emits arrays of all expanded item keys after any event that triggers a navigation item collapsing. */
  private handleCollapsing(categoryItemChanges$: Observable<CategoryItemChanges>): Observable<AppNavigationItemKey[]> {
    const removedItems$ = categoryItemChanges$.pipe(map(({ removedItems }) => removedItems.map(item => item.key)));

    const imperativeCollapses$ = this.imperativeCollapsesSubject.pipe(map(itemKey => [itemKey]));

    return merge(removedItems$, imperativeCollapses$).pipe(
      map(categoryItemKeysToCollapse => {
        const categoryItemKeysToCollapseSet = new Set(categoryItemKeysToCollapse);
        return this.expandedItemKeysSnapshot.filter(itemKey => !categoryItemKeysToCollapseSet.has(itemKey));
      })
    );
  }

  private getCategoryItems(items: AppNavigationItem[], itemsByKey: Map<AppNavigationItemKey, AppNavigationCategoryItem>) {
    items.forEach(item => {
      if (item.type === 'category') {
        itemsByKey.set(item.key, item);
        this.getCategoryItems(item.children, itemsByKey);
      }
    });
    return itemsByKey;
  }

  private getCategoryItemKeysWithActivatedChild(ancestorKeys: AppNavigationItemKey[], children: AppNavigationItem[]): AppNavigationItemKey[] {
    return children.flatMap(child => {
      if (child.type === 'category') {
        return this.getCategoryItemKeysWithActivatedChild([...ancestorKeys, child.key], child.children);
      }

      return child.action.type === 'routerLink' && this.isActive(child.action) ? ancestorKeys : [];
    });
  }

  private isActive(routerLinkAction: AppNavigationRouterLink): boolean {
    const urlTree = this.router.createUrlTree(routerLinkAction.commands, routerLinkAction.extras);

    return this.router.isActive(urlTree, {
      paths: 'exact',
      queryParams: 'exact',
      fragment: 'ignored',
      matrixParams: 'ignored'
    });
  }
}

interface CategoryItemChanges {
  addedItems: AppNavigationCategoryItem[];
  removedItems: AppNavigationCategoryItem[];
}
