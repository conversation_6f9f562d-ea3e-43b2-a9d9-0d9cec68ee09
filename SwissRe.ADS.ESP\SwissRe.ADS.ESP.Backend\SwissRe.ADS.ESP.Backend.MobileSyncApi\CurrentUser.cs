﻿using Microsoft.Extensions.Caching.Memory;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Application.CurrentUser;
using System.Security.Claims;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi
{
    internal class CurrentUser : ICurrentUser
    {
        public const string AzureUserIdClaimType = "http://schemas.microsoft.com/identity/claims/objectidentifier";
        private static readonly HashSet<CurrentUserRole> _emptyRoles = [];
        private List<Guid> _userEspRoles = [];

        public string? SrUserId { get; }
        public Guid AzureUserId { get; } = Guid.Empty;
        public string FullName { get; } = "N/A";
        public string Email { get; } = "N/A";
        public IReadOnlySet<CurrentUserRole> Roles { get; } = _emptyRoles;

        public IReadOnlyCollection<Guid> EspRoles => _userEspRoles.AsReadOnly();

        public CurrentUser(IHttpContextAccessor accessor, UnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            if (accessor.HttpContext is { } httpContext)
            {
                SrUserId = GetClaim(httpContext, "swissreuid");
                AzureUserId = Guid.Parse(GetClaim(httpContext, AzureUserIdClaimType)!);
                FullName = GetClaim(httpContext, "name")!;
                Email = GetClaim(httpContext, "preferred_username")!;
                Roles = GetRoles(httpContext);

                if (SrUserId is null)
                    SrUserId = $"NO_SRUSERID_{AzureUserId.ToString()}";

                GetEspRoles(AzureUserId, unitOfWork, memoryCache);
            }
        }

        private void GetEspRoles(Guid azureUserId, UnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            var cacheKey = $"EspUserRoles_{azureUserId}";
            if (memoryCache.TryGetValue(cacheKey, out var espUserGroups))
            {
                if (espUserGroups is List<Guid> && espUserGroups is not null)
                    _userEspRoles = (List<Guid>)espUserGroups;

                return;
            }

            var dbRoles = unitOfWork.Repository<User>().GetFirstOrNullAsync(x => x.AzureUserId == azureUserId).GetAwaiter().GetResult();
            if (dbRoles is null)
            {
                _userEspRoles = [];
                return;
            }
            else
            {
                _userEspRoles = dbRoles.UserRoles.Select(x => x.RoleId).ToList();
                memoryCache.Set(cacheKey, _userEspRoles, TimeSpan.FromMinutes(15));
            }
        }

        private string? GetClaim(HttpContext httpContext, string type) =>
            httpContext.User.Claims.SingleOrDefault(claim => claim.Type == type)?.Value;

        private IReadOnlySet<CurrentUserRole> GetRoles(HttpContext httpContext)
        {
            var roles = httpContext.User.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(claim => CurrentUserRoleUtils.TryConvert(claim.Value, out var role) ? role : (CurrentUserRole?)null)
                .Where(role => role != null)
                .Select(role => role!.Value);

            return new HashSet<CurrentUserRole>(roles);
        }
    }
}
