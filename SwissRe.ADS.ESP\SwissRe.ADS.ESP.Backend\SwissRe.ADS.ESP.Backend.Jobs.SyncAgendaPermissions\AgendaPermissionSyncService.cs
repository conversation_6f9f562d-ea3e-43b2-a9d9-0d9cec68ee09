﻿using Microsoft.Extensions.Logging;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Jobs.Common;
using Microsoft.EntityFrameworkCore;
using System.Data;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;

namespace SwissRe.ADS.ESP.Backend.Jobs.SyncAgendaPermissions
{
    public sealed class AgendaPermissionSyncService : DeltaSyncJobBase<User>
    {
        private readonly ILogger<AgendaPermissionSyncService> _logger;
        private readonly UnitOfWork _unitOfWork;
        private readonly DatabaseHelpers _databaseHelpers;

        public AgendaPermissionSyncService(ILogger<AgendaPermissionSyncService> logger, UnitOfWork unitOfWork, DatabaseHelpers databaseHelpers) : base(unitOfWork, logger, "AgendaPermissionSyncJob")
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            this._databaseHelpers = databaseHelpers;
        }

        protected override Func<IQueryable<User>, IQueryable<User>> BuildSourceQuery()
        {
            return query => query.Where(u => u.IsActive);
        }

        protected override async Task ProcessEntityAsync(User entity, CancellationToken token)
        {
            var currentAccessSnapshot = await _unitOfWork.ReadRepository<IDomainReadModelRepository>()
                                                                .GetAgendaPermissionsForAllDomainsForUser(entity.Id, token);


            await ProcessMeetings(entity.Id, currentAccessSnapshot, token);

            //TODO: Process Voting containers
            //TODO: Process Document containers
        }

        private async Task ProcessMeetings(string userId,
            List<(AgendaPermissionEnum accessType, Guid domainId)> currentAccessSnapshot,
            CancellationToken token)
        {
            //retrieve current snapshot before sync
            var currentMeetingsWhereUserBelongedBeforeSync = await _databaseHelpers.GetAccessibleMeetingIdsForUser(userId, token);

            var domainIds = currentAccessSnapshot.Select(d => d.domainId).ToList();

            var userMeetingsForCurrentSnapshot = await _unitOfWork.Repository<MeetingContainer>()
                .GetAllAsync(meetings => meetings
                    .Where(m => domainIds.Contains(m.DomainId) && m.State != ContainerStateEnum.PastMeeting)
                , true);


            foreach (var meeting in userMeetingsForCurrentSnapshot)
            {
                var userAccessLevel = currentAccessSnapshot.First(d => d.domainId == meeting.DomainId).accessType;
                meeting.SynchronizeUserAccessForAllAgendas(userId, userAccessLevel);
            }

            //remove user from meetings they no longer belong to
            var meetingsToRemoveIds = currentMeetingsWhereUserBelongedBeforeSync
                .Where(m => !userMeetingsForCurrentSnapshot.Any(x => x.Id == m))
                .ToList();

            if (meetingsToRemoveIds.Count == 0) return;

            var meetingsToRemove = await _unitOfWork.Repository<MeetingContainer>()
                .GetAllAsync(meetings => meetings
                    .Where(m => meetingsToRemoveIds.Contains(m.Id))
                , true);

            foreach (var meeting in meetingsToRemove)
                meeting.RemoveUserAccessFromAllAgendas(userId);
        }
    }
}
