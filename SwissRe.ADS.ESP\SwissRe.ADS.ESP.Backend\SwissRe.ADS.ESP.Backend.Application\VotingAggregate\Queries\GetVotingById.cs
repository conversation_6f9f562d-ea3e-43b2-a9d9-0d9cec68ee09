using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using Microsoft.AspNetCore.Builder;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using Microsoft.AspNetCore.Http;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.VotingsAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAggregate.Queries
{
    public class GetVotingByIdEndpoint(UnitOfWork unitOfWork, ICurrentUser currentUser, IContainerAuthorizationService containerAuthorizationService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;
        private readonly IContainerAuthorizationService _containerAuthorizationService = containerAuthorizationService;

        public static void BuildRoute([EndpointRouteBuilder<VotingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/containers/{votingContainerId}", (Guid votingContainerId, GetVotingByIdEndpoint endpoint) => endpoint.HandleAsync(votingContainerId))
                .WithSummary("Get details of a specific voting container by its ID.")
                .WithDescription("Returns detailed information about a voting container, including its agendas and files, if the current user has access. Requires view permission on the voting container.")
                .WithAngularName<VotingContainerRouteGroup>("GetById");
        }

        public async Task<GetVotingDetailResponse?> HandleAsync(Guid votingContainerId)
        {
            var canAccess = await _containerAuthorizationService.CanUserViewContainerAsync(votingContainerId, _currentUser.SrUserId, AgendaTypeEnum.VotingAgenda);
            if (canAccess == false)
                throw new UnauthorizedAccessException("Current user does not have access to this voting");

            return await _unitOfWork.ReadRepository<IVotingReadModelRepository>().GetVotingDetailById(votingContainerId, _currentUser.SrUserId);
        }
    }
}
