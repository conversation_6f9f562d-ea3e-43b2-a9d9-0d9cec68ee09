import { ColumnSchema } from './ColumnMapping';

/**
 * MeetingEntity is a simple wrapper interface used to store meeting data in the database.
 * It contains an 'id' for identification and a 'valueAsJson' string to hold the serialized meeting details.
 */
export interface MeetingEntity {
  id: string;
  name: string;
  description?: string;
  lastUpdatedOn: Date;
  state: string;
  startTime?: Date;
  endTime?: Date;
  location?: string;
  numberOfUnreadDocuments: number;
  valueAsJson: string;
}

export const meetingEntityColumnSchema: ColumnSchema<MeetingEntity> = {
  id: { column: 'id', type: 'string' },
  valueAsJson: { column: 'valueAsJson', type: 'string' },
  name: { column: 'Name', type: 'string' },
  description: { column: 'Description', type: 'optional-string' },
  lastUpdatedOn: { column: 'LastUpdatedOn', type: 'date' },
  state: { column: 'State', type: 'string' },
  startTime: { column: 'StartTime', type: 'optional-date' },
  endTime: { column: 'EndTime', type: 'optional-date' },
  location: { column: 'Location', type: 'optional-string' },
  numberOfUnreadDocuments: {
    column: 'NumberOfUnreadDocuments',
    type: 'number',
  },
};
