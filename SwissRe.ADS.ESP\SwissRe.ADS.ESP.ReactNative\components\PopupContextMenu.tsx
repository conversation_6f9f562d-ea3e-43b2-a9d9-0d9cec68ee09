import { StyleSheet, TouchableOpacity, Text } from 'react-native';
import { MenuAction, MenuView } from '@react-native-menu/menu';
import { useEspAppTheme } from '@/hooks/useEspTheme';
import { IconSymbol } from './ui/IconSymbol';
import { ThemedText } from './ThemedText';

export default function PopupContextMenu({
  menuTitle,
  textLabel,
  actions,
  onActionSelected,
}: {
  menuTitle: string;
  textLabel: string;
  actions: MenuAction[];
  // eslint-disable-next-line no-unused-vars
  onActionSelected: (actionId: string) => void;
}) {
  const theme = useEspAppTheme();
  return (
    <TouchableOpacity>
      <MenuView
        hitSlop={{ top: 100, bottom: 10, left: 1000, right: 10 }}
        style={styles.menuStyle}
        title={menuTitle}
        onPressAction={({ nativeEvent }) => {
          onActionSelected(nativeEvent.event);
        }}
        actions={actions}
        shouldOpenOnLongPress={false}
      >
        <ThemedText type="link">{textLabel}</ThemedText>
        <IconSymbol name="chevron.up.chevron.down" size={18} weight="medium" color={theme.colors.icon} style={{ marginLeft: 4, alignSelf: 'center' }} />
      </MenuView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  menuStyle: {
    alignSelf: 'flex-start',
    flexDirection: 'row',
  },
});
