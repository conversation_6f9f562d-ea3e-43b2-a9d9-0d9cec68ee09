﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class DeviceSyncFeedTrackerConfiguration : IEntityTypeConfiguration<DeviceSyncFeedTracker>
    {
        public void Configure(EntityTypeBuilder<DeviceSyncFeedTracker> builder)
        {
            builder.ToTable("DeviceSyncFeeds");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion();

            builder.Property(x => x.UserId).HasMaxLength(255).IsRequired();
            builder.Property(x => x.DeviceId).HasMaxLength(255).IsRequired();
            builder.Property(x => x.EntityType).HasConversion<string>().HasMaxLength(512).IsRequired();

            // Enforce uniqueness on UserId, DeviceId, EntityType
            builder.HasIndex(x => new { x.UserId, x.DeviceId, x.EntityType }).IsUnique();

            // Owned collection: PendingChangedItems
            builder.OwnsMany(t => t.PendingChangedItems, a =>
            {
                a.WithOwner();
                a.ToTable("PendingSyncChangeEntries");
                a.HasKey(e=>e.Id);
                a.Property(x => x.Id).ValueGeneratedNever();

                // Optionally map properties explicitly
                a.Property(p => p.EntityId);
                a.Property(p => p.UpdatedOn);
                a.Property(p => p.ValueAsJson);
                a.Property(p => p.UserLostAccess);
            });

            // Owned collection: BatchedItems
            builder.OwnsMany(t => t.BatchedItems, a =>
            {
                a.WithOwner();
                a.ToTable("BatchedSyncChangeEntries");
                a.HasKey(e => e.Id);
                a.Property(x => x.Id).ValueGeneratedNever();

                a.Property(p => p.EntityId);
                a.Property(p => p.UpdatedOn);
                a.Property(p => p.ValueAsJson);
                a.Property(p => p.UserLostAccess);
            });

            // SyncedItems: separate entity
            builder.HasMany(t => t.SyncedItems)
                .WithOne()
                .HasForeignKey(x=>x.DeviceSyncFeedTrackerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Navigation(p => p.PendingChangedItems).AutoInclude();
            builder.Navigation(p => p.BatchedItems).AutoInclude();
        }
    }
}
