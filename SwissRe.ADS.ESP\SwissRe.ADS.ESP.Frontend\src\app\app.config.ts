import { ApplicationConfig, inject, provideAppInitializer } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { provideAnimations } from '@angular/platform-browser/animations';
import { HttpInterceptorFn, provideHttpClient, withInterceptors } from '@angular/common/http';
import { globalLoaderHttpInterceptor } from './shared/global-loader/http-middleware';
import { error500HttpInterceptor } from './shared/global-error-handling/error-500/error-500-http-interceptor';
import { error401HttpInterceptor } from './shared/global-error-handling/error-401/error-401-http-interceptor';
import { API_BASE_URL } from './shared/apiServices';
import { requestHeadersHttpInterceptor } from './shared/request-headers-http-interceptor';
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import { RowGroupingModule, RowGroupingPanelModule } from 'ag-grid-enterprise';
import { ConfigService } from './shared/config.service';

ModuleRegistry.registerModules([AllCommunityModule, RowGroupingModule, RowGroupingPanelModule]);

const httpInterceptors: HttpInterceptorFn[] = [
  error401HttpInterceptor,
  error500HttpInterceptor,
  globalLoaderHttpInterceptor,
  requestHeadersHttpInterceptor
];

const appInitializerFn = () => {
  const configService = inject(ConfigService);
  return configService.loadConfig();
};

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideAnimations(),
    provideHttpClient(withInterceptors(httpInterceptors)),
    { provide: API_BASE_URL, useValue: '' },
    provideAppInitializer(appInitializerFn)
  ]
};
