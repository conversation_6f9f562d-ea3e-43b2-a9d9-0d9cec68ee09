//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.0.3.0 (NJsonSchema v11.0.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* tslint:disable */
/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase, HttpContext } from '@angular/common/http';

export const API_BASE_URL = new InjectionToken<string>('API_BASE_URL');

@Injectable({
    providedIn: 'root'
})
export class VotingAnswerClient {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "https://localhost:5001";
    }

    /**
     * Submit a vote for a specific voting question.
     */
    createUserVote(command: CreateUserVoteCommand, questionId: string, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/VotingAnswer/questions/{questionId}/votes";
        if (questionId === undefined || questionId === null)
            throw new Error("The parameter 'questionId' must be defined.");
        url_ = url_.replace("{questionId}", encodeURIComponent("" + questionId));
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(command);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateUserVote(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateUserVote(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreateUserVote(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable({
    providedIn: 'root'
})
export class VotingContainerClient {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "https://localhost:5001";
    }

    /**
     * Get details of a specific voting container by its ID.
     */
    getById(votingContainerId: string, httpContext?: HttpContext): Observable<GetVotingDetailResponse> {
        let url_ = this.baseUrl + "/api/Voting/containers/{votingContainerId}";
        if (votingContainerId === undefined || votingContainerId === null)
            throw new Error("The parameter 'votingContainerId' must be defined.");
        url_ = url_.replace("{votingContainerId}", encodeURIComponent("" + votingContainerId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetById(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetById(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetVotingDetailResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetVotingDetailResponse>;
        }));
    }

    protected processGetById(response: HttpResponseBase): Observable<GetVotingDetailResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetVotingDetailResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    createVotingAgenda(command: CreateVotingAgendaCommand, containerId: string, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Voting/CreateVotingAgenda?";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined and cannot be null.");
        else
            url_ += "containerId=" + encodeURIComponent("" + containerId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(command);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateVotingAgenda(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateVotingAgenda(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreateVotingAgenda(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Create a new voting container.
     */
    createVotingContainer(command: CreateVotingContainerCommand, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Voting/containers";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(command);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateVotingContainer(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateVotingContainer(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreateVotingContainer(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Create a new voting question in a voting agenda.
     */
    createVotingQuestion(command: CreateVotingQuestionCommand, containerId: string, agendaId: string, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Voting/containers/{containerId}/agendas/{agendaId}/questions";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined.");
        url_ = url_.replace("{containerId}", encodeURIComponent("" + containerId));
        if (agendaId === undefined || agendaId === null)
            throw new Error("The parameter 'agendaId' must be defined.");
        url_ = url_.replace("{agendaId}", encodeURIComponent("" + agendaId));
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(command);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateVotingQuestion(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateVotingQuestion(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreateVotingQuestion(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Add a supporting document to a voting question.
     */
    createVotingQuestionSupportingDocument(containerId: string, agendaId: string, questionId: string, file: FileParameter, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Voting/containers/{containerId}/agendas/{agendaId}/questions/{questionId}/documents";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined.");
        url_ = url_.replace("{containerId}", encodeURIComponent("" + containerId));
        if (agendaId === undefined || agendaId === null)
            throw new Error("The parameter 'agendaId' must be defined.");
        url_ = url_.replace("{agendaId}", encodeURIComponent("" + agendaId));
        if (questionId === undefined || questionId === null)
            throw new Error("The parameter 'questionId' must be defined.");
        url_ = url_.replace("{questionId}", encodeURIComponent("" + questionId));
        url_ = url_.replace(/[?&]$/, "");

        const content_ = new FormData();
        if (file === null || file === undefined)
            throw new Error("The parameter 'file' cannot be null.");
        else
            content_.append("file", file.data, file.fileName ? file.fileName : "file");

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateVotingQuestionSupportingDocument(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateVotingQuestionSupportingDocument(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreateVotingQuestionSupportingDocument(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable({
    providedIn: 'root'
})
export class MeetingContainerClient {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "https://localhost:5001";
    }

    /**
     * Get all current and future meetings that user can manage.
     */
    getAdminDashboardCurrentData(httpContext?: HttpContext): Observable<GetAdminDashboardCurrentDataResponse> {
        let url_ = this.baseUrl + "/api/Meeting/adminDashboard/current";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetAdminDashboardCurrentData(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetAdminDashboardCurrentData(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetAdminDashboardCurrentDataResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetAdminDashboardCurrentDataResponse>;
        }));
    }

    protected processGetAdminDashboardCurrentData(response: HttpResponseBase): Observable<GetAdminDashboardCurrentDataResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetAdminDashboardCurrentDataResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Get all past meetings that user can manage.
     */
    getAdminDashboardPastData(httpContext?: HttpContext): Observable<GetAdminDashboardPastDataResponse> {
        let url_ = this.baseUrl + "/api/Meeting/adminDashboard/past";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetAdminDashboardPastData(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetAdminDashboardPastData(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetAdminDashboardPastDataResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetAdminDashboardPastDataResponse>;
        }));
    }

    protected processGetAdminDashboardPastData(response: HttpResponseBase): Observable<GetAdminDashboardPastDataResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetAdminDashboardPastDataResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Get all versions of a meeting file in an agenda.
     */
    getAllMeetingFileMetadataVersions(containerId: string, agendaId: string, fileGroupId: string, httpContext?: HttpContext): Observable<MeetingFileMetadataResponse[]> {
        let url_ = this.baseUrl + "/api/Meeting/containers/{containerId}/agendas/{agendaId}/files/{fileGroupId}/versions";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined.");
        url_ = url_.replace("{containerId}", encodeURIComponent("" + containerId));
        if (agendaId === undefined || agendaId === null)
            throw new Error("The parameter 'agendaId' must be defined.");
        url_ = url_.replace("{agendaId}", encodeURIComponent("" + agendaId));
        if (fileGroupId === undefined || fileGroupId === null)
            throw new Error("The parameter 'fileGroupId' must be defined.");
        url_ = url_.replace("{fileGroupId}", encodeURIComponent("" + fileGroupId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetAllMeetingFileMetadataVersions(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetAllMeetingFileMetadataVersions(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<MeetingFileMetadataResponse[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<MeetingFileMetadataResponse[]>;
        }));
    }

    protected processGetAllMeetingFileMetadataVersions(response: HttpResponseBase): Observable<MeetingFileMetadataResponse[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(MeetingFileMetadataResponse.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Get details of a specific meeting container by its ID.
     */
    getById(meetingContainerId: string, httpContext?: HttpContext): Observable<GetMeetingDetailResponse> {
        let url_ = this.baseUrl + "/api/Meeting/containers/{meetingContainerId}";
        if (meetingContainerId === undefined || meetingContainerId === null)
            throw new Error("The parameter 'meetingContainerId' must be defined.");
        url_ = url_.replace("{meetingContainerId}", encodeURIComponent("" + meetingContainerId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetById(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetById(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetMeetingDetailResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetMeetingDetailResponse>;
        }));
    }

    protected processGetById(response: HttpResponseBase): Observable<GetMeetingDetailResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetMeetingDetailResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Get all current and future meetings that user can read.
     */
    getUserDashboardCurrentData(httpContext?: HttpContext): Observable<GetUserDashboardCurrentDataResponse> {
        let url_ = this.baseUrl + "/api/Meeting/userDashboard/current";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetUserDashboardCurrentData(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetUserDashboardCurrentData(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetUserDashboardCurrentDataResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetUserDashboardCurrentDataResponse>;
        }));
    }

    protected processGetUserDashboardCurrentData(response: HttpResponseBase): Observable<GetUserDashboardCurrentDataResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetUserDashboardCurrentDataResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Get all past meetings that user can read.
     */
    getUserDashboardPastData(httpContext?: HttpContext): Observable<GetUserDashboardPastDataResponse> {
        let url_ = this.baseUrl + "/api/Meeting/userDashboard/past";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetUserDashboardPastData(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetUserDashboardPastData(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetUserDashboardPastDataResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetUserDashboardPastDataResponse>;
        }));
    }

    protected processGetUserDashboardPastData(response: HttpResponseBase): Observable<GetUserDashboardPastDataResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetUserDashboardPastDataResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Create a new meeting container.
     */
    createMeetingContainer(command: CreateMeetingContainerCommand, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Meeting/containers";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(command);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateMeetingContainer(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateMeetingContainer(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreateMeetingContainer(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Upload a new file to a meeting agenda.
     */
    createMeetingFile(containerId: string, agendaId: string, file: FileParameter, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Meeting/containers/{containerId}/agendas/{agendaId}/files";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined.");
        url_ = url_.replace("{containerId}", encodeURIComponent("" + containerId));
        if (agendaId === undefined || agendaId === null)
            throw new Error("The parameter 'agendaId' must be defined.");
        url_ = url_.replace("{agendaId}", encodeURIComponent("" + agendaId));
        url_ = url_.replace(/[?&]$/, "");

        const content_ = new FormData();
        if (file === null || file === undefined)
            throw new Error("The parameter 'file' cannot be null.");
        else
            content_.append("file", file.data, file.fileName ? file.fileName : "file");

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateMeetingFile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateMeetingFile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreateMeetingFile(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Manually overwrite the current user's agenda permission.
     */
    manuallyOverwriteAgendaPermission(containerId: string, rootAgendaId: string, newPermission: AgendaPermissionEnum, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Meeting/containers/{containerId}/agendas/{rootAgendaId}/permissions?";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined.");
        url_ = url_.replace("{containerId}", encodeURIComponent("" + containerId));
        if (rootAgendaId === undefined || rootAgendaId === null)
            throw new Error("The parameter 'rootAgendaId' must be defined.");
        url_ = url_.replace("{rootAgendaId}", encodeURIComponent("" + rootAgendaId));
        if (newPermission === undefined || newPermission === null)
            throw new Error("The parameter 'newPermission' must be defined and cannot be null.");
        else
            url_ += "newPermission=" + encodeURIComponent("" + newPermission) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processManuallyOverwriteAgendaPermission(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processManuallyOverwriteAgendaPermission(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processManuallyOverwriteAgendaPermission(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Mark a meeting agenda file as read for the current user.
     */
    markMeetingFileAsRead(containerId: string, agendaId: string, fileId: string, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Meeting/containers/{containerId}/agendas/{agendaId}/files/{fileId}/read";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined.");
        url_ = url_.replace("{containerId}", encodeURIComponent("" + containerId));
        if (agendaId === undefined || agendaId === null)
            throw new Error("The parameter 'agendaId' must be defined.");
        url_ = url_.replace("{agendaId}", encodeURIComponent("" + agendaId));
        if (fileId === undefined || fileId === null)
            throw new Error("The parameter 'fileId' must be defined.");
        url_ = url_.replace("{fileId}", encodeURIComponent("" + fileId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processMarkMeetingFileAsRead(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processMarkMeetingFileAsRead(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processMarkMeetingFileAsRead(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Replace a file in a meeting agenda with a new version.
     */
    replaceMeetingFileWithNewVersion(containerId: string, agendaId: string, originalFileId: string, file: FileParameter, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Meeting/containers/{containerId}/agendas/{agendaId}/files/{originalFileId}/replace";
        if (containerId === undefined || containerId === null)
            throw new Error("The parameter 'containerId' must be defined.");
        url_ = url_.replace("{containerId}", encodeURIComponent("" + containerId));
        if (agendaId === undefined || agendaId === null)
            throw new Error("The parameter 'agendaId' must be defined.");
        url_ = url_.replace("{agendaId}", encodeURIComponent("" + agendaId));
        if (originalFileId === undefined || originalFileId === null)
            throw new Error("The parameter 'originalFileId' must be defined.");
        url_ = url_.replace("{originalFileId}", encodeURIComponent("" + originalFileId));
        url_ = url_.replace(/[?&]$/, "");

        const content_ = new FormData();
        if (file === null || file === undefined)
            throw new Error("The parameter 'file' cannot be null.");
        else
            content_.append("file", file.data, file.fileName ? file.fileName : "file");

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processReplaceMeetingFileWithNewVersion(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processReplaceMeetingFileWithNewVersion(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processReplaceMeetingFileWithNewVersion(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    sEEDTOREMOVE(domainId: string, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Meeting/seed-TOREMOVE?";
        if (domainId === undefined || domainId === null)
            throw new Error("The parameter 'domainId' must be defined and cannot be null.");
        else
            url_ += "domainId=" + encodeURIComponent("" + domainId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSEEDTOREMOVE(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSEEDTOREMOVE(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processSEEDTOREMOVE(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable({
    providedIn: 'root'
})
export class CurrentUserClient {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "https://localhost:5001";
    }

    get(httpContext?: HttpContext): Observable<GetCurrentUserResponse> {
        let url_ = this.baseUrl + "/api/currentUser/Get";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGet(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGet(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetCurrentUserResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetCurrentUserResponse>;
        }));
    }

    protected processGet(response: HttpResponseBase): Observable<GetCurrentUserResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetCurrentUserResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable({
    providedIn: 'root'
})
export class AppConfigurationClient {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "https://localhost:5001";
    }

    get(httpContext?: HttpContext): Observable<GetAppConfigurationResponse> {
        let url_ = this.baseUrl + "/api/appConfiguration/Get";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGet(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGet(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetAppConfigurationResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetAppConfigurationResponse>;
        }));
    }

    protected processGet(response: HttpResponseBase): Observable<GetAppConfigurationResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetAppConfigurationResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable({
    providedIn: 'root'
})
export class AnnotationClient {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "https://localhost:5001";
    }

    /**
     * Get all annotations for a file.
     */
    getAnnotations(fileMetadataId: string, fileMetadataType: FileMetadataTypeEnum, httpContext?: HttpContext): Observable<GetAnnotationResult[]> {
        let url_ = this.baseUrl + "/api/Annotation/files/{fileMetadataId}/annotations?";
        if (fileMetadataId === undefined || fileMetadataId === null)
            throw new Error("The parameter 'fileMetadataId' must be defined.");
        url_ = url_.replace("{fileMetadataId}", encodeURIComponent("" + fileMetadataId));
        if (fileMetadataType === undefined || fileMetadataType === null)
            throw new Error("The parameter 'fileMetadataType' must be defined and cannot be null.");
        else
            url_ += "fileMetadataType=" + encodeURIComponent("" + fileMetadataType) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetAnnotations(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetAnnotations(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetAnnotationResult[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetAnnotationResult[]>;
        }));
    }

    protected processGetAnnotations(response: HttpResponseBase): Observable<GetAnnotationResult[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(GetAnnotationResult.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Add an annotation to a file.
     */
    addAnnotation(command: AddAnnotationCommand, fileMetadataId: string, fileMetadataType: FileMetadataTypeEnum, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Annotation/files/{fileMetadataId}/annotations?";
        if (fileMetadataId === undefined || fileMetadataId === null)
            throw new Error("The parameter 'fileMetadataId' must be defined.");
        url_ = url_.replace("{fileMetadataId}", encodeURIComponent("" + fileMetadataId));
        if (fileMetadataType === undefined || fileMetadataType === null)
            throw new Error("The parameter 'fileMetadataType' must be defined and cannot be null.");
        else
            url_ += "fileMetadataType=" + encodeURIComponent("" + fileMetadataType) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(command);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAddAnnotation(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAddAnnotation(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processAddAnnotation(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    updateAnnotation(command: UpdateAnnotationCommand, fileMetadataId: string, annotationId: string, fileMetadataType: FileMetadataTypeEnum, httpContext?: HttpContext): Observable<void> {
        let url_ = this.baseUrl + "/api/Annotation/files/{fileMetadataId}/annotations/{annotationId}?";
        if (fileMetadataId === undefined || fileMetadataId === null)
            throw new Error("The parameter 'fileMetadataId' must be defined.");
        url_ = url_.replace("{fileMetadataId}", encodeURIComponent("" + fileMetadataId));
        if (annotationId === undefined || annotationId === null)
            throw new Error("The parameter 'annotationId' must be defined.");
        url_ = url_.replace("{annotationId}", encodeURIComponent("" + annotationId));
        if (fileMetadataType === undefined || fileMetadataType === null)
            throw new Error("The parameter 'fileMetadataType' must be defined and cannot be null.");
        else
            url_ += "fileMetadataType=" + encodeURIComponent("" + fileMetadataType) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(command);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            withCredentials: true,
            context: httpContext,
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateAnnotation(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateAnnotation(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUpdateAnnotation(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export class CreateUserVoteCommand implements ICreateUserVoteCommand {
    vote!: string;
    comment?: string | null;

    constructor(data?: ICreateUserVoteCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.vote = _data["vote"] !== undefined ? _data["vote"] : <any>null;
            this.comment = _data["comment"] !== undefined ? _data["comment"] : <any>null;
        }
    }

    static fromJS(data: any): CreateUserVoteCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateUserVoteCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["vote"] = this.vote !== undefined ? this.vote : <any>null;
        data["comment"] = this.comment !== undefined ? this.comment : <any>null;
        return data;
    }
}

export interface ICreateUserVoteCommand {
    vote: string;
    comment?: string | null;
}

export class GetVotingDetailResponse implements IGetVotingDetailResponse {
    id!: string;
    name!: string;
    description?: string | null;
    state!: ContainerStateEnum;
    agendas!: GetVotingAgendaResponse[];

    constructor(data?: IGetVotingDetailResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.agendas = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
            this.state = _data["state"] !== undefined ? _data["state"] : <any>null;
            if (Array.isArray(_data["agendas"])) {
                this.agendas = [] as any;
                for (let item of _data["agendas"])
                    this.agendas!.push(GetVotingAgendaResponse.fromJS(item));
            }
            else {
                this.agendas = <any>null;
            }
        }
    }

    static fromJS(data: any): GetVotingDetailResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetVotingDetailResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        data["state"] = this.state !== undefined ? this.state : <any>null;
        if (Array.isArray(this.agendas)) {
            data["agendas"] = [];
            for (let item of this.agendas)
                data["agendas"].push(item.toJSON());
        }
        return data;
    }
}

export interface IGetVotingDetailResponse {
    id: string;
    name: string;
    description?: string | null;
    state: ContainerStateEnum;
    agendas: GetVotingAgendaResponse[];
}

export enum ContainerStateEnum {
    Archived = "Archived",
    Deleted = "Deleted",
    Draft = "Draft",
    Published = "Published",
    PastMeeting = "PastMeeting",
}

export class GetVotingAgendaResponse implements IGetVotingAgendaResponse {
    id!: string;
    name!: string;
    description?: string | null;
    order!: number;

    constructor(data?: IGetVotingAgendaResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
            this.order = _data["order"] !== undefined ? _data["order"] : <any>null;
        }
    }

    static fromJS(data: any): GetVotingAgendaResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetVotingAgendaResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        data["order"] = this.order !== undefined ? this.order : <any>null;
        return data;
    }
}

export interface IGetVotingAgendaResponse {
    id: string;
    name: string;
    description?: string | null;
    order: number;
}

export class CreateVotingAgendaCommand implements ICreateVotingAgendaCommand {
    name!: string;
    description!: string;

    constructor(data?: ICreateVotingAgendaCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
        }
    }

    static fromJS(data: any): CreateVotingAgendaCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateVotingAgendaCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        return data;
    }
}

export interface ICreateVotingAgendaCommand {
    name: string;
    description: string;
}

export class CreateVotingContainerCommand implements ICreateVotingContainerCommand {
    name!: string;
    description!: string;
    domainId!: string;
    defaultVotingOptions!: VotingOptions;

    constructor(data?: ICreateVotingContainerCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.defaultVotingOptions = new VotingOptions();
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
            this.domainId = _data["domainId"] !== undefined ? _data["domainId"] : <any>null;
            this.defaultVotingOptions = _data["defaultVotingOptions"] ? VotingOptions.fromJS(_data["defaultVotingOptions"]) : new VotingOptions();
        }
    }

    static fromJS(data: any): CreateVotingContainerCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateVotingContainerCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        data["domainId"] = this.domainId !== undefined ? this.domainId : <any>null;
        data["defaultVotingOptions"] = this.defaultVotingOptions ? this.defaultVotingOptions.toJSON() : <any>null;
        return data;
    }
}

export interface ICreateVotingContainerCommand {
    name: string;
    description: string;
    domainId: string;
    defaultVotingOptions: VotingOptions;
}

export class VotingOptions implements IVotingOptions {
    options!: string[];

    constructor(data?: IVotingOptions) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.options = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["options"])) {
                this.options = [] as any;
                for (let item of _data["options"])
                    this.options!.push(item);
            }
            else {
                this.options = <any>null;
            }
        }
    }

    static fromJS(data: any): VotingOptions {
        data = typeof data === 'object' ? data : {};
        let result = new VotingOptions();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.options)) {
            data["options"] = [];
            for (let item of this.options)
                data["options"].push(item);
        }
        return data;
    }
}

export interface IVotingOptions {
    options: string[];
}

export class CreateVotingQuestionCommand implements ICreateVotingQuestionCommand {
    question!: string;
    votingOptions!: VotingOptions;
    description?: string | null;

    constructor(data?: ICreateVotingQuestionCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.votingOptions = new VotingOptions();
        }
    }

    init(_data?: any) {
        if (_data) {
            this.question = _data["question"] !== undefined ? _data["question"] : <any>null;
            this.votingOptions = _data["votingOptions"] ? VotingOptions.fromJS(_data["votingOptions"]) : new VotingOptions();
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
        }
    }

    static fromJS(data: any): CreateVotingQuestionCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateVotingQuestionCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["question"] = this.question !== undefined ? this.question : <any>null;
        data["votingOptions"] = this.votingOptions ? this.votingOptions.toJSON() : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        return data;
    }
}

export interface ICreateVotingQuestionCommand {
    question: string;
    votingOptions: VotingOptions;
    description?: string | null;
}

export class GetAdminDashboardCurrentDataResponse implements IGetAdminDashboardCurrentDataResponse {
    meetings!: GetAdminMeetingResponse[];
    votings!: GetAdminVotingResponse[];

    constructor(data?: IGetAdminDashboardCurrentDataResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.meetings = [];
            this.votings = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["meetings"])) {
                this.meetings = [] as any;
                for (let item of _data["meetings"])
                    this.meetings!.push(GetAdminMeetingResponse.fromJS(item));
            }
            else {
                this.meetings = <any>null;
            }
            if (Array.isArray(_data["votings"])) {
                this.votings = [] as any;
                for (let item of _data["votings"])
                    this.votings!.push(GetAdminVotingResponse.fromJS(item));
            }
            else {
                this.votings = <any>null;
            }
        }
    }

    static fromJS(data: any): GetAdminDashboardCurrentDataResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetAdminDashboardCurrentDataResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.meetings)) {
            data["meetings"] = [];
            for (let item of this.meetings)
                data["meetings"].push(item.toJSON());
        }
        if (Array.isArray(this.votings)) {
            data["votings"] = [];
            for (let item of this.votings)
                data["votings"].push(item.toJSON());
        }
        return data;
    }
}

export interface IGetAdminDashboardCurrentDataResponse {
    meetings: GetAdminMeetingResponse[];
    votings: GetAdminVotingResponse[];
}

export abstract class GetContainerResponseBase implements IGetContainerResponseBase {
    containerType!: ContainerTypeEnum;
    domain!: GetDomainResponse;
    containerId!: string;
    title!: string;
    startTime?: Date | null;
    state!: ContainerStateEnum;

    constructor(data?: IGetContainerResponseBase) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.domain = new GetDomainResponse();
        }
    }

    init(_data?: any) {
        if (_data) {
            this.containerType = _data["containerType"] !== undefined ? _data["containerType"] : <any>null;
            this.domain = _data["domain"] ? GetDomainResponse.fromJS(_data["domain"]) : new GetDomainResponse();
            this.containerId = _data["containerId"] !== undefined ? _data["containerId"] : <any>null;
            this.title = _data["title"] !== undefined ? _data["title"] : <any>null;
            this.startTime = _data["startTime"] ? new Date(_data["startTime"].toString()) : <any>null;
            this.state = _data["state"] !== undefined ? _data["state"] : <any>null;
        }
    }

    static fromJS(data: any): GetContainerResponseBase {
        data = typeof data === 'object' ? data : {};
        throw new Error("The abstract class 'GetContainerResponseBase' cannot be instantiated.");
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["containerType"] = this.containerType !== undefined ? this.containerType : <any>null;
        data["domain"] = this.domain ? this.domain.toJSON() : <any>null;
        data["containerId"] = this.containerId !== undefined ? this.containerId : <any>null;
        data["title"] = this.title !== undefined ? this.title : <any>null;
        data["startTime"] = this.startTime ? this.startTime.toISOString() : <any>null;
        data["state"] = this.state !== undefined ? this.state : <any>null;
        return data;
    }
}

export interface IGetContainerResponseBase {
    containerType: ContainerTypeEnum;
    domain: GetDomainResponse;
    containerId: string;
    title: string;
    startTime?: Date | null;
    state: ContainerStateEnum;
}

export class GetAdminMeetingResponse extends GetContainerResponseBase implements IGetAdminMeetingResponse {
    location?: string | null;

    constructor(data?: IGetAdminMeetingResponse) {
        super(data);
    }

    override init(_data?: any) {
        super.init(_data);
        if (_data) {
            this.location = _data["location"] !== undefined ? _data["location"] : <any>null;
        }
    }

    static override fromJS(data: any): GetAdminMeetingResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetAdminMeetingResponse();
        result.init(data);
        return result;
    }

    override toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["location"] = this.location !== undefined ? this.location : <any>null;
        super.toJSON(data);
        return data;
    }
}

export interface IGetAdminMeetingResponse extends IGetContainerResponseBase {
    location?: string | null;
}

export enum ContainerTypeEnum {
    MeetingContainer = "MeetingContainer",
    VotingContainer = "VotingContainer",
    DocumentContainer = "DocumentContainer",
}

export class GetDomainResponse implements IGetDomainResponse {
    id!: string;
    parent?: GetDomainResponse | null;
    name!: string;

    constructor(data?: IGetDomainResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.parent = _data["parent"] ? GetDomainResponse.fromJS(_data["parent"]) : <any>null;
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
        }
    }

    static fromJS(data: any): GetDomainResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetDomainResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["parent"] = this.parent ? this.parent.toJSON() : <any>null;
        data["name"] = this.name !== undefined ? this.name : <any>null;
        return data;
    }
}

export interface IGetDomainResponse {
    id: string;
    parent?: GetDomainResponse | null;
    name: string;
}

export class GetAdminVotingResponse extends GetContainerResponseBase implements IGetAdminVotingResponse {
    totalUsersWithVotingRights!: number;
    totalUsersVoted!: number;

    constructor(data?: IGetAdminVotingResponse) {
        super(data);
    }

    override init(_data?: any) {
        super.init(_data);
        if (_data) {
            this.totalUsersWithVotingRights = _data["totalUsersWithVotingRights"] !== undefined ? _data["totalUsersWithVotingRights"] : <any>null;
            this.totalUsersVoted = _data["totalUsersVoted"] !== undefined ? _data["totalUsersVoted"] : <any>null;
        }
    }

    static override fromJS(data: any): GetAdminVotingResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetAdminVotingResponse();
        result.init(data);
        return result;
    }

    override toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["totalUsersWithVotingRights"] = this.totalUsersWithVotingRights !== undefined ? this.totalUsersWithVotingRights : <any>null;
        data["totalUsersVoted"] = this.totalUsersVoted !== undefined ? this.totalUsersVoted : <any>null;
        super.toJSON(data);
        return data;
    }
}

export interface IGetAdminVotingResponse extends IGetContainerResponseBase {
    totalUsersWithVotingRights: number;
    totalUsersVoted: number;
}

export class GetAdminDashboardPastDataResponse implements IGetAdminDashboardPastDataResponse {
    meetings!: GetAdminMeetingResponse[];
    votings!: GetAdminVotingResponse[];

    constructor(data?: IGetAdminDashboardPastDataResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.meetings = [];
            this.votings = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["meetings"])) {
                this.meetings = [] as any;
                for (let item of _data["meetings"])
                    this.meetings!.push(GetAdminMeetingResponse.fromJS(item));
            }
            else {
                this.meetings = <any>null;
            }
            if (Array.isArray(_data["votings"])) {
                this.votings = [] as any;
                for (let item of _data["votings"])
                    this.votings!.push(GetAdminVotingResponse.fromJS(item));
            }
            else {
                this.votings = <any>null;
            }
        }
    }

    static fromJS(data: any): GetAdminDashboardPastDataResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetAdminDashboardPastDataResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.meetings)) {
            data["meetings"] = [];
            for (let item of this.meetings)
                data["meetings"].push(item.toJSON());
        }
        if (Array.isArray(this.votings)) {
            data["votings"] = [];
            for (let item of this.votings)
                data["votings"].push(item.toJSON());
        }
        return data;
    }
}

export interface IGetAdminDashboardPastDataResponse {
    meetings: GetAdminMeetingResponse[];
    votings: GetAdminVotingResponse[];
}

export class MeetingFileMetadataResponse implements IMeetingFileMetadataResponse {
    id!: string;
    fileName!: string;

    constructor(data?: IMeetingFileMetadataResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.fileName = _data["fileName"] !== undefined ? _data["fileName"] : <any>null;
        }
    }

    static fromJS(data: any): MeetingFileMetadataResponse {
        data = typeof data === 'object' ? data : {};
        let result = new MeetingFileMetadataResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["fileName"] = this.fileName !== undefined ? this.fileName : <any>null;
        return data;
    }
}

export interface IMeetingFileMetadataResponse {
    id: string;
    fileName: string;
}

export class GetMeetingDetailResponse implements IGetMeetingDetailResponse {
    id!: string;
    name!: string;
    description?: string | null;
    state!: ContainerStateEnum;
    lastUpdatedOn!: Date;
    agendas!: GetMeetingAgendaResponse[];

    constructor(data?: IGetMeetingDetailResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.agendas = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
            this.state = _data["state"] !== undefined ? _data["state"] : <any>null;
            this.lastUpdatedOn = _data["lastUpdatedOn"] ? new Date(_data["lastUpdatedOn"].toString()) : <any>null;
            if (Array.isArray(_data["agendas"])) {
                this.agendas = [] as any;
                for (let item of _data["agendas"])
                    this.agendas!.push(GetMeetingAgendaResponse.fromJS(item));
            }
            else {
                this.agendas = <any>null;
            }
        }
    }

    static fromJS(data: any): GetMeetingDetailResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetMeetingDetailResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        data["state"] = this.state !== undefined ? this.state : <any>null;
        data["lastUpdatedOn"] = this.lastUpdatedOn ? this.lastUpdatedOn.toISOString() : <any>null;
        if (Array.isArray(this.agendas)) {
            data["agendas"] = [];
            for (let item of this.agendas)
                data["agendas"].push(item.toJSON());
        }
        return data;
    }
}

export interface IGetMeetingDetailResponse {
    id: string;
    name: string;
    description?: string | null;
    state: ContainerStateEnum;
    lastUpdatedOn: Date;
    agendas: GetMeetingAgendaResponse[];
}

export class GetMeetingAgendaResponse implements IGetMeetingAgendaResponse {
    id!: string;
    parentAgendaId?: string | null;
    name!: string;
    description?: string | null;
    order!: number;
    files!: FileMetadataResponse[];
    children!: GetMeetingAgendaResponse[];

    constructor(data?: IGetMeetingAgendaResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.files = [];
            this.children = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.parentAgendaId = _data["parentAgendaId"] !== undefined ? _data["parentAgendaId"] : <any>null;
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
            this.order = _data["order"] !== undefined ? _data["order"] : <any>null;
            if (Array.isArray(_data["files"])) {
                this.files = [] as any;
                for (let item of _data["files"])
                    this.files!.push(FileMetadataResponse.fromJS(item));
            }
            else {
                this.files = <any>null;
            }
            if (Array.isArray(_data["children"])) {
                this.children = [] as any;
                for (let item of _data["children"])
                    this.children!.push(GetMeetingAgendaResponse.fromJS(item));
            }
            else {
                this.children = <any>null;
            }
        }
    }

    static fromJS(data: any): GetMeetingAgendaResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetMeetingAgendaResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["parentAgendaId"] = this.parentAgendaId !== undefined ? this.parentAgendaId : <any>null;
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        data["order"] = this.order !== undefined ? this.order : <any>null;
        if (Array.isArray(this.files)) {
            data["files"] = [];
            for (let item of this.files)
                data["files"].push(item.toJSON());
        }
        if (Array.isArray(this.children)) {
            data["children"] = [];
            for (let item of this.children)
                data["children"].push(item.toJSON());
        }
        return data;
    }
}

export interface IGetMeetingAgendaResponse {
    id: string;
    parentAgendaId?: string | null;
    name: string;
    description?: string | null;
    order: number;
    files: FileMetadataResponse[];
    children: GetMeetingAgendaResponse[];
}

export class FileMetadataResponse implements IFileMetadataResponse {
    id!: string;
    displayName!: string;
    extension!: string;
    supplementary!: boolean;
    versionNumber!: number;
    isLatest!: boolean;
    order!: number;
    size!: number;
    numberOfPages!: number;
    isRead!: boolean;

    constructor(data?: IFileMetadataResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.displayName = _data["displayName"] !== undefined ? _data["displayName"] : <any>null;
            this.extension = _data["extension"] !== undefined ? _data["extension"] : <any>null;
            this.supplementary = _data["supplementary"] !== undefined ? _data["supplementary"] : <any>null;
            this.versionNumber = _data["versionNumber"] !== undefined ? _data["versionNumber"] : <any>null;
            this.isLatest = _data["isLatest"] !== undefined ? _data["isLatest"] : <any>null;
            this.order = _data["order"] !== undefined ? _data["order"] : <any>null;
            this.size = _data["size"] !== undefined ? _data["size"] : <any>null;
            this.numberOfPages = _data["numberOfPages"] !== undefined ? _data["numberOfPages"] : <any>null;
            this.isRead = _data["isRead"] !== undefined ? _data["isRead"] : <any>null;
        }
    }

    static fromJS(data: any): FileMetadataResponse {
        data = typeof data === 'object' ? data : {};
        let result = new FileMetadataResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["displayName"] = this.displayName !== undefined ? this.displayName : <any>null;
        data["extension"] = this.extension !== undefined ? this.extension : <any>null;
        data["supplementary"] = this.supplementary !== undefined ? this.supplementary : <any>null;
        data["versionNumber"] = this.versionNumber !== undefined ? this.versionNumber : <any>null;
        data["isLatest"] = this.isLatest !== undefined ? this.isLatest : <any>null;
        data["order"] = this.order !== undefined ? this.order : <any>null;
        data["size"] = this.size !== undefined ? this.size : <any>null;
        data["numberOfPages"] = this.numberOfPages !== undefined ? this.numberOfPages : <any>null;
        data["isRead"] = this.isRead !== undefined ? this.isRead : <any>null;
        return data;
    }
}

export interface IFileMetadataResponse {
    id: string;
    displayName: string;
    extension: string;
    supplementary: boolean;
    versionNumber: number;
    isLatest: boolean;
    order: number;
    size: number;
    numberOfPages: number;
    isRead: boolean;
}

export class GetUserDashboardCurrentDataResponse implements IGetUserDashboardCurrentDataResponse {
    meetings!: GetUserMeetingResponse[];
    votings!: GetUserVotingResponse[];

    constructor(data?: IGetUserDashboardCurrentDataResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.meetings = [];
            this.votings = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["meetings"])) {
                this.meetings = [] as any;
                for (let item of _data["meetings"])
                    this.meetings!.push(GetUserMeetingResponse.fromJS(item));
            }
            else {
                this.meetings = <any>null;
            }
            if (Array.isArray(_data["votings"])) {
                this.votings = [] as any;
                for (let item of _data["votings"])
                    this.votings!.push(GetUserVotingResponse.fromJS(item));
            }
            else {
                this.votings = <any>null;
            }
        }
    }

    static fromJS(data: any): GetUserDashboardCurrentDataResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetUserDashboardCurrentDataResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.meetings)) {
            data["meetings"] = [];
            for (let item of this.meetings)
                data["meetings"].push(item.toJSON());
        }
        if (Array.isArray(this.votings)) {
            data["votings"] = [];
            for (let item of this.votings)
                data["votings"].push(item.toJSON());
        }
        return data;
    }
}

export interface IGetUserDashboardCurrentDataResponse {
    meetings: GetUserMeetingResponse[];
    votings: GetUserVotingResponse[];
}

export class GetUserMeetingResponse extends GetContainerResponseBase implements IGetUserMeetingResponse {
    totalDocuments!: number;
    readDocuments!: number;
    unreadDocuments!: number;
    location?: string | null;

    constructor(data?: IGetUserMeetingResponse) {
        super(data);
    }

    override init(_data?: any) {
        super.init(_data);
        if (_data) {
            this.totalDocuments = _data["totalDocuments"] !== undefined ? _data["totalDocuments"] : <any>null;
            this.readDocuments = _data["readDocuments"] !== undefined ? _data["readDocuments"] : <any>null;
            this.unreadDocuments = _data["unreadDocuments"] !== undefined ? _data["unreadDocuments"] : <any>null;
            this.location = _data["location"] !== undefined ? _data["location"] : <any>null;
        }
    }

    static override fromJS(data: any): GetUserMeetingResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetUserMeetingResponse();
        result.init(data);
        return result;
    }

    override toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["totalDocuments"] = this.totalDocuments !== undefined ? this.totalDocuments : <any>null;
        data["readDocuments"] = this.readDocuments !== undefined ? this.readDocuments : <any>null;
        data["unreadDocuments"] = this.unreadDocuments !== undefined ? this.unreadDocuments : <any>null;
        data["location"] = this.location !== undefined ? this.location : <any>null;
        super.toJSON(data);
        return data;
    }
}

export interface IGetUserMeetingResponse extends IGetContainerResponseBase {
    totalDocuments: number;
    readDocuments: number;
    unreadDocuments: number;
    location?: string | null;
}

export class GetUserVotingResponse extends GetContainerResponseBase implements IGetUserVotingResponse {
    totalQuestions!: number;
    totalUserVotes!: number;
    unvotedQuestions!: number;

    constructor(data?: IGetUserVotingResponse) {
        super(data);
    }

    override init(_data?: any) {
        super.init(_data);
        if (_data) {
            this.totalQuestions = _data["totalQuestions"] !== undefined ? _data["totalQuestions"] : <any>null;
            this.totalUserVotes = _data["totalUserVotes"] !== undefined ? _data["totalUserVotes"] : <any>null;
            this.unvotedQuestions = _data["unvotedQuestions"] !== undefined ? _data["unvotedQuestions"] : <any>null;
        }
    }

    static override fromJS(data: any): GetUserVotingResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetUserVotingResponse();
        result.init(data);
        return result;
    }

    override toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["totalQuestions"] = this.totalQuestions !== undefined ? this.totalQuestions : <any>null;
        data["totalUserVotes"] = this.totalUserVotes !== undefined ? this.totalUserVotes : <any>null;
        data["unvotedQuestions"] = this.unvotedQuestions !== undefined ? this.unvotedQuestions : <any>null;
        super.toJSON(data);
        return data;
    }
}

export interface IGetUserVotingResponse extends IGetContainerResponseBase {
    totalQuestions: number;
    totalUserVotes: number;
    unvotedQuestions: number;
}

export class GetUserDashboardPastDataResponse implements IGetUserDashboardPastDataResponse {
    meetings!: GetUserMeetingResponse[];
    votings!: GetUserVotingResponse[];

    constructor(data?: IGetUserDashboardPastDataResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.meetings = [];
            this.votings = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["meetings"])) {
                this.meetings = [] as any;
                for (let item of _data["meetings"])
                    this.meetings!.push(GetUserMeetingResponse.fromJS(item));
            }
            else {
                this.meetings = <any>null;
            }
            if (Array.isArray(_data["votings"])) {
                this.votings = [] as any;
                for (let item of _data["votings"])
                    this.votings!.push(GetUserVotingResponse.fromJS(item));
            }
            else {
                this.votings = <any>null;
            }
        }
    }

    static fromJS(data: any): GetUserDashboardPastDataResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetUserDashboardPastDataResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.meetings)) {
            data["meetings"] = [];
            for (let item of this.meetings)
                data["meetings"].push(item.toJSON());
        }
        if (Array.isArray(this.votings)) {
            data["votings"] = [];
            for (let item of this.votings)
                data["votings"].push(item.toJSON());
        }
        return data;
    }
}

export interface IGetUserDashboardPastDataResponse {
    meetings: GetUserMeetingResponse[];
    votings: GetUserVotingResponse[];
}

export class CreateMeetingContainerCommand implements ICreateMeetingContainerCommand {
    name!: string;
    description!: string;
    domainId!: string;
    agendas!: CreateMeetingAgendaCommand[];

    constructor(data?: ICreateMeetingContainerCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.agendas = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
            this.domainId = _data["domainId"] !== undefined ? _data["domainId"] : <any>null;
            if (Array.isArray(_data["agendas"])) {
                this.agendas = [] as any;
                for (let item of _data["agendas"])
                    this.agendas!.push(CreateMeetingAgendaCommand.fromJS(item));
            }
            else {
                this.agendas = <any>null;
            }
        }
    }

    static fromJS(data: any): CreateMeetingContainerCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateMeetingContainerCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        data["domainId"] = this.domainId !== undefined ? this.domainId : <any>null;
        if (Array.isArray(this.agendas)) {
            data["agendas"] = [];
            for (let item of this.agendas)
                data["agendas"].push(item.toJSON());
        }
        return data;
    }
}

export interface ICreateMeetingContainerCommand {
    name: string;
    description: string;
    domainId: string;
    agendas: CreateMeetingAgendaCommand[];
}

export class CreateMeetingAgendaCommand implements ICreateMeetingAgendaCommand {
    name!: string;
    description?: string | null;
    order!: number;
    children!: CreateMeetingAgendaCommand[];

    constructor(data?: ICreateMeetingAgendaCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
        if (!data) {
            this.children = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"] !== undefined ? _data["name"] : <any>null;
            this.description = _data["description"] !== undefined ? _data["description"] : <any>null;
            this.order = _data["order"] !== undefined ? _data["order"] : <any>null;
            if (Array.isArray(_data["children"])) {
                this.children = [] as any;
                for (let item of _data["children"])
                    this.children!.push(CreateMeetingAgendaCommand.fromJS(item));
            }
            else {
                this.children = <any>null;
            }
        }
    }

    static fromJS(data: any): CreateMeetingAgendaCommand {
        data = typeof data === 'object' ? data : {};
        let result = new CreateMeetingAgendaCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name !== undefined ? this.name : <any>null;
        data["description"] = this.description !== undefined ? this.description : <any>null;
        data["order"] = this.order !== undefined ? this.order : <any>null;
        if (Array.isArray(this.children)) {
            data["children"] = [];
            for (let item of this.children)
                data["children"].push(item.toJSON());
        }
        return data;
    }
}

export interface ICreateMeetingAgendaCommand {
    name: string;
    description?: string | null;
    order: number;
    children: CreateMeetingAgendaCommand[];
}

export enum AgendaPermissionEnum {
    Manage = 1,
    Read = 2,
    Write = 4,
    Deny = 1024,
}

export class GetCurrentUserResponse implements IGetCurrentUserResponse {
    fullName!: string;

    constructor(data?: IGetCurrentUserResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.fullName = _data["fullName"] !== undefined ? _data["fullName"] : <any>null;
        }
    }

    static fromJS(data: any): GetCurrentUserResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetCurrentUserResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["fullName"] = this.fullName !== undefined ? this.fullName : <any>null;
        return data;
    }
}

export interface IGetCurrentUserResponse {
    fullName: string;
}

export class GetAppConfigurationResponse implements IGetAppConfigurationResponse {
    agGridLicenseKey!: string;

    constructor(data?: IGetAppConfigurationResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.agGridLicenseKey = _data["agGridLicenseKey"] !== undefined ? _data["agGridLicenseKey"] : <any>null;
        }
    }

    static fromJS(data: any): GetAppConfigurationResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetAppConfigurationResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["agGridLicenseKey"] = this.agGridLicenseKey !== undefined ? this.agGridLicenseKey : <any>null;
        return data;
    }
}

export interface IGetAppConfigurationResponse {
    agGridLicenseKey: string;
}

export class GetAnnotationResult implements IGetAnnotationResult {
    id!: string;
    annotationUniqueId!: string;
    annotationXml!: string;

    constructor(data?: IGetAnnotationResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : <any>null;
            this.annotationUniqueId = _data["annotationUniqueId"] !== undefined ? _data["annotationUniqueId"] : <any>null;
            this.annotationXml = _data["annotationXml"] !== undefined ? _data["annotationXml"] : <any>null;
        }
    }

    static fromJS(data: any): GetAnnotationResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetAnnotationResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : <any>null;
        data["annotationUniqueId"] = this.annotationUniqueId !== undefined ? this.annotationUniqueId : <any>null;
        data["annotationXml"] = this.annotationXml !== undefined ? this.annotationXml : <any>null;
        return data;
    }
}

export interface IGetAnnotationResult {
    id: string;
    annotationUniqueId: string;
    annotationXml: string;
}

export enum FileMetadataTypeEnum {
    Meetings = "Meetings",
    Documents = "Documents",
}

export class AddAnnotationCommand implements IAddAnnotationCommand {
    annotationId!: string;
    annotationPdftronGeneratedId!: string;
    annotationXml!: string;

    constructor(data?: IAddAnnotationCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.annotationId = _data["annotationId"] !== undefined ? _data["annotationId"] : <any>null;
            this.annotationPdftronGeneratedId = _data["annotationPdftronGeneratedId"] !== undefined ? _data["annotationPdftronGeneratedId"] : <any>null;
            this.annotationXml = _data["annotationXml"] !== undefined ? _data["annotationXml"] : <any>null;
        }
    }

    static fromJS(data: any): AddAnnotationCommand {
        data = typeof data === 'object' ? data : {};
        let result = new AddAnnotationCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["annotationId"] = this.annotationId !== undefined ? this.annotationId : <any>null;
        data["annotationPdftronGeneratedId"] = this.annotationPdftronGeneratedId !== undefined ? this.annotationPdftronGeneratedId : <any>null;
        data["annotationXml"] = this.annotationXml !== undefined ? this.annotationXml : <any>null;
        return data;
    }
}

export interface IAddAnnotationCommand {
    annotationId: string;
    annotationPdftronGeneratedId: string;
    annotationXml: string;
}

export class UpdateAnnotationCommand implements IUpdateAnnotationCommand {
    annotationXml!: string;

    constructor(data?: IUpdateAnnotationCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.annotationXml = _data["annotationXml"] !== undefined ? _data["annotationXml"] : <any>null;
        }
    }

    static fromJS(data: any): UpdateAnnotationCommand {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateAnnotationCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["annotationXml"] = this.annotationXml !== undefined ? this.annotationXml : <any>null;
        return data;
    }
}

export interface IUpdateAnnotationCommand {
    annotationXml: string;
}

export interface FileParameter {
    data: any;
    fileName: string;
}

export class ApiException extends Error {
    override message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}