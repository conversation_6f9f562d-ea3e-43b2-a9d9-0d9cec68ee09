﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class MeetingFileMetadataConfiguration : FileMetadataBaseConfiguration<MeetingFileMetadata>
    {
        public override void Configure(EntityTypeBuilder<MeetingFileMetadata> builder)
        {
            builder.ToTable("MeetingFileMetadatas");
            base.Configure(builder);
        }
    }
}
