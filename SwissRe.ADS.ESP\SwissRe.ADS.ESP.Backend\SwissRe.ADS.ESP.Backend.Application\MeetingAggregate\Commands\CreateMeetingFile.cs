﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using Microsoft.AspNetCore.Builder;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.PDFTron;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Commands
{
    public class CreateMeetingFileEndpoint(UnitOfWork unitOfWork, 
        IAgendaAuthorizationService agendaPermissionService,
        IPdfPageCounterService pdfPageCounterService,
        ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly IPdfPageCounterService _pdfPageCounterService = pdfPageCounterService;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapPost("/containers/{containerId}/agendas/{agendaId}/files", async (Guid containerId, Guid agendaId, IFormFile file, CreateMeetingFileEndpoint endpoint) =>
                    await endpoint.HandleAsync(containerId, agendaId, file))
                .WithSummary("Upload a new file to a meeting agenda.")
                .WithDescription("Uploads a new file to the specified agenda within a meeting container. Requires manage permission on the agenda. The file is processed to count PDF pages and store metadata.")
                .DisableAntiforgery()
                .WithAngularName<MeetingContainerRouteGroup>("CreateMeetingFile");

        public async Task HandleAsync(Guid containerId, Guid agendaId, IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File cannot be null or empty", nameof(file));

            var canManageAgenda = await _agendaPermissionService.CanUserManageAgendaAsync(containerId, agendaId, AgendaTypeEnum.MeetingAgenda);
            if (!canManageAgenda)
                throw new UnauthorizedAccessException("Current user does not have permission to add a file to agenda");

            var container = await _unitOfWork.Repository<MeetingContainer>().GetAsync(containerId, true);
            using var stream = new MemoryStream();
            file.CopyTo(stream);
            stream.Position = 0;

            var numberOfPages = _pdfPageCounterService.GetTotalPages(stream);
            var fileExtension = Path.GetExtension(file.FileName) ?? "";
            if (string.IsNullOrWhiteSpace(fileExtension))
                throw new InvalidOperationException("File extension cannot be empty");


            container.AddNewFile(
                agendaId,
                stream.ToArray(),
                file.FileName,
                fileExtension,
                (int)file.Length,
                0,
                numberOfPages,
                _currentUser.SrUserId,
                false
            );

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
