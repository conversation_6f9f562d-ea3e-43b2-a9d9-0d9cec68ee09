import { Routes } from '@angular/router';
import { Error500Component } from './shared/global-error-handling/error-500/error-500.component';
import { error500CanActivate } from './shared/global-error-handling/error-500/error-500-can-activate';
import { MeetingsComponent } from './pages/meetings/meetings.component';
import { DocumentsComponent } from './pages/documents/documents.component';
import { FeedbackComponent } from './pages/feedback/feedback.component';
import { NewMeetingComponent } from './pages/admin/new-meeting/new-meeting.component';
import { NewResolutionComponent } from './pages/admin/new-resolution/new-resolution.component';
import { adminGuard } from './shared/admin.guard';

export const routes: Routes = [
  { path: '', pathMatch: 'full', redirectTo: 'meetings' },
  { path: 'meetings', component: MeetingsComponent },
  { path: 'documents', component: DocumentsComponent },
  { path: 'feedback', component: FeedbackComponent },
  {
    path: 'admin',
    children: [
      { path: 'new-meeting', component: NewMeetingComponent },
      { path: 'new-resolution', component: NewResolutionComponent }
    ],
    canActivate: [adminGuard]
  },
  { path: 'error', component: Error500Component, canActivate: [error500CanActivate] }
];
