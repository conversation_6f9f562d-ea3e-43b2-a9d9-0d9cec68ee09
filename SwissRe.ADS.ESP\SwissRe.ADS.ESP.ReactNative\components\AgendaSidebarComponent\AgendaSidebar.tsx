import React from 'react';
import { View, ScrollView, StyleSheet, ViewStyle, useWindowDimensions } from 'react-native';
import AgendaSidebarItem from './AgendaSidebarItem';
import { GetMeetingAgendaResponse } from '@/api/apiServices';
import { useEspAppTheme } from '@/hooks/useEspTheme';

const AgendaSidebar = ({ agendas, onSelectAgenda, activeAgendaId }: { agendas: GetMeetingAgendaResponse[]; onSelectAgenda: (_agendaId: string) => void; activeAgendaId: string | null }) => {
  const theme = useEspAppTheme();
  const { width, height } = useWindowDimensions();

  const isPortrait = height >= width;

  const containerStyle: ViewStyle = {
    backgroundColor: '#7878801F',
    width: isPortrait ? 240 : 300,
    borderLeftColor: '#78788014',
    borderLeftWidth: 2,
  };

  return (
    <View style={containerStyle}>
      <ScrollView contentContainerStyle={styles.scroll}>
        {agendas.map((agenda) => (
          <AgendaSidebarItem key={agenda.id} agenda={agenda} onSelect={onSelectAgenda} activeAgendaId={activeAgendaId} />
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  scroll: {
    paddingVertical: 10,
  },
});

export default AgendaSidebar;
