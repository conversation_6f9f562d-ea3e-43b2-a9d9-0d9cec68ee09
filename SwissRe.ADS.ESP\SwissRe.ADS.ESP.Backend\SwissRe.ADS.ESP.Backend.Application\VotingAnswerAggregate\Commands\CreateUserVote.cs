﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using Microsoft.AspNetCore.Http;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAnswerAggregate.Commands
{
    public record CreateUserVoteCommand(string vote, string? comment);
    public class CreateUserVoteEndpoint(UnitOfWork unitOfWork,
        IAgendaAuthorizationService agendaPermissionService,
        IEspEncryptionService espEncryptionService,  
        ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly IEspEncryptionService _espEncryptionService = espEncryptionService;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<VotingAnswerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapPost("/questions/{questionId}/votes", (CreateUserVoteCommand command, Guid questionId, CreateUserVoteEndpoint endpoint) => endpoint.HandleAsync(command, questionId))
                .WithSummary("Submit a vote for a specific voting question.")
                .WithDescription("Allows the current user to submit a vote and optional comment for a specific voting question. Requires permission to vote on the question.")
                .WithAngularName<VotingAnswerRouteGroup>("CreateUserVote");

        public async Task HandleAsync(CreateUserVoteCommand command, Guid questionId)
        {
            var questionContainer = await _unitOfWork.Repository<VotingContainer>().GetFirstOrNullAsync(
                                                containers => containers.SelectMany(x => x.Agendas)
                                                                        .Where(a => a.VotingQuestions.Any(o => o.Id == questionId))
                                                                        .Select(result => new { ContainerId = result.ParentContainerId, AgendaId = result.Id }));

            if (questionContainer is null)
                throw new AggregateRootNotFoundException<VotingContainer>();

            var canUserCastVote = await _agendaPermissionService.CanUserSubmitVoteAsync(questionContainer.ContainerId, questionContainer.AgendaId, AgendaTypeEnum.VotingAgenda);
            if (canUserCastVote == false)
                throw new UnauthorizedAccessException("Current user does not have permission to cast a vote for this question.");

            var previousUserAnswer = await _unitOfWork.Repository<VotingAnswer>().GetFirstOrNullAsync(answers => answers.Where(x => x.VotingQuestionId == questionId && x.IsLatest == true), true);
            if (previousUserAnswer is not null)
                previousUserAnswer.MarkAnswerAsHistorical();

            //TODO: Validate if current answer aligns with VotingQUestionAvailableOptions

            // Encrypt the vote and comment
            var encryptedVote = _espEncryptionService.Encrypt(command.vote);
            var encryptedComment = command.comment is not null ? _espEncryptionService.Encrypt(command.comment) : null;

            var answer = VotingAnswer.Create(questionId, encryptedVote, encryptedComment, _currentUser.SrUserId);
            _unitOfWork.Repository<VotingAnswer>().Insert(answer);

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
