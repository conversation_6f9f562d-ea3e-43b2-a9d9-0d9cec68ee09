import { Environment } from './environment-enum';

const cookieName = 'FrontendMetadata'; // name of the cookie to retrieve metadata from

const cookieValueJson = document.cookie?.match(new RegExp(`${cookieName}\\s*=\\s*([^;\\s]+)`))?.[1];
const cookieValue: CookieValue | undefined = cookieValueJson && JSON.parse(decodeURIComponent(cookieValueJson));

interface CookieValue {
  environmentName?: string | null;
  appVersion?: string | null;
  environment?: string | null;
}

/**
 * Static metadata such as environment name, version etc. sent from authproxy or anything that serves this FE.
 *
 * Intended usage:
 *  1. Setup FrontendMetedata feature on authproxy to send any metadata JSON object you need to FE. The JSON will be sent via a cookie.
 *  2. Frontend (this file) then reads this JSON from the cookie and uses it i.e. to display app version, set the theme, etc.
 *
 * For local development setup correct defaults below (if you don't run authproxy on local).
 */
export const metadata = {
  environmentName: cookieValue?.environmentName?.trim() || 'Dev',
  appVersion: cookieValue?.appVersion?.trim() || undefined,
  environment: toEnvironmentEnum(cookieValue?.environment)
};

function toEnvironmentEnum(value: string | undefined | null): Environment {
  switch (value?.trim().toLowerCase()) {
    case 'prod':
    case 'production':
      return Environment.PROD;
    case 'np':
    case 'nonprod':
    case 'non-prod':
      return Environment.NP;
    case 'dev':
    case 'development':
    default:
      return Environment.DEV;
  }
}
