import { ColumnSchema } from '../types/ColumnMapping';

export function serializeDatabaseEntity<T>(entity: T, schema: ColumnSchema<T>): Record<string, any> {
  const dbRow: Record<string, any> = {};

  for (const key in schema) {
    const { column, type } = schema[key as keyof T];
    const value = entity[key as keyof T];

    switch (type) {
      case 'string':
        dbRow[column] = value as string;
        break;
      case 'optional-string':
        dbRow[column] = value ?? null;
        break;
      case 'number':
        dbRow[column] = value as number;
        break;
      case 'optional-number':
        dbRow[column] = value ?? null;
        break;
      case 'date':
        dbRow[column] = Math.floor((value as Date).getTime() / 1000);
        break;
      case 'optional-date':
        dbRow[column] = value instanceof Date ? Math.floor(value.getTime() / 1000) : null;
        break;
    }
  }

  return dbRow;
}

export function deserializeDatabaseEntity<T>(row: Record<string, any>, schema: ColumnSchema<T>): T {
  const entity = {} as T;

  for (const key in schema) {
    const { column, type } = schema[key as keyof T];
    const value = row[column];

    switch (type) {
      case 'string':
        (entity as any)[key] = value as string;
        break;
      case 'optional-string':
        (entity as any)[key] = value ?? undefined;
        break;
      case 'number':
        (entity as any)[key] = value as number;
        break;
      case 'optional-number':
        (entity as any)[key] = value ?? undefined;
        break;
      case 'date':
        (entity as any)[key] = new Date((value as number) * 1000);
        break;
      case 'optional-date':
        (entity as any)[key] = value !== null && value !== undefined ? new Date((value as number) * 1000) : undefined;
        break;
    }
  }

  return entity;
}
