import { Text, type TextProps, StyleSheet } from 'react-native';

import { useEspAppTheme } from '@/hooks/useEspTheme';

export type ThemedTextProps = TextProps & {
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link' | 'description';
};

export function ThemedText({ style, type = 'default', ...rest }: ThemedTextProps) {
  const theme = useEspAppTheme();
  const color = type === 'link' ? theme.colors.link : type === 'description' ? theme.colors.textColors.secondary : theme.colors.textColors.primary;

  //TODO: link styles with appThemeStyles for fonts
  return (
    <Text
      style={[
        { color },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: 16,
    lineHeight: 24,
  },
  defaultSemiBold: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 24,
  },
  link: {
    fontSize: 16,
    lineHeight: 30,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 32,
  },
});
