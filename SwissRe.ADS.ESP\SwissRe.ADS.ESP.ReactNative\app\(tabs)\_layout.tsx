import { IconSymbol } from '@/components/ui/IconSymbol';
import { Tabs } from 'expo-router';

export default function TabsLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="meetings"
        options={{
          title: 'Meetings',
          tabBarIcon: ({ color }) => <IconSymbol name="calendar" color={color} />,
        }}
      />
      <Tabs.Screen
        name="documents"
        options={{
          title: 'Documents',
          tabBarIcon: ({ color }) => <IconSymbol name="document" color={color} />,
        }}
      />

      {/* Hide details from tab bar */}
      <Tabs.Screen name="meetings/[id]" options={{ href: null }} />
      <Tabs.Screen name="meetings/detail" options={{ href: null }} />
      <Tabs.Screen name="documents/documentDetail" options={{ href: null }} />
    </Tabs>
  );
}
