﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers
{
    public abstract class FileMetadataBaseConfiguration<T> : IEntityTypeConfiguration<T> where T: FileMetadataBase
    {
        public virtual void Configure(EntityTypeBuilder<T> builder)
        {
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.Property(x => x.OriginalFileName).IsRequired().HasMaxLength(512);
            builder.Property(x => x.DisplayFileName).IsRequired().HasMaxLength(512);
            builder.Property(x => x.FileExtension).IsRequired().HasMaxLength(256);
            builder.Property(x => x.FileContentId).IsRequired();

            builder.Property(x => x.CreatedOn).IsRequired();
            builder.Property(x => x.CreatedByUserId).IsRequired().HasMaxLength(128);

            builder.OwnsMany(a => a.ReadStatuses,
                ownedNav =>
                {
                    ownedNav.ToJson("ReadStatuses");
                    ownedNav.Property(p => p.UserId).IsRequired();
                });

            builder.HasOne<User>()
                .WithMany()
                .HasForeignKey(x => x.CreatedByUserId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne<User>()
                .WithMany()
                .HasForeignKey(x => x.LastModifiedByUserId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne<FileContent>()
                .WithOne()
                .HasForeignKey<T>(x => x.FileContentId)
                .OnDelete(DeleteBehavior.Cascade);

            //Ensure 1-1 relationship betweek FileContent and FileMetadata
            builder.HasIndex(fm => fm.FileContentId).IsUnique();
        }
    }
}
