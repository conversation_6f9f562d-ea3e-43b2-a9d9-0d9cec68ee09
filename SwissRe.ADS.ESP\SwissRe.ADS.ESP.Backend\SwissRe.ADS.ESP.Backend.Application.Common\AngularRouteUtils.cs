﻿using Microsoft.AspNetCore.Builder;
using System.Text.RegularExpressions;

namespace SwissRe.ADS.App.Application.Routing
{
    public static partial class AngularRouteUtils
    {
        /// <summary>
        /// Specifies the angular client class name and this endpoint's method name in this class.
        /// Client class name will be extracted from the class name of <typeparamref name="TRouteGroup"/> - everything before 'RouteGroup' suffix.
        /// NSwag generator on frontend has to be configured with 'operationGenerationMode = MultipleClientsFromOperationId'.
        /// </summary>
        public static RouteHandlerBuilder WithAngularName<TRouteGroup>(this RouteHandlerBuilder builder, string methodName) =>
            builder.WithAngularName(GetClientClassName<TRouteGroup>(), methodName);

        /// <summary>
        /// Specifies the angular client class name and this endpoint's method name in this class.
        /// NSwag generator on frontend has to be configured with 'operationGenerationMode = MultipleClientsFromOperationId'.
        /// </summary>
        public static RouteHandlerBuilder WithAngularName(this RouteHandlerBuilder builder, string clientClassName, string methodName) =>
            builder.WithName($"{clientClassName}_{methodName}");

        private static string GetClientClassName<TRouteGroup>()
        {
            var routeGroupType = typeof(TRouteGroup);
            var match = ClientClassNameRegex().Match(typeof(TRouteGroup).Name);

            return match.Success ?
                match.Groups[1].Value :
                throw new InvalidOperationException($"{routeGroupType.FullName} does not follow the required naming convention: {ClientClassNameRegex()}");
        }

        [GeneratedRegex("(.+)(RouteGroup)$")]
        private static partial Regex ClientClassNameRegex();
    }
}
