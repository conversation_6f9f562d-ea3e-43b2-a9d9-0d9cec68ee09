using SwissRe.ADS.ESP.Backend.Application;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.FileContentsAggregate;
using SwissRe.ADS.MinimalApi.Endpoints;

public class FileContentsV1RouteGroup : IRouteGroup
{
    private static int RouteVersion = 1;

    public static IEndpointRouteBuilder? BuildRoute([EndpointRouteBuilder<ApiRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
    {
        var routePrefix = FileContentsAggregateRouteGroupBase.RoutePrefix + RouteVersion;
        return builder.MapGroup(routePrefix).WithTags(routePrefix);
    }
}