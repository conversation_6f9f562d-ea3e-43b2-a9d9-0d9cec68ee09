﻿using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using Microsoft.AspNetCore.Builder;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using Microsoft.AspNetCore.Http;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using pdftron.PDF.OCG;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Queries
{
    public class GetMeetingByIdEndpoint(UnitOfWork unitOfWork, ICurrentUser currentUser, IContainerAuthorizationService containerAuthorizationService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;
        private readonly IContainerAuthorizationService _containerAuthorizationService = containerAuthorizationService;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/containers/{meetingContainerId}", (Guid meetingContainerId, GetMeetingByIdEndpoint endpoint) => endpoint.HandleAsync(meetingContainerId))
                .WithSummary("Get details of a specific meeting container by its ID.")
                .WithDescription("Returns detailed information about a meeting container, including its agendas and files, if the current user has access. Requires view permission on the meeting container.")
                .WithAngularName<MeetingContainerRouteGroup>("GetById");
        }

        public async Task<GetMeetingDetailResponse?> HandleAsync(Guid meetingContainerId)
        {
            var canAccess = await _containerAuthorizationService.CanUserViewContainerAsync(meetingContainerId, _currentUser.SrUserId, AgendaTypeEnum.MeetingAgenda);
            if (canAccess == false)
                throw new UnauthorizedAccessException("Current user does not have access to this meeting");

            var meetingData = await _unitOfWork.Repository<MeetingContainer>()
                                                        .GetFirstOrNullAsync(meetings =>
                                                                             meetings.Where(m => m.Id == meetingContainerId)
                                                        .Select(MeetingContainerEntityFrameworkExtensions.MeetingContainerProjection(_currentUser.SrUserId)));

            if (meetingData == null)
                throw new ArgumentOutOfRangeException("Meeting was not found.");

            return meetingData;

        }
    }
}