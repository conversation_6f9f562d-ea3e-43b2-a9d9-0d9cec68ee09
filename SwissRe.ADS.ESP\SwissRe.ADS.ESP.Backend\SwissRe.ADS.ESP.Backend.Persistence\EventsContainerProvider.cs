﻿using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.Ddd.Events.Abstractions;

namespace SwissRe.ADS.ESP.Backend.Persistence
{
    public class EventsContainerProvider(AppDbContext dbContext) : IEventsContainerProvider
    {
        /// <summary>
        /// Gets the collection of all events containers (entities) that are being tracked by the context.
        /// This serves as the input for the event dispatcher.
        /// </summary>
        public IEnumerable<IEventsContainer> GetEventsContainers() =>
            dbContext.ChangeTracker.Entries()
                .Select(entry => entry.Entity)
                .Where(entity => entity is IEventsContainer)
                .Cast<IEventsContainer>()
                .ToList();
    }
}
