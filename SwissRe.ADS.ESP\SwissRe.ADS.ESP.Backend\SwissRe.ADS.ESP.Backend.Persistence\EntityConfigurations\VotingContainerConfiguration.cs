﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    internal class VotingContainerConfiguration : IEntityTypeConfiguration<VotingContainer>
    {
        public void Configure(EntityTypeBuilder<VotingContainer> builder)
        {
            builder.ToTable("VotingsContainer");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion(); // All aggregate roots should have a row version.

            builder.Property(x => x.Name).IsRequired().HasMaxLength(512);
            builder.Property(x => x.Description).HasMaxLength(5092);
            builder.Property(x => x.State).HasConversion<string>().HasMaxLength(256);

            builder.HasMany(x => x.Agendas)
                .WithOne()
                .HasForeignKey(x => x.ParentContainerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(x => x.Domain)
                .WithMany()
                .HasForeignKey(x => x.DomainId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Property(x => x.DefaultVotingOptions).HasVotingOptionsConverter();

            builder.Navigation(x => x.Agendas).AutoInclude();
        }
    }
}
