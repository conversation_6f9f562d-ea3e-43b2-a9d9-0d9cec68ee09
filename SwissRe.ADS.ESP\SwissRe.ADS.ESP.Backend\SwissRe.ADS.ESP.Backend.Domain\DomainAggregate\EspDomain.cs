﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DomainAggregate
{
    public class EspDomain : GuidEntity, IAggregateRoot
    {
        private List<EspDomainRole> _domainRoles = [];

        public Guid? ParentId { get; set; }
        public EspDomain? Parent { get; set; }

        public IReadOnlyCollection<EspDomainRole> DomainRoles => _domainRoles.AsReadOnly();

        public string Name { get; set; } = null!;
        public string? Description { get; set; }

        /// <summary>
        /// Past meeting documents available in browser expire after # days
        /// </summary>
        public int? HistoricalDocumentExpirationAfter { get; private set; }

        /// <summary>
        /// Individual annotations expire after
        /// </summary>
        public int? AnnotationsExpirationAfter { get; private set; }

        /// <summary>
        /// See documents from past meetings on iPad for # days
        /// </summary>
        public int? iPadDocumentsExpirationAfter { get; private set; }

        /// <summary>
        /// Change status to past meeting after # of days
        /// </summary>
        public int? ChangeStatusToPastAfter { get; private set; }
        public int Order { get; set; }

        public static EspDomain Create(string name, string? description, int? historicalDocumentExpirationAfter, int? annotationsExpiration, int? iPadDocumentsExpiration, int? changeStatusToPastAfter, Guid? parentId = null)
        {
            Guard.Against.NullOrWhiteSpace(name, nameof(Name), "Domain name cannot be blank.");
            if (historicalDocumentExpirationAfter is not null)
                Guard.Against.OutOfRange(historicalDocumentExpirationAfter.Value, nameof(historicalDocumentExpirationAfter), 1, 2920, "Range for Historical documents is between 1 and 2920 days");

            if (annotationsExpiration is not null)
                Guard.Against.OutOfRange(annotationsExpiration.Value, nameof(annotationsExpiration), 1, 2920, "Range for Annotation expiration is between 1 and 2920 days");

            if (iPadDocumentsExpiration is not null)
                Guard.Against.OutOfRange(iPadDocumentsExpiration.Value, nameof(iPadDocumentsExpiration), 1, 2920, "Range for iPad documents expiration is between 1 and 2920 days");

            if (changeStatusToPastAfter is not null)
                Guard.Against.OutOfRange(changeStatusToPastAfter.Value, nameof(changeStatusToPastAfter), 1, 30, "Range for change status to past is between 1 and 30 days");

            return new EspDomain()
            {
                Name = name,
                Description = description,
                ParentId = parentId,
                HistoricalDocumentExpirationAfter = historicalDocumentExpirationAfter,
                AnnotationsExpirationAfter = annotationsExpiration,
                iPadDocumentsExpirationAfter = iPadDocumentsExpiration,
                ChangeStatusToPastAfter = changeStatusToPastAfter
            };
        }

        private EspDomain() { }

        public void AddRole(EspRole role, DomainRoleTypeEnum domainRoleType)
        {
            Guard.Against.Null(role, nameof(role), "Role cannot be null.");

            if (_domainRoles.Any(r => r.RoleId == role.Id && r.RoleType == domainRoleType))
                throw new InvalidOperationException($"Role {role.Name} already exists in the domain with the same access right.");

            var domainRole = new EspDomainRole(role.Id, Id, domainRoleType);
            _domainRoles.Add(domainRole);
        }

        public void RemoveRole(Guid roleId, DomainRoleTypeEnum domainRoleType)
        {
            Guard.Against.NullOrEmpty(roleId, nameof(roleId), "Role ID cannot be null or empty.");

            var domainRole = _domainRoles.FirstOrDefault(r => r.RoleId == roleId && r.RoleType == domainRoleType);
            if (domainRole == null)
                throw new InvalidOperationException($"Role with ID {roleId} and type {domainRoleType} does not exist in the domain.");

            _domainRoles.Remove(domainRole);
        }
    }
}
