import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { getDatabase } from '../databaseClient';
import { Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

type DatabaseContextValue = {
  isReady: boolean;
};

const DatabaseContext = createContext<DatabaseContextValue | null>(null);

export const DatabaseProvider = ({ children }: { children: ReactNode }) => {
  const [ready, setReady] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    getDatabase()
      .runDatabaseMigrations()
      .then(() => {
        setReady(true);
      })
      .catch((err) => {
        // Reset promise so next render can retry
        // initDatabasePromise = null;
        console.log('error occured!!');
        setReady(true);
        setError(err);
        throw err;
      });
  }, []);

  if (!ready)
    return (
      <SafeAreaView>
        <Text>Loading data, please wait...</Text>
      </SafeAreaView>
    );

  if (error)
    return (
      <SafeAreaView>
        <Text>Critical error occurred</Text>
      </SafeAreaView>
    );

  return <DatabaseContext.Provider value={{ isReady: ready }}>{children}</DatabaseContext.Provider>;
};

export function useDatabaseReady(): boolean {
  const ctx = useContext(DatabaseContext);
  if (!ctx) throw new Error('useDatabaseReady must be used within DatabaseProvider');
  return ctx.isReady;
}
