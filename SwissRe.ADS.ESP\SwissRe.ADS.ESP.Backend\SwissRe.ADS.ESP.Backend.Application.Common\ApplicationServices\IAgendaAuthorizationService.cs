﻿using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices
{
    public interface IAgendaAuthorizationService
    {
        /// <summary>
        /// Determines whether user has Admin access to a container
        /// </summary>
        /// <param name="containerId"></param>
        /// <returns></returns>
        Task<bool> CanUserAddNewAgendaAsync(Guid containerId, AgendaTypeEnum agendaType);

        /// <summary>
        /// Determines whether user has CRUD access operations for existing Agenda. User has to be Admin for that entity
        /// </summary>
        /// <param name="agendaId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<bool> CanUserManageAgendaAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType);

        /// <summary>
        /// Determines whether user has access to view specific agenda
        /// </summary>
        /// <param name="containerId"></param>
        /// <param name="agendaId"></param>
        /// <param name="agendaType"></param>
        /// <returns></returns>
        Task<bool> CanUserViewAgendaAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType);

        /// <summary>
        /// Determines whether user can submit a voting response to a voting container
        /// </summary>
        /// <param name="agendaId"></param>
        /// <returns></returns>
        Task<bool> CanUserSubmitVoteAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType);

        /// <summary>
        /// Determines whether user can add an annotation to an agenda file
        /// </summary>
        /// <param name="agendaId"></param>
        /// <returns></returns>
        Task<bool> CanUserAddAnnotationAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType);
    }
}
