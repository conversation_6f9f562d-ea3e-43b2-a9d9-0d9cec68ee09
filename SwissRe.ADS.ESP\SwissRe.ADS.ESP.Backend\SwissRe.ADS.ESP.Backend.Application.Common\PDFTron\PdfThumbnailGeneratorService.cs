﻿using pdftron.PDF;
using System.Drawing.Imaging;

namespace SwissRe.ADS.ESP.Backend.Application.Common.PDFTron
{
    public interface IPdfThumbnailGeneratorService
    {
        byte[] GeneratePngThumbnail(byte[] fileBytes);
    }

    public class PdfThumbnailGeneratorService() : IPdfThumbnailGeneratorService
    {
        public byte[] GeneratePngThumbnail(byte[] fileBytes)
        {
            try
            {
                using var doc = new PDFDoc(fileBytes, fileBytes.Length);

                // Ensure the document is not locked
                doc.InitSecurityHandler();

                // Get the first page
                var page = doc.GetPage(1);
                if (page == null)
                {
                    throw new NullReferenceException("The PDF document does not contain any pages.");
                }

                // Render first page to bitmap
                using var pdfDraw = new PDFDraw();
                pdfDraw.SetDPI(70);
                pdfDraw.SetImageSize(250, 350, true);

                using var bmp = pdfDraw.GetBitmap(page);
                using var msImage = new MemoryStream();

#pragma warning disable CA1416 // Validate platform compatibility
                bmp.Save(msImage, ImageFormat.Png); // Can use JPEG as well
#pragma warning restore CA1416 // Validate platform compatibility

                return msImage.ToArray();

            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
