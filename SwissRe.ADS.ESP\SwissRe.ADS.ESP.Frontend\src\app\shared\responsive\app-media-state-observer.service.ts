import { BreakpointObserver } from '@angular/cdk/layout';
import { Injectable, inject } from '@angular/core';
import { Observable, map, distinctUntilChanged, shareReplay, startWith } from 'rxjs';
import { AppMediaStatesProvider } from './app-media-states-provider';
import { AppMediaState } from './AppMediaState';

/**
 * Exposes an observable that emits the matched @media state.
 * Use to watch for @media state changes.
 * */
@Injectable({
  providedIn: 'root'
})
export class AppMediaStateObserver {
  readonly state$: Observable<AppMediaState>;

  constructor() {
    const appMediaStatesProvider = inject(AppMediaStatesProvider);

    const statesByMediaQuery = Object.fromEntries(
      appMediaStatesProvider.states.map(state => {
        const largerState = appMediaStatesProvider.getLarger(state);
        const maxWidth = largerState && largerState.minWidth - 0.02;
        const mediaQuery = `(min-width: ${state.minWidth}px)${maxWidth ? ` and (max-width: ${maxWidth}px)` : ''}`;
        return [mediaQuery, state];
      })
    );

    this.state$ = inject(BreakpointObserver)
      .observe(Object.keys(statesByMediaQuery))
      .pipe(
        map(result => {
          const mediaQuery = Object.entries(result.breakpoints).find(([_, matches]) => matches)![0];
          return statesByMediaQuery[mediaQuery];
        }),
        startWith(appMediaStatesProvider.largest),
        distinctUntilChanged(),
        shareReplay(1)
      );
  }
}
