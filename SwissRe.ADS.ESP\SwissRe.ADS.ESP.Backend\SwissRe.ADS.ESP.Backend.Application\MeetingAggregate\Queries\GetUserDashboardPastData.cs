﻿using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Queries
{

    public class GetUserDashboardPastDataResponse
    {
        public List<GetUserMeetingResponse> Meetings { get; set; } = [];
        public List<GetUserVotingResponse> Votings { get; set; } = [];
    }

    public class GetUserDashboardPastDataEndpoint(UnitOfWork unitOfWork) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/userDashboard/past", (GetUserDashboardPastDataEndpoint endpoint, CancellationToken cancellationToken) => endpoint.HandleAsync(cancellationToken))
                .WithSummary("Get all past meetings that user can read.")
                .WithDescription("Returns a list of all meeting containers and their agendas that the current user has read access to, where the meetings are in the past. The response includes meeting metadata and agenda information.")
                .WithAngularName<MeetingContainerRouteGroup>("GetUserDashboardPastData");
        }

        public async Task<GetUserDashboardPastDataResponse> HandleAsync(CancellationToken cancellationToken)
        {
            var allMeetings = await _unitOfWork.ReadRepository<IGetUserDashboardMeetingsRepository>().GetPastUserMeetings(cancellationToken);
            var allVotings = await _unitOfWork.ReadRepository<IGetUserDashboardVotingsRepository>().GetPastUserVotings(cancellationToken);

            return new()
            {
                Meetings = allMeetings,
                Votings = allVotings
            };
        }
    }
}
