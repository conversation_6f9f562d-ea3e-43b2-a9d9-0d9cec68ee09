﻿using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.VotingsAggregate.Commands
{
    public class GetRevokedVotingsForUserCommand
    {
        public required IEnumerable<Guid> VotingContainerIdsOnMobileDevice { get; set; }
    }

    public class GetRevokedVotingsForUserEndpoint(UnitOfWork unitOfWork, ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<VotingsAggregateRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapPost("/users/me/revoked",
                    async (GetRevokedVotingsForUserEndpoint endpoint,
                           GetRevokedVotingsForUserCommand request,
                           CancellationToken cancellationToken) =>
                        await endpoint.HandleAsync(request, cancellationToken))
                .WithSummary("Get revoked votings for the current user")
                .WithDescription("Returns a list of voting container GUIDs that are no longer available to the current user and should be erased from the mobile device.")
                .WithAngularName<VotingsAggregateRouteGroup>("GetRevokedVotingsForUser");
        }

        public async Task<List<Guid>> HandleAsync(GetRevokedVotingsForUserCommand request, CancellationToken cancellationToken)
        {
            // Get all active voting container IDs for the current user
            var allAvailableCurrentUserVotings = await GetAccessibleVotingIdsForUser();

            // Find which IDs from the device are no longer active for the user
            var revokedIds = request.VotingContainerIdsOnMobileDevice.Except(allAvailableCurrentUserVotings).ToList();

            return revokedIds;
        }

        /// <summary>
        /// Returns all voting container IDs that the current user has access to, irrespective of their state
        /// </summary>
        /// <returns></returns>
        private async Task<List<Guid>> GetAccessibleVotingIdsForUser()
        {
            return await _unitOfWork.Repository<VotingContainer>()
                .GetAllAsync(votings => votings
                    .Where(voting => voting.Agendas.AsQueryable().Any(AgendaPermissionExtensions.HasReadAccessFor<VotingAgenda>(_currentUser.SrUserId)))
                    .Select(x => x.Id));
        }
    }
}
