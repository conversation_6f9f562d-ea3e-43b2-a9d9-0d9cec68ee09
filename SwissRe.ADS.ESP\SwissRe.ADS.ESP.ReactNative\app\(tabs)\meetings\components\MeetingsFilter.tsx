// components/HeaderMenu.tsx
import React, { useState, useEffect } from 'react';
import { View, Platform, StyleSheet, ViewStyle } from 'react-native';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import PopupContextMenu from '@/components/PopupContextMenu';
import { useEspAppTheme } from '@/hooks/useEspTheme';

export enum MeetingFilterGroupByEnum {
  // eslint-disable-next-line no-unused-vars
  ByDate,
  // eslint-disable-next-line no-unused-vars
  ByType,
}

export interface MeetingFilters {
  selectedTimeframe: MeetingSelectedTimeFrame;
  groupBy: MeetingFilterGroupByEnum;
}

export enum MeetingSelectedTimeFrame {
  // eslint-disable-next-line no-unused-vars
  Current = 'current',
  // eslint-disable-next-line no-unused-vars
  Past = 'past',
}

enum MeetingTimeframe {
  // eslint-disable-next-line no-unused-vars
  Current = 0,
  // eslint-disable-next-line no-unused-vars
  Past = 1,
}

export default function MeetingsFilter({ onChange, style }: { onChange: React.Dispatch<React.SetStateAction<MeetingFilters>>; style?: ViewStyle }) {
  const [selectedTimeframeIndex, setTimeframe] = useState<MeetingTimeframe>(MeetingTimeframe.Current);
  const [groupBy, setGroupBy] = useState<MeetingFilterGroupByEnum>(MeetingFilterGroupByEnum.ByDate);

  const theme = useEspAppTheme();

  // Whenever state changes, notify parent
  useEffect(() => {
    const selectedTimeframe = selectedTimeframeIndex === MeetingTimeframe.Current ? MeetingSelectedTimeFrame.Current : MeetingSelectedTimeFrame.Past;
    const newFilters = { selectedTimeframe, groupBy };

    onChange((prev) => {
      if (prev.selectedTimeframe !== newFilters.selectedTimeframe || prev.groupBy !== newFilters.groupBy) {
        console.log('Meetings Filter onChange called');
        return newFilters;
      }
      return prev;
    });
  }, [selectedTimeframeIndex, groupBy]);

  return (
    <View style={[styles.headerMenu, { backgroundColor: theme.colors.background }, style]}>
      <SegmentedControl
        values={['Current', 'Past']}
        selectedIndex={selectedTimeframeIndex}
        onChange={(event) => setTimeframe(event.nativeEvent.selectedSegmentIndex)}
        style={styles.segmentedControlWidth}
      />

      <PopupContextMenu
        menuTitle="Group results"
        textLabel={groupBy === MeetingFilterGroupByEnum.ByDate ? 'Group by Date' : 'Group by Type'}
        onActionSelected={(actionId) => setGroupBy(actionId === 'byDate' ? MeetingFilterGroupByEnum.ByDate : MeetingFilterGroupByEnum.ByType)}
        actions={[
          {
            id: 'byDate',
            title: 'By Date',
            titleColor: '#2367A2',
            image: Platform.select({
              ios: 'calendar',
              android: 'ic_menu_add',
            }),
            imageColor: '#2367A2',
          },
          {
            id: 'byType',
            title: 'By Type',
            titleColor: '#2367A2',
            image: Platform.select({
              ios: 'books.vertical',
              android: 'ic_menu_add',
            }),
            imageColor: '#2367A2',
          },
        ]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  headerMenu: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: -16,
    paddingBottom: 16,
    paddingLeft: 8,
    paddingTop: 16,
  },
  segmentedControlWidth: {
    width: 250,
  },
});
