﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <None Remove="Sample\AnnotationSample.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Sample\AnnotationSample.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.SlowCheetah" Version="4.0.50">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Persistence\SwissRe.ADS.ESP.Backend.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.Development.json">
      <TransformOnBuild>true</TransformOnBuild>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TransformOnBuild>true</TransformOnBuild>
    </None>
  </ItemGroup>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
