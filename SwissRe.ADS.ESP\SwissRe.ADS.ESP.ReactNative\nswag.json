{"runtime": "Net80", "documentGenerator": {"fromDocument": {"url": "https://localhost:6001/swagger/v1/swagger.json", "output": null, "newLineBehavior": "Auto"}}, "codeGenerators": {"openApiToTypeScriptClient": {"className": "{controller}Client", "moduleName": "", "namespace": "", "typeScriptVersion": 5.3, "template": "A<PERSON>os", "templateDirectory": "_nswag/custom-templates", "extensionCode": "_nswag/nswag.imports.ts", "promiseType": "Promise", "withCredentials": true, "dateTimeType": "Date", "nullValue": "null", "generateClientClasses": true, "generateClientInterfaces": false, "generateOptionalParameters": true, "exportTypes": true, "wrapDtoExceptions": false, "exceptionClass": "ApiException", "clientBaseClass": null, "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "protectedMethods": [], "configurationClass": null, "useTransformOptionsMethod": false, "useTransformResultMethod": false, "generateDtoTypes": true, "operationGenerationMode": "MultipleClientsFromOperationId", "markOptionalProperties": true, "generateCloneMethod": false, "typeStyle": "Class", "enumStyle": "Enum", "useLeafType": false, "classTypes": [], "extendedClasses": [], "generateDefaultValues": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateTypeCheckFunctions": false, "generateConstructorInterface": true, "convertConstructorInterfaceData": false, "importRequiredTypes": true, "useGetBaseUrlMethod": false, "baseUrlTokenName": "API_BASE_URL", "queryNullValue": "", "useAbortSignal": true, "inlineNamedDictionaries": false, "inlineNamedAny": false, "includeHttpContext": false, "serviceHost": null, "serviceSchemes": null, "output": "api/apiServices.ts", "newLineBehavior": "Auto"}}}