import { getDatabase } from '../databaseClient';
import { CurrentDeviceInfoEntity } from '../types/CurrentDeviceInfoEntity';

export class CurrentDeviceInfoRepository {
  static async getDeviceId(): Promise<string> {
    const db = await getDatabase();

    const deviceInfo = await db.getFirst<CurrentDeviceInfoEntity>('SELECT * FROM CurrentDeviceInfoEntity');
    return deviceInfo?.deviceId ?? '';
  }
}
