﻿using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DomainAggregate
{
    public class EspDomainRole : GuidEntity
    {
        public Guid RoleId { get; private set; }
        public EspRole Role { get; private set; } = null!;

        public Guid DomainId { get; private set; }
        public EspDomain Domain { get; private set; } = null!;

        public DomainRoleTypeEnum RoleType { get; private set; }

        public EspDomainRole(Guid roleId, Guid domainId, DomainRoleTypeEnum roleType)
        {
            DomainId = domainId;
            RoleId = roleId;
            RoleType = roleType;
        }

        private EspDomainRole() { }
    }
}
