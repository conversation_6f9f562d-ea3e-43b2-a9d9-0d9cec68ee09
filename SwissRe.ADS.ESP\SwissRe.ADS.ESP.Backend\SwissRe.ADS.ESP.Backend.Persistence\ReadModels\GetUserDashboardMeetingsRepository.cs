﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.Common.Containers;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.ReadModels
{
    public class GetUserDashboardMeetingsRepository(AppDbContext context, ICurrentUser currentUser) : IGetUserDashboardMeetingsRepository
    {
        private readonly AppDbContext _context = context;
        private readonly ICurrentUser _currentUser = currentUser;

        public async Task<List<GetUserMeetingResponse>> GetCurrentAndFutureUserMeetings(CancellationToken cancellationToken = default)
        {
            return await GetUserDashboardMeetingsByState(ContainerStateEnum.Published, cancellationToken);
        }

        public async Task<List<GetUserMeetingResponse>> GetPastUserMeetings(CancellationToken cancellationToken = default)
        {
            return await GetUserDashboardMeetingsByState(ContainerStateEnum.PastMeeting, cancellationToken);
        }

        private async Task<List<GetUserMeetingResponse>> GetUserDashboardMeetingsByState(ContainerStateEnum containerState, CancellationToken cancellationToken = default)
        {
            var accessibleContainerIds = _context.Set<MeetingAgenda>()
                          .Where(AgendaPermissionExtensions.HasReadAccessFor<MeetingAgenda>(_currentUser.SrUserId))
                          .Select(a => a.ParentContainerId)
                          .Distinct();

            var accessibleAgendaIds = _context.Set<MeetingAgenda>()
                .Where(AgendaPermissionExtensions.HasReadAccessFor<MeetingAgenda>(_currentUser.SrUserId))
                .Select(a => a.Id);


            var allMeetingDocuments = from fileMetadata in _context.Set<MeetingFileMetadata>()
                                      where fileMetadata.IsLatest && accessibleAgendaIds.Contains(fileMetadata.MeetingAgendaId)
                                      join agenda in _context.Set<MeetingAgenda>() on fileMetadata.MeetingAgendaId equals agenda.Id
                                      group fileMetadata by new { agenda.ParentContainerId } into g
                                      select new
                                      {
                                          ContainerId = g.Key.ParentContainerId,
                                          TotalDocuments = g.Count(),
                                      };


            var userMeetingReadDocuments = from fileMetadata in _context.Set<MeetingFileMetadata>()
                                           where fileMetadata.IsLatest && accessibleAgendaIds.Contains(fileMetadata.MeetingAgendaId)
                                           && fileMetadata.ReadStatuses.Where(o => o.UserId == _currentUser.SrUserId).Any()
                                           join agenda in _context.Set<MeetingAgenda>() on fileMetadata.MeetingAgendaId equals agenda.Id
                                           group fileMetadata by new { agenda.ParentContainerId } into g
                                           select new
                                           {
                                               ContainerId = g.Key.ParentContainerId,
                                               ReadDocuments = g.Count(),
                                           };

            var meetingQuery = from meeting in _context.Set<MeetingContainer>().Where(x => accessibleContainerIds.Contains(x.Id) && x.State == containerState)
                               join domain in _context.Set<EspDomain>() on meeting.DomainId equals domain.Id
                               join parentDomain in _context.Set<EspDomain>() on domain.ParentId equals parentDomain.Id into parentDomains
                               from parentDomain in parentDomains.DefaultIfEmpty()
                               from meetingAllDocuments in allMeetingDocuments.Where(ad => ad.ContainerId == meeting.Id).DefaultIfEmpty()
                               from meetingReadDocuments in userMeetingReadDocuments.Where(ad => ad.ContainerId == meeting.Id).DefaultIfEmpty()
                               select new GetUserMeetingResponse
                               {
                                   ContainerType = ContainerTypeEnum.MeetingContainer,
                                   ContainerId = meeting.Id,
                                   Title = meeting.Name,
                                   TotalDocuments = meetingAllDocuments != null ? meetingAllDocuments.TotalDocuments : 0,
                                   ReadDocuments = meetingReadDocuments != null ? meetingReadDocuments.ReadDocuments : 0,
                                   Location = meeting.Location,
                                   State = meeting.State,
                                   StartTime = meeting.StartTime,
                                   Domain = new GetDomainResponse
                                   {
                                       Id = domain.Id,
                                       Name = domain.Name,
                                       Parent = parentDomain != null
                                           ? new GetDomainResponse
                                           {
                                               Id = parentDomain.Id,
                                               Name = parentDomain.Name
                                           }
                                           : null
                                   }
                               };

            return await meetingQuery.AsNoTracking().ToListAsync(cancellationToken);
        }
    }
}
