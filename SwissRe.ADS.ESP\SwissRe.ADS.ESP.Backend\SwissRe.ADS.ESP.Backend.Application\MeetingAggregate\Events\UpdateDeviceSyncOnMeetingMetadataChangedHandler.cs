﻿using SwissRe.ADS.Ddd.Events.Dispatching.Abstractions;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate.ServiceBusMessages;
using SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate.Events;
using SwissRe.ADS.ServiceBus.Sending;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Events
{
    /// <summary>
    /// Purpose of this handler is to only send a message to the service bus when the meeting metadata changes
    /// So that it can be picked up by the MobileSyncApi and the devices can be updated accordingly.
    /// </summary>
    /// <param name="busSender"></param>
    [IntegrationEventHandler("UpdateDeviceSyncOnMeetingMetadataChanged")]
    public class UpdateDeviceSyncOnMeetingMetadataChangedHandler(ISender busSender) : IntegrationEventHandler<MeetingMetadataChangedEvent>
    {
        private readonly ISender _busSender = busSender;

        public override async Task HandleAsync(MeetingMetadataChangedEvent evt)
        {
            await _busSender.SendAsync(new MeetingMetadataChangedMessage(evt.MeetingId));
        }
    }
}
