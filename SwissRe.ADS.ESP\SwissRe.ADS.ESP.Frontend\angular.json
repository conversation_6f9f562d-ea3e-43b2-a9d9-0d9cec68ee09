{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/app", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles/base.scss", "src/styles/sr-theme.scss", "src/styles/font.scss", "src/styles/material.scss", "src/app/shared/responsive/media.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles", "src/app/shared/responsive"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "app:build:production"}, "development": {"buildTarget": "app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "app:build"}}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles/styles.scss"], "scripts": []}}}}}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}