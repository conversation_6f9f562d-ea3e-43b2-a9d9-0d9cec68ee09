import { AppState, AppStateStatus } from 'react-native';
import { IS<PERSON>Job } from './types/ISyncJob';
import { MeetingDataSyncJob } from './jobs/MeetingDataSyncJob';
import { CurrentDeviceInfoRepository } from '@/database/repositories/currentDeviceInfoRepository';

// eslint-disable-next-line no-unused-vars
type Listener = (changed: boolean) => void;

export class SyncJobManager {
  private static instance: SyncJobManager;
  private jobs: ISyncJob[] = [];
  private timers: Record<string, ReturnType<typeof setInterval>> = {};
  private running: Record<string, boolean> = {};
  private listeners: Record<string, Listener[]> = {};
  private appState: AppStateStatus = AppState.currentState;
  private currentDeviceUniqueId: string | undefined = undefined;

  public async initializeSyncJobs() {
    // if (this.currentDeviceUniqueId) throw new Error(`You should only initialize sync job once per application lifecycle!`);
    // const deviceId = await CurrentDeviceInfoRepository.getDeviceId();
    // if (!deviceId) throw new Error('No device ID found');
    // this.currentDeviceUniqueId = deviceId;
    // console.log(`[SyncManager] Device ID set: ${deviceId}`);
    // //register all jobs
    // this.jobs = [new MeetingDataSyncJob(this.currentDeviceUniqueId)];
    // // AppState listener
    // AppState.addEventListener('change', this.handleAppStateChange);
    // // Start immediately if app is active
    // if (this.appState === 'active') {
    //   this.startAll(true);
    // }
  }

  //   /**
  //    * Injects deviceId into SyncManager
  //    * Can only be set once.
  //    */
  //   public setDeviceId(id: string) {
  //     if (this.currentDeviceUniqueId) {
  //       throw new Error(`Device ID is already set to '${this.currentDeviceUniqueId}' — re-assignment is not allowed.`);
  //     }
  //     this.currentDeviceUniqueId = id;
  //     console.log(`[SyncManager] Device ID set: ${id}`);

  //     //register all jobs
  //     this.jobs = [new MeetingDataSyncJob(this.currentDeviceUniqueId)];

  //     // AppState listener
  //     AppState.addEventListener('change', this.handleAppStateChange);

  //     // Start immediately if app is active
  //     if (this.appState === 'active') {
  //       this.startAll(true);
  //     }
  //   }

  public static getInstance(): SyncJobManager {
    if (!SyncJobManager.instance) {
      SyncJobManager.instance = new SyncJobManager();
    }
    return SyncJobManager.instance;
  }

  public subscribe(jobId: string, listener: Listener) {
    if (!this.listeners[jobId]) this.listeners[jobId] = [];
    this.listeners[jobId].push(listener);
    return () => {
      this.listeners[jobId] = this.listeners[jobId].filter((l) => l !== listener);
    };
  }

  private notify(jobId: string, changed: boolean) {
    (this.listeners[jobId] || []).forEach((listener) => listener(changed));
  }
  private handleAppStateChange = (nextState: AppStateStatus) => {
    if (this.appState.match(/inactive|background/) && nextState === 'active') {
      console.log('App resumed — running all jobs immediately');
      this.startAll(true);
    }
    if (this.appState === 'active' && nextState.match(/inactive|background/)) {
      console.log('App backgrounded — stopping sync jobs');
      this.stopAll();
    }
    this.appState = nextState;
  };

  private startAll(runImmediately = false) {
    for (const job of this.jobs) {
      this.startJob(job, runImmediately);
    }
  }

  private startJob(job: ISyncJob, runImmediately = false) {
    if (runImmediately) {
      this.runJob(job);
    }
    this.timers[job.id] = setInterval(() => this.runJob(job), job.intervalInMs);
  }

  private stopAll() {
    Object.values(this.timers).forEach(clearInterval);
    this.timers = {};
  }

  private async runJob(job: ISyncJob) {
    if (this.running[job.id]) {
      console.log(`[${job.id}] Already running — skipping`);
      return;
    }
    this.running[job.id] = true;
    try {
      const changed = await job.run();
      this.notify(job.id, changed);
    } catch (err) {
      console.error(`[${job.id}] Sync failed`, err);
    } finally {
      this.running[job.id] = false;
    }
  }
}
