﻿using SwissRe.ADS.Ddd.Events.Dispatching.Abstractions;
using SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate.Events;
using SwissRe.ADS.ServiceBus.Sending;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Events
{
    /// <summary>
    /// Purpose of this handler is to only send a message to the service bus when the meeting metadata changes
    /// So that it can be picked up by the MobileSyncApi and the devices can be updated accordingly.
    /// </summary>
    /// <param name="busSender"></param>
    [IntegrationEventHandler("UpdateDeviceSyncOnMeetingAccessRightChanged")]
    public class UpdateDeviceSyncOnMeetingAccessRightChangedHandler(ISender busSender) : IntegrationEventHandler<MeetingAccessRightsChangedForUserEvent>
    {
        private readonly ISender _busSender = busSender;

        public override Task HandleAsync(MeetingAccessRightsChangedForUserEvent evt)
        {
            throw new NotImplementedException();
        }
    }
}
