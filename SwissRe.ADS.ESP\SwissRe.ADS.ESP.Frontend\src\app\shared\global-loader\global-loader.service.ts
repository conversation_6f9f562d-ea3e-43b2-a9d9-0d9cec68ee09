import { BehaviorSubject } from 'rxjs';
import { Injectable, inject } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { GlobalLoaderComponent } from './global-loader.component';

@Injectable({
  providedIn: 'root'
})
export class GlobalLoaderService {
  private dialog = inject(MatDialog);

  private readonly runningLoadersSubject = new BehaviorSubject<RunningGlobalLoader[]>([]);
  private dialogRef: MatDialogRef<GlobalLoaderComponent> | null = null;

  /**
   * Displays a global loader with the given optional text.
   * @returns A function to hide the loader.
   */
  show(text: string | null = 'Loading application data...'): GlobalLoaderHideFunction {
    let loader: RunningGlobalLoader | null = { text: text?.trim() || null };
    this.runningLoadersSubject.next([...this.runningLoadersSubject.value, loader]);

    this.dialogRef ??= this.dialog.open(GlobalLoaderComponent, { disableClose: true, data: this.runningLoadersSubject.asObservable() });

    return () => {
      if (loader) {
        this.runningLoadersSubject.next(this.runningLoadersSubject.value.filter(l => l !== loader));
        loader = null;

        if (!this.runningLoadersSubject.value.length) {
          this.dialogRef!.close();
          this.dialogRef = null;
        }
      }
    };
  }
}

export type GlobalLoaderHideFunction = () => void;

export interface RunningGlobalLoader {
  text: string | null;
}
