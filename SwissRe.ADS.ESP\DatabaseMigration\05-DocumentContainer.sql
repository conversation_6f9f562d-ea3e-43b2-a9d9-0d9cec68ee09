-- DocumentsContainer table
-- SELECT * from esp.t_mp_meeting m where m.item_type = 'GD'
-- SELECT distinct m.item_type from esp.t_mp_meeting m 
--SELECT * from esp.t_mp_meeting m where m.type_tid = 135375970 and ord = 1
/*
SELECT 
    * 
FROM 
    esp.t_mp_meeting m
WHERE 
    m.type_tid in (SELECT type_tid from esp.t_mp_type where item_type = 'GD') and m.ord = 1
*/
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."DocumentsContainer") <> 0 THEN
        RAISE NOTICE 'Delete from espv2.DocumentsContainer';
        DELETE FROM espv2."DocumentsContainer";
    END IF;
END $$;

INSERT INTO espv2."DocumentsContainer" (
    "Id"
    ,"DomainId"
    ,"ORIGINAL_DB_ID"
    ,"Name"
    ,"Description"
    ,"State"
    ,"LastUpdatedOnUTC"
)
VALUES (
    '00000000-0000-0000-0000-000000000000'
    ,'00000000-0000-0000-0000-000000000000'
    ,0
    ,'Dummy Document Container'
    ,NULL
    ,'Disabled'
    ,'1970-01-01 00:00:00'
)

INSERT INTO espv2."DocumentsContainer" (
    "Id"
    ,"DomainId"
    ,"ORIGINAL_DB_ID"
    ,"Name"
    ,"Description"
    ,"State"
    ,"LastUpdatedOnUTC"
)
SELECT DISTINCT ON (m.meeting_tid)
    gen_random_uuid() AS Id
    ,(SELECT "Id" FROM espv2."Domains" WHERE "ORIGINAL_DB_ID" = m.type_tid) AS DomainId
    ,m.meeting_tid AS ORIGINAL_DB_ID
    ,m.Name
    ,m.Description
    ,COALESCE(
        (SELECT nme FROM esp.t_upload_status us WHERE us.upload_status_tid = m.status_tid),
        'Published'
    ) AS "State"
    ,COALESCE(m.update_time, '1970-01-01 00:00:00') AS "LastUpdatedOnUTC"
    ,m.status_tid
FROM 
    esp.t_mp_meeting m
INNER JOIN 
    esp.t_mp_type t ON m.type_tid = t.type_tid
INNER JOIN 
    esp.t_mp_file mf ON mf.meeting_item_tid = m.meeting_tid
INNER JOIN
    esp.t_file_rel fr ON mf.file_tid = fr.file_tid
WHERE 
    t.item_type = 'GD' 
    AND m.ord = 1
ORDER BY m.meeting_tid, m.update_time DESC

