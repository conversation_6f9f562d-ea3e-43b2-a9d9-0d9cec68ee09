﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate
{
    public class VotingContainer : GuidEntity, IAggregateRoot
    {
        private List<VotingAgenda> agendas = [];
        public Guid DomainId { get; private set; }

        public EspDomain Domain { get; set; } = null!;
        public string Name { get; private set; } = null!;
        public string? Description { get; private set; }
        public ContainerStateEnum State { get; private set; }
        public VotingOptions DefaultVotingOptions { get; private set; } = null!;

        /// <summary>
        /// Flag that determines, whether Guest role should be included in the Agenda Permission
        /// </summary>
        public bool IncludeGuestRole { get; private set; } = false;

        public DateTime LastUpdatedOnUTC { get; private set; } = DateTime.UtcNow;

        public IReadOnlyCollection<VotingAgenda> Agendas => agendas.AsReadOnly();
        internal VotingAgenda GetAgenda(Guid agendaId) => agendas.First(a => a.Id == agendaId) ?? throw new ArgumentException($"Agenda with ID {agendaId} does not exist in this container.");

        private VotingContainer() { }
        public static VotingContainer Create(string name, Guid domainId, DateTime startTime, DateTime endTime, VotingOptions defaultVotingOptions, string? location = null, string? description = null)
        {
            Guard.Against.NullOrWhiteSpace(name, nameof(Name), "Name cannot be blank.");
            Guard.Against.Null(defaultVotingOptions, nameof(DefaultVotingOptions), "You need to specify default voting options.");
            Guard.Against.Zero(defaultVotingOptions.Options.Count, nameof(defaultVotingOptions.Options), "You need to specify at least one default voting option.");

            if (startTime >= endTime)
                throw new ArgumentException("Start time must be before end time.");

            var voting = new VotingContainer
            {
                Name = name,
                DomainId = domainId,
                Description = description,
                State = ContainerStateEnum.Draft,
                DefaultVotingOptions = defaultVotingOptions
            };

            return voting;
        }

        public void AddAgenda(VotingAgenda agenda)
        {
            agendas.Add(agenda);

            UpdateLastModifiedOn();
        }

        public void ReorderAgendas(IReadOnlyList<Guid> orderedAgendaIds)
        {
            AgendaReorderService.ReorderAgendaItems(agendas, orderedAgendaIds, (agenda, order) => agenda.SetOrder(order));

            UpdateLastModifiedOn();
        }

        public void RemoveAgenda(Guid agendaId)
        {
            var agenda = agendas.FirstOrDefault(a => a.Id == agendaId);
            if (agenda == null)
            {
                throw new ArgumentException($"Agenda with ID {agendaId} does not exist in this voting container.");
            }
            agendas.Remove(agenda);

            UpdateLastModifiedOn();
        }


        public void AddVotingQuestion(Guid agendaId, string question, byte[]? descriptionEncrypted, VotingOptions availableOptions, string createdByUserId)
        {
            var agenda = GetAgenda(agendaId);
            agenda.AddQuestion(question, descriptionEncrypted, availableOptions, createdByUserId);

            UpdateLastModifiedOn();
        }

        public void UpdateVotingQuestion(Guid agendaId, Guid votingQuestionId, string newQuestion, byte[]? newDescriptionEncrypted, VotingOptions? newAvailableOptions, string updatedByUserId)
        {
            var agenda = GetAgenda(agendaId);
            agenda.UpdateQuestion(votingQuestionId, newQuestion, newDescriptionEncrypted, newAvailableOptions, updatedByUserId);

            UpdateLastModifiedOn();
        }

        public void AddVotingQuestionSupportingDocument(Guid agendaId, Guid votingQuestionId, byte[] unencryptedFileContents, string fileName, string extension, int size, int order, int numberOfPages, string createdByUserId, bool isSupplementaryFile = false)
        {
            var agenda = GetAgenda(agendaId);
            agenda.AddQuestionSupportingDocument(votingQuestionId, unencryptedFileContents, fileName, extension, size, order, numberOfPages, createdByUserId, isSupplementaryFile);

            UpdateLastModifiedOn();
        }

        public void RemoveVotingQuestionSupportingDocument(Guid agendaId, Guid votingQuestionId, Guid supportingDocumentId)
        {
            var agenda = GetAgenda(agendaId);
            agenda.RemoveQuestionSupportingDocument(votingQuestionId, supportingDocumentId);

            UpdateLastModifiedOn();
        }

        public void LockQuestionOnUserVote(Guid agendaId, Guid votingQuestionId)
        {
            var agenda = GetAgenda(agendaId);
            agenda.LockQuestionOnUserAnswer(votingQuestionId);

            //we do not need to update last modified on here, as this is a state change that does not affect the container itself.
        }

        /// <summary>
        /// Updates LastUpdatedOnUTC to indicate that the container or any of it's child entities have been modified.
        /// </summary>
        private void UpdateLastModifiedOn() => LastUpdatedOnUTC = DateTime.UtcNow;
    }
}
