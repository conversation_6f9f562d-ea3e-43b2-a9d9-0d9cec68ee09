﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="BouncyCastle.Cryptography" Version="2.6.1" />
	<PackageReference Include="PDFTron.NET.x64" Version="11.5.0" />
    <PackageReference Include="SwissRe.ADS.Ddd.Events.Dispatching.Abstractions" Version="2.1.1" />
    <PackageReference Include="SwissRe.ADS.MinimalApi.Endpoints" Version="1.0.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.6" />
	  <PackageReference Include="SwissRe.ADS.ServiceBus" Version="11.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Domain\SwissRe.ADS.ESP.Backend.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
  </ItemGroup>

</Project>
