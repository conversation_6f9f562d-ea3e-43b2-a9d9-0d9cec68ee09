﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.AuditLogAggregate
{
    public class AuditLog : GuidEntity, IAggregateRoot
    {
        public string EntityName { get; private set; } = null!;
        public string Period { get; private set; } = null!;
        public Guid EntityId { get; private set; }
        public string Message { get; private set; } = null!;
        public string? Payload { get; private set; }
        public DateTime CreatedOn { get; private set; }
        public string CreatedByUserId { get; private set; } = null!;

        public static AuditLog Create(
            string entityName,
            Guid entityId,
            string message,
            string createdByUserId,
            string? payload = null)
        {
            Guard.Against.NullOrWhiteSpace(entityName, nameof(entityName), "Entity name cannot be blank.");
            Guard.Against.NullOrWhiteSpace(message, nameof(Message), "Audit log message cannot be blank.");

            return new AuditLog()
            {
                EntityName = entityName,
                Period = DateTime.UtcNow.ToString("yyyy-MM"),
                EntityId = entityId,
                Message = message,
                Payload = payload,
                CreatedOn = DateTime.UtcNow,
                CreatedByUserId = createdByUserId
            };
        }

    }
}
