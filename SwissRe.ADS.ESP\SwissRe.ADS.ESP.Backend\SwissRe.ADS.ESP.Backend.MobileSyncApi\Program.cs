using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Identity.Web;
using Microsoft.Identity.Abstractions;
using Microsoft.Identity.Web.Resource;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.ResponseCompression;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application;
using SwissRe.ADS.ESP.Backend.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.MinimalApi.Endpoints;
using Serilog;
using Microsoft.ApplicationInsights.Extensibility;
using HealthChecks.UI.Client;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.MeetingsAggregate.Helpers;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ServiceBus.Registration;
using Azure.Messaging.ServiceBus;
using System.Reflection;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi;

public class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        AddServices(builder);

        var app = builder.Build();
        BuildPipeline(app);
        app.Run();
    }
    private static void AddServices(WebApplicationBuilder builder)
    {
        builder.Services.AddApplicationInsightsTelemetry();
        builder.Services.AddSerilog((services, config) =>
        {
            config.WriteTo.Console();
            config.WriteTo.ApplicationInsights(services.GetRequiredService<TelemetryConfiguration>(), TelemetryConverter.Traces);
        });

        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerDocument();
        builder.Services.AddMemoryCache();
        builder.Services.AddApplication(builder.Configuration);
        builder.Services.AddPersistence(builder.Configuration, null);
        builder.Services.AddMinimalApiEndpoints(config => config.AddEndpointsFrom(typeof(Program).Assembly));
        builder.Services.AddProblemDetails();
        builder.Services.AddAuth(builder.Configuration, builder.Environment);
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddHealthChecks();

        if (builder.Environment.IsDevelopment())
        {
            builder.Services.AddAdsServiceBus(builder.Configuration, builder.Environment, configure: config =>
            {
                config.ServiceBusClientFactory = (fullyQualifiedNamespace, credential) => new ServiceBusClient(builder.Configuration.GetValue<string>("ServiceBus:ConnectionString"));
                config.AddReceiversFrom(Assembly.GetExecutingAssembly());
            });
        }
        else
        {
            builder.Services.AddAdsServiceBus(builder.Configuration, builder.Environment, configure: config => config.AddReceiversFrom(Assembly.GetExecutingAssembly()));
        }

        builder.Services.AddScoped<IMeetingSyncDataService, MeetingSyncDataService>();

        builder.Services.AddResponseCompression(options =>
        {
            options.Providers.Add<GzipCompressionProvider>();
            options.EnableForHttps = true;
        });

        builder.Services.Configure<GzipCompressionProviderOptions>(options =>
        {
            options.Level = System.IO.Compression.CompressionLevel.Optimal;
        });

        builder.Services.AddResponseCompression(options =>
        {
            options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(["application/json"]);
        });

        builder.Services.AddScoped<ICurrentUser, CurrentUser>();
    }

    private static void BuildPipeline(WebApplication app)
    {
        app.UseHealthChecks("/healthCheck", new HealthCheckOptions
        {
            ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
        });

        AddExplicitEndpointsForManuallyExposingModels(app);

        if (app.Environment.IsDevelopment())
        {
            app.UseOpenApi();
            app.UseSwaggerUI();
        }

        app.UseExceptionHandler(exApp => exApp.Run(async context =>
        {
            var exception = context.Features.GetRequiredFeature<IExceptionHandlerFeature>().Error;
            await GlobalExceptionHandler.OnExceptionAsync(exception, context, app.Environment);
        }));

        app.UseAuthentication();
        app.UseAuthorization();

        app.MapMinimalApiEndpoints();
    }

    /// <summary>
    /// We need to expose some models explicitly to ensure they are included in the Swagger documentation. This is the easiest way in Minimal API as of today.
    /// </summary>
    /// <param name="app"></param>
    private static void AddExplicitEndpointsForManuallyExposingModels(WebApplication app)
    {
        if (app.Environment.IsDevelopment() == false) return;

        app.MapGroup("__InternalModelExposure__")
            .WithTags("__InternalModelExposure__")
            .MapGet("/model-exposure/MeetingDetailResponse", () =>
            {
                throw new NotSupportedException("This endpoint exists only to expose the MeetingDetailResponse model for Swagger/OpenAPI schema generation. Do not use it in application logic.");
            })
            .WithName("SwaggerInclude_MeetingDetailResponse")
            .Produces<GetMeetingDetailResponse>(StatusCodes.Status200OK);

    }
}
