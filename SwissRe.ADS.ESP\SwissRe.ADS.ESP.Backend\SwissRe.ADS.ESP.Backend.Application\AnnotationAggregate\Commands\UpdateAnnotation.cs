using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Application.Common.DomainServices;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Application.AnnotationAggregate.Commands
{
    public record UpdateAnnotationCommand(string annotationXml);

    public class UpdateAnnotationEndpoint(
        UnitOfWork unitOfWork,
        IAgendaAuthorizationService agendaPermissionService,
        IEspEncryptionService espEncryptionService,
        IAgendaInfoProviderService agendaInfoProviderService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly IEspEncryptionService _espEncryptionService = espEncryptionService;
        private readonly IAgendaInfoProviderService _agendaInfoProviderService = agendaInfoProviderService;

        public static void BuildRoute([EndpointRouteBuilder<AnnotationRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapPut("/files/{fileMetadataId}/annotations/{annotationId}", (UpdateAnnotationCommand command, Guid fileMetadataId, Guid annotationId, FileMetadataTypeEnum fileMetadataType, UpdateAnnotationEndpoint endpoint) => endpoint.HandleAsync(command, fileMetadataId, annotationId, fileMetadataType))
                .WithAngularName<AnnotationRouteGroup>("UpdateAnnotation");

        public async Task HandleAsync(UpdateAnnotationCommand command, Guid fileMetadataId, Guid annotationId, FileMetadataTypeEnum fileMetadataType)
        {
            var agendaInfo = await _agendaInfoProviderService.GetAgendaInfoAsync(fileMetadataId, fileMetadataType);
            
            AgendaTypeEnum agendaType = fileMetadataType switch
            {
                FileMetadataTypeEnum.Documents => AgendaTypeEnum.DocumentAgenda,
                FileMetadataTypeEnum.Meetings => AgendaTypeEnum.MeetingAgenda,
                _ => throw new ArgumentException("Invalid file metadata type.", nameof(fileMetadataType))
            };
            
            var canAddAnnotation = await _agendaPermissionService.CanUserAddAnnotationAsync(agendaInfo.ParentContainerId, agendaInfo.AgendaId, agendaType);
            if (canAddAnnotation == false)
                throw new UnauthorizedAccessException("Current user does not have permission to update annotations for this file.");

            var annotationRepo = _unitOfWork.Repository<Annotation>();
            var annotation = await annotationRepo.GetAsync(annotationId, true);

            var encryptedAnnotationText = _espEncryptionService.Encrypt(command.annotationXml);
            annotation.Modify(encryptedAnnotationText);
            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
