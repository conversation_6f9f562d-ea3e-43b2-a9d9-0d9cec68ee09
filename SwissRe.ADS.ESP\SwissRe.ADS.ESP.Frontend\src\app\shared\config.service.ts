import { Injectable } from '@angular/core';
import { LicenseManager } from 'ag-grid-enterprise';
import { firstValueFrom } from 'rxjs';
import { AppConfigurationClient } from './apiServices';

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  constructor(private configClient: AppConfigurationClient) {}

  async loadConfig() {
    const res = await firstValueFrom(this.configClient.get());
    LicenseManager.setLicenseKey(res.agGridLicenseKey);
  }
}
