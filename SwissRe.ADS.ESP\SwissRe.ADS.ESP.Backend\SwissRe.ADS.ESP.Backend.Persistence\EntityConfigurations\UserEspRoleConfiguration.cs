﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class UserEspRoleConfiguration : IEntityTypeConfiguration<UserEspRole>
    {
        public void Configure(EntityTypeBuilder<UserEspRole> builder)
        {
            builder.ToTable("UserRoles");

            // Composite primary key
            builder.HasKey(ma => new { ma.UserId, ma.RoleId });
        }
    }
}
