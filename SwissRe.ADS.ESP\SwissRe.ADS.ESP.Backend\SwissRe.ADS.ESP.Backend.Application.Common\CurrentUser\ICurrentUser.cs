﻿using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser
{
    public interface ICurrentUser
    {
        string SrUserId { get; }
        Guid AzureUserId { get; }
        string FullName { get; }
        string Email { get; }
        IReadOnlySet<CurrentUserRole> Roles { get; }
        IReadOnlyCollection<Guid> EspRoles { get; }
    }
}
