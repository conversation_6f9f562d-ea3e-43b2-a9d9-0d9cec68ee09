﻿using Microsoft.Extensions.Options;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Org.BouncyCastle.Bcpg;
using Org.BouncyCastle.Security;
using System.Text;
using Org.BouncyCastle.Utilities.IO;

namespace SwissRe.ADS.ESP.Backend.Application.Common.Encryption
{
    public class EspEncryptionService : IEspEncryptionService
    {
        private readonly EspEncryptionConfigurationOptions _encryptionOptions;

        public EspEncryptionService(IOptions<EspEncryptionConfigurationOptions> encryptionOptions)
        {
            ArgumentNullException.ThrowIfNull(encryptionOptions, nameof(encryptionOptions));
            _encryptionOptions = encryptionOptions.Value ?? throw new ArgumentNullException(nameof(encryptionOptions.Value), "Encryption options cannot be null.");
            ArgumentNullException.ThrowIfNullOrWhiteSpace(_encryptionOptions.Passphrase, nameof(_encryptionOptions.Passphrase));
        }

        public byte[] Encrypt(string plainText)
        {
            var plainBytes = Encoding.UTF8.GetBytes(plainText);
            return Encrypt(plainBytes);
        }

        public byte[] Encrypt(byte[] plainBytes)
        {
            using var outputStream = new MemoryStream();
            using (var armoredStream = new ArmoredOutputStream(outputStream))
            {
                var encGen = new PgpEncryptedDataGenerator(
                    SymmetricKeyAlgorithmTag.Aes256,
                    true,
                    new SecureRandom()
                );

                // Fix: Use AddMethodUtf8 to handle passphrase as char[] with UTF-8 encoding
                encGen.AddMethodUtf8(_encryptionOptions.Passphrase.ToCharArray(), HashAlgorithmTag.Sha256);

                using (var encryptedOut = encGen.Open(armoredStream, new byte[1 << 16]))
                {
                    var literalGen = new PgpLiteralDataGenerator();
                    using (var literalOut = literalGen.Open(
                        encryptedOut,
                        PgpLiteralData.Binary,
                        "data",
                        plainBytes.Length,
                        DateTime.UtcNow
                    ))
                    {
                        literalOut.Write(plainBytes, 0, plainBytes.Length);
                    }
                }
            }
            return outputStream.ToArray();
        }


        /// <summary>
        /// Decrypts a PGP encrypted string using the provided passphrase.
        /// </summary>
        /// <param name="encryptedData"></param>
        /// <param name="passphrase"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        /// <exception cref="PgpDataValidationException">Exception thrown in case invalid passphrase was provided</exception>
        public string Decrypt(byte[] encryptedData)
        {
            using var inputStream = PgpUtilities.GetDecoderStream(new MemoryStream(encryptedData));
            var pgpFactory = new PgpObjectFactory(inputStream);
            PgpEncryptedDataList encDataList;

            var pgpObject = pgpFactory.NextPgpObject();
            if (pgpObject is PgpEncryptedDataList)
            {
                encDataList = (PgpEncryptedDataList)pgpObject;
            }
            else
            {
                encDataList = (PgpEncryptedDataList)pgpFactory.NextPgpObject();
            }

            foreach (PgpEncryptedData encryptedDataObj in encDataList.GetEncryptedDataObjects())
            {
                if (encryptedDataObj is PgpPbeEncryptedData pbeData)
                {
                    var clearDataStream = pbeData.GetDataStream(_encryptionOptions.Passphrase.ToCharArray());
                    var plainFactory = new PgpObjectFactory(clearDataStream);
                    var message = plainFactory.NextPgpObject();

                    if (message is PgpLiteralData literalData)
                    {
                        using var dataStream = literalData.GetInputStream();
                        using var reader = new StreamReader(dataStream, Encoding.UTF8);
                        return reader.ReadToEnd();
                    }
                }
            }

            throw new InvalidOperationException("Decryption failed: no suitable encrypted data found.");
        }

        public byte[] DecryptFile(byte[] encryptedData)
        {
            using var inputStream = new MemoryStream(encryptedData);
            using var decoderStream = PgpUtilities.GetDecoderStream(inputStream);

            var pgpFactory = new PgpObjectFactory(decoderStream);
            PgpEncryptedDataList encDataList;

            var firstObj = pgpFactory.NextPgpObject();
            if (firstObj is PgpEncryptedDataList list)
            {
                encDataList = list;
            }
            else
            {
                encDataList = (PgpEncryptedDataList)pgpFactory.NextPgpObject();
            }

            foreach (PgpEncryptedData ed in encDataList.GetEncryptedDataObjects())
            {
                if (ed is PgpPbeEncryptedData pbe)
                {
                    using var clearStream = pbe.GetDataStream(_encryptionOptions.Passphrase.ToCharArray());
                    var plainFactory = new PgpObjectFactory(clearStream);
                    var message = plainFactory.NextPgpObject();

                    if (message is PgpLiteralData literal)
                    {
                        using var literalStream = literal.GetInputStream();
                        using var outputStream = new MemoryStream();
                        Streams.PipeAll(literalStream, outputStream);
                        return outputStream.ToArray();
                    }
                }
            }

            throw new Exception("No suitable encrypted data found or decryption failed.");
        }
    }
}
