import Animated, { Easing, Extrapolation, interpolate, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { useState } from 'react';
import { LayoutChangeEvent, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';

import { useEspAppTheme } from '@/hooks/useEspTheme';
import { MeetingEntity } from '@/database/types/MeetingEntity';
import MeetingItem from './MeetingItem';

export default function MeetingsGroup({
  meetingItems,
  title,
  applyBottomBorder = false,
  applyTopPadding = true,
  applyBottomPadding = true,
}: {
  title: string;
  meetingItems: MeetingEntity[];
  applyBottomBorder?: boolean;
  applyTopPadding?: boolean;
  applyBottomPadding?: boolean;
}) {
  const theme = useEspAppTheme();

  const [isOpen, setIsOpen] = useState(false);
  const contentHeight = useSharedValue(0);
  const progress = useSharedValue(0);

  const groupBorderStyle: ViewStyle = {
    borderBottomWidth: 1,
    borderColor: applyBottomBorder ? theme.colors.border : theme.colors.transparent,
  };

  const groupStyle: ViewStyle = {
    paddingBottom: applyBottomPadding ? 16 : 0,
    paddingTop: applyTopPadding ? 16 : 0,
  };

  const toggle = () => {
    const next = !isOpen;
    setIsOpen(next);
    progress.value = withTiming(next ? 1 : 0, {
      duration: 260,
      easing: Easing.out(Easing.cubic),
    });
  };

  // measure the content height
  const onHiddenLayout = (e: LayoutChangeEvent) => {
    contentHeight.value = e.nativeEvent.layout.height;
  };

  const collapsibleStyle = useAnimatedStyle(() => {
    return {
      height: progress.value * contentHeight.value,
      opacity: interpolate(progress.value, [0, 1], [0, 1], Extrapolation.CLAMP),
    };
  });

  const chevronStyle = useAnimatedStyle(() => {
    const deg = interpolate(progress.value, [0, 1], [0, 90], Extrapolation.CLAMP);
    return { transform: [{ rotate: `${deg}deg` }] };
  });

  return (
    <View style={[groupStyle, applyBottomBorder && !isOpen ? groupBorderStyle : {}]}>
      <TouchableOpacity style={styles.heading} onPress={toggle} activeOpacity={0.8}>
        <ThemedText type="subtitle" style={{ fontWeight: isOpen ? 'bold' : 'normal' }}>
          {title}
        </ThemedText>
        <Animated.View style={chevronStyle}>
          <IconSymbol name="chevron.right" size={18} weight="medium" color={theme.colors.icon} />
        </Animated.View>
      </TouchableOpacity>

      <Animated.View style={[styles.collapse, collapsibleStyle]}>
        <View style={styles.content}>
          {meetingItems.map((item, index) => (
            <MeetingItem
              key={item.id}
              meeting={item}
              applyTopBorderRadius={index === 0}
              applyBottomBorderRadius={index === meetingItems.length - 1}
              showBottomBorder={index !== meetingItems.length - 1}
            />
          ))}
        </View>
      </Animated.View>

      {/* Hidden copy for measuring natural height */}
      <View style={styles.hiddenMeasure} onLayout={onHiddenLayout} pointerEvents="none">
        <View style={styles.content}>
          {meetingItems.map((item) => (
            <MeetingItem key={item.id} meeting={item} />
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  collapse: {
    overflow: 'hidden', // important so content is clipped while height animates
  },
  content: {
    marginTop: 6,
  },
  heading: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'space-between',
    paddingBottom: 10,
    paddingRight: 12,
  },
  hiddenMeasure: {
    opacity: 0,
    position: 'absolute',
    zIndex: -1,
  },
});
