﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate
{
    public class FileContent : GuidEntity, IAggregateRoot
    {
        public byte[] EncryptedContents { get; private set; } = null!;

        //TODO: Check from business owner if thumbnail can be decrypted in database
        public byte[] ThumbnailImageContents { get; private set; } = null!;


        public static FileContent Create(Guid id, byte[] encryptedContents, byte[] thumbnailImageContents)
        {
            Guard.Against.Null(encryptedContents, nameof(encryptedContents), "File contents cannot be null.");
            Guard.Against.Zero(encryptedContents.Length, nameof(encryptedContents), "File contents cannot be empty.");

            Guard.Against.Null(thumbnailImageContents, nameof(thumbnailImageContents), "Thumbnail contents cannot be null.");
            Guard.Against.Zero(thumbnailImageContents.Length, nameof(thumbnailImageContents), "Thumbnail contents cannot be empty.");

            return new FileContent
            {
                Id = id,
                EncryptedContents = encryptedContents,
                ThumbnailImageContents = thumbnailImageContents
            };
        }

        private FileContent() { }
    }
}
