-- VotingsAgenda table

-- WARNIING! meeting_item_tid from t_cr_mp_link is meeting_tid from t_mp_agenda!!!!!!!!!!!!!
/*
t_cr_question
t_cr_mp_link
t_mp_agenda

---------------------------
select 
    meeting_item_tid, 
    question_tid 
from 
    esp.t_cr_mp_link 
LIMIT 1000


select
    * 
from 
    esp.t_mp_meeting m
where 
    m.meeting_tid in (select meeting_item_tid from esp.t_cr_mp_link)


select 
    * 
from 
    esp.t_mp_agenda A
where 
    a.meeting_tid in (select meeting_item_tid from esp.t_cr_mp_link)


select 
    * 
from 
    esp.t_mp_agenda A
where 
    a.meeting_tid in (select meeting_tid from esp.t_mp_meeting where item_type='M')

select * from esp.t_mp_meeting where meeting_tid = 142342232
select * from esp.t_mp_agenda A where a.meeting_tid = 142342232
select * from esp.t_cr_mp_link where meeting_item_tid= 142342233


select count(*) from esp.t_cr_mp_link where meeting_item_tid in (select agenda_tid from esp.t_mp_agenda A where item_type = 'CR')
;
select count(*) from esp.t_cr_mp_link
;
select count(*) from esp.t_mp_agenda a where a.agenda_tid in (select meeting_item_tid from esp.t_cr_mp_link) and a.item_type = 'CR'

select a.*, m.type_tid from esp.t_mp_agenda a 
join esp.t_mp_meeting m on a.meeting_tid = a.meeting_tid and m.item_type = 'CR'
where agenda_tid in (140862782, 142185423) 

select * from esp.t_mp_agenda where meeting_tid = 140862781

select * from esp.t_mp_meeting where meeting_tid in (142342232)
select * from esp.t_mp_agenda where meeting_tid in (142342232)
*/
-- the query itself
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."VotingsAgenda") <> 0 THEN
        RAISE NOTICE 'Delete FROM espv2.VotingsAgenda';
        DELETE FROM espv2."VotingsAgenda";
    END IF;
END $$;

-- Insert dummy agenda for questions without agenda
INSERT INTO espv2."VotingsAgenda" (
    "Id"
    ,"ParentContainerId"
    ,"ORIGINAL_DB_ID"
    ,"Name"
    ,"Description"
    ,"Order"
)
VALUES (
    '00000000-0000-0000-0000-000000000000'
    ,'00000000-0000-0000-0000-000000000000'
    ,0
    ,'Dummy Agenda name'
    ,'Dummy Agenda description'
    ,-999999
);

-- Agendas

INSERT INTO espv2."VotingsAgenda" (
    "Id"
    ,"ParentContainerId"
    ,"ORIGINAL_DB_ID"
    ,"Name"
    ,"Description"
    ,"Order"
)
SELECT
    gen_random_uuid() AS Id
    ,(SELECT vc."Id" FROM espv2."VotingsContainer" vc WHERE a.meeting_tid = vc."ORIGINAL_DB_ID") AS ParentContainerId
    ,a.agenda_tid AS ORIGINAL_DB_ID
    ,COALESCE(a.name, '--NAME MUST NOT BE EMPTY!--') AS "Name"
    ,a.description AS "Description"
    ,a.ord AS "Order"
    -- TODO: add/update permissions later
FROM 
    esp.t_mp_agenda a 
WHERE
    a.agenda_tid IN (SELECT meeting_item_tid FROM esp.t_cr_mp_link) AND a.item_type = 'CR';

/*
select * from esp.t_mp_agenda a WHERE a.agenda_tid in (71155706);
select * from esp.t_mp_meeting where meeting_tid = 71155705
select * from espv2."VotingsContainer" where "Id" = '68fdd20d-7f94-4b73-8adc-761c4b6428f7';
select * from espv2."VotingsAgenda" where "ParentContainerId" = '68fdd20d-7f94-4b73-8adc-761c4b6428f7'
select * from espv2."VotingsAgenda" where "ORIGINAL_DB_ID"=141339501
*/

