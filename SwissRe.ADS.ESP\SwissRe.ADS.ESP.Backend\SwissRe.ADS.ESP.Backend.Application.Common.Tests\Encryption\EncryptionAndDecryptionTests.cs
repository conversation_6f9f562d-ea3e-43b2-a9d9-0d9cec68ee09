﻿using Microsoft.Extensions.Options;
using Org.BouncyCastle.Bcpg.OpenPgp;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using System.Text;

namespace SwissRe.ADS.ESP.Backend.Application.Common.Tests.Encryption
{
    [TestFixture]
    public class EncryptionAndDecryptionTests
    {
        private EspEncryptionService espEncryptionService = null!;

        [SetUp]
        public void SetUp()
        {
            var configOptions = new EspEncryptionConfigurationOptions() { Passphrase = "StrongPassphrase123!" };
            var options = Options.Create(configOptions);
            espEncryptionService = new EspEncryptionService(options);
        }

        [Test]
        public void Encrypt_String_ReturnsNonEmptyByteArray()
        {
            // Arrange
            var plainText = "Test message";

            // Act
            var encrypted = espEncryptionService.Encrypt(plainText);
            var encryptedDataAsPlainText = Encoding.UTF8.GetString(encrypted);

            // Assert
            Assert.That(encrypted, Is.Not.Null);
            Assert.That(encrypted, Is.Not.Empty);
            Assert.That(encryptedDataAsPlainText, Is.Not.EqualTo(plainText), "Encrypted data should not be equal to plain text.");
        }

        [Test]
        public void Decrypt_ValidateConsistent_Decryption_Thorought_Package_Upgrades()
        {
            //The following test is written to ensure that despite package ugprades we will still de-crypt the original values stored in the database the same way
            //Original password was: StrongPassphrase123!
            var encryptedText = File.ReadAllBytes("Encryption\\EncryptedText.txt");

            //Act
            var decrypted = espEncryptionService.Decrypt(encryptedText);

            //Assert
            Assert.That(decrypted, Is.EqualTo("Test message"));
        }

        [Test]
        public void Encrypt_ByteArray_ReturnsNonEmptyByteArray()
        {
            // Arrange
            var plainBytes = Encoding.UTF8.GetBytes("Test message");
            var passphrase = "StrongPassphrase123!";

            // Act
            var encrypted = espEncryptionService.Encrypt(plainBytes);

            // Assert
            Assert.That(encrypted, Is.Not.Null);
            Assert.That(encrypted, Is.Not.Empty);
            Assert.That(encrypted.Length, Is.Not.EqualTo(plainBytes.Length));
        }

        [Test]
        public void Encrypt_SameInputDifferentOutputs()
        {
            // Arrange
            var plainText = "Test message";

            // Act
            var encrypted1 = espEncryptionService.Encrypt(plainText);
            var encrypted2 = espEncryptionService.Encrypt(plainText);

            // Assert
            Assert.That(encrypted1, Is.Not.EqualTo(encrypted2));
        }

        [Test]
        public void Encrypt_Then_Decrypt_String_RoundTrip_Success()
        {
            // Arrange
            var plainText = "Sensitive data for encryption";

            // Act
            var encrypted = espEncryptionService.Encrypt(plainText);
            var decrypted = espEncryptionService.Decrypt(encrypted);

            // Assert
            Assert.That(decrypted, Is.EqualTo(plainText));
        }

        [Test]
        public void Encrypt_Then_Decrypt_Bytes_RoundTrip_Success()
        {
            // Arrange
            var plainBytes = Encoding.UTF8.GetBytes("Another secret");

            // Act
            var encrypted = espEncryptionService.Encrypt(plainBytes);
            var decrypted = espEncryptionService.Decrypt(encrypted);

            // Assert
            Assert.That(decrypted, Is.EqualTo("Another secret"));
        }

        [Test]
        public void Decrypt_With_Wrong_Passphrase_Throws()
        {
            // Arrange
            var plainText = "Data";
            var encrypted = espEncryptionService.Encrypt(plainText);

            ChangePassphrase("WrongPassword");
            
            
            // Pgp Validation exception means that passphrase is incorrect
            Assert.Throws<PgpDataValidationException>(() =>
            {
                espEncryptionService.Decrypt(encrypted);
            });
        }

        private void ChangePassphrase(string newPassphrase)
        {
            var configOptions = new EspEncryptionConfigurationOptions() { Passphrase = newPassphrase };
            var options = Options.Create(configOptions);
            espEncryptionService = new EspEncryptionService(options);
        }
    }
}
