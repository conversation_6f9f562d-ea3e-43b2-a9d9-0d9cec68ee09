﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class EspDomainConfiguration : IEntityTypeConfiguration<EspDomain>
    {
        public void Configure(EntityTypeBuilder<EspDomain> builder)
        {
            builder.ToTable("Domains");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion(); // All aggregate roots should have a row version.

            builder.Property(x => x.Name).IsRequired().HasMaxLength(512);
            builder.Property(x => x.Description).HasMaxLength(5092);
            builder.Property(x => x.Order).HasDefaultValue(1);

            builder.HasOne(x => x.Parent)
                .WithMany()
                .HasForeignKey(x => x.ParentId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent cascading delete to avoid accidental data loss

            builder.Navigation(p => p.DomainRoles).AutoInclude();
        }
    }
}
