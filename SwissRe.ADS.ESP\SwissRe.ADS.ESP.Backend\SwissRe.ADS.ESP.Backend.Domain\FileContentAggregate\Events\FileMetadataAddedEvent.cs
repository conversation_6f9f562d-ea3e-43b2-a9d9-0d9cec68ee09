﻿using SwissRe.ADS.Ddd.Events.Abstractions;

namespace SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.Events
{
    public class FileMetadataAddedEvent(byte[] fileContents, Guid fileContentId) : IEvent
    {
        public static string TypeCode => "FileMetadataAdded";

        public byte[] FileContents { get; } = fileContents;

        /// <summary>
        /// Id of the new FileContent entity, that will be saved into the database
        /// </summary>
        public Guid FileContentId { get; } = fileContentId;
    }
}
