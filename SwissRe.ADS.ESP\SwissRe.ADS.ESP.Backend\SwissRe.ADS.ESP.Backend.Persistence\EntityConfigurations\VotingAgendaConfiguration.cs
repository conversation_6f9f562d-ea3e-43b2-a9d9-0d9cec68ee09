﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    internal class VotingAgendaConfiguration : IEntityTypeConfiguration<VotingAgenda>
    {
        public void Configure(EntityTypeBuilder<VotingAgenda> builder)
        {
            builder.ToTable("VotingsAgenda");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.Property(x => x.Name).IsRequired().HasMaxLength(512);
            builder.Property(x => x.Description).HasMaxLength(5092);

            builder.OwnsMany(a => a.Permissions,
                ownedNav =>
                {
                    ownedNav.ToJson("Permissions");
                    ownedNav.Property(p => p.CurrentPermission).HasConversion<int>();
                    ownedNav.Property(p => p.InheritedPermission).HasConversion<int>();
                });

            //builder.HasIndex(x => x.VotingPermissions).HasMethod("gin");



            builder.HasMany(x => x.VotingQuestions)
                .WithOne()
                .HasForeignKey(x => x.AgendaId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany<VotingFileMetadata>()
                .WithOne()
                .HasForeignKey(x => x.VotingAgendaId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Navigation(x => x.VotingQuestions).AutoInclude();
        }
    }
}
