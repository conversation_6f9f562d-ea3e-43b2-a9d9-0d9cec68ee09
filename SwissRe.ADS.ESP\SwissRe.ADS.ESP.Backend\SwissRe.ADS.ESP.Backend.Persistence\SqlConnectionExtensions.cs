﻿using Azure.Core;
using Azure.Identity;
using Microsoft.Data.SqlClient;

namespace SwissRe.ADS.ESP.Backend.Persistence
{
    public static class SqlConnectionExtensions
    {
        // TODO: remove - put this into connection string -- https://learn.microsoft.com/en-us/sql/connect/ado-net/sql/azure-active-directory-authentication?view=sql-server-ver16
        public static SqlConnection WithCredentials(this SqlConnection connection)
        {
            if (!connection.ConnectionString.Contains("mssqllocaldb", StringComparison.InvariantCultureIgnoreCase)
                && !connection.ConnectionString.Contains("localhost", StringComparison.InvariantCultureIgnoreCase))
            {
                var tokenRequestContext = new TokenRequestContext(["https://database.windows.net/.default"]);
                connection.AccessToken = new DefaultAzureCredential().GetTokenAsync(tokenRequestContext).AsTask().Result.Token;
            }
            return connection;
        }
    }
}
