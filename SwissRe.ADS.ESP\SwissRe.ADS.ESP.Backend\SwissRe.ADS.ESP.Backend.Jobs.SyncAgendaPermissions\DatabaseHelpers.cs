﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Persistence;

namespace SwissRe.ADS.ESP.Backend.Jobs.SyncAgendaPermissions
{
    public class DatabaseHelpers(AppDbContext context)
    {
        private readonly AppDbContext _context = context;

        public async Task<List<Guid>> GetAccessibleMeetingIdsForUser(string SrUserId, CancellationToken cancellationToken = default)
        {
            //we consider even deny access as user might have altogether lost complete access to the specific meeting container
            var accessibleContainerIds = _context.Set<MeetingAgenda>()
                .Where(agenda => agenda.Permissions.Any(p => p.UserId == SrUserId))
                .Select(a => a.ParentContainerId)
            .Distinct();

            var meetingIds = await _context.Set<MeetingContainer>()
                .Where(x => accessibleContainerIds.Contains(x.Id))
                .AsNoTracking()
                .Select(x => x.Id)
                .ToListAsync(cancellationToken);

            return meetingIds;
        }
    }
}
