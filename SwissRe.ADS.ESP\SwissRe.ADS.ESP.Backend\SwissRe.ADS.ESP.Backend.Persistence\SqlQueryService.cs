﻿using Dapper;
using Npgsql;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;

namespace SwissRe.ADS.ESP.Backend.Persistence
{
    public class SqlQueryService(NpgsqlConnection connection) : ISqlQueryService
    {
        private readonly NpgsqlConnection _connection = connection;

        public Task<IEnumerable<TResult>> QueryAsync<TResult>(string sql, object? parameters = null) =>
            _connection.QueryAsync<TResult>(sql, parameters);

        public Task<TResult> QueryFirstAsync<TResult>(string sql, object? parameters = null) =>
            _connection.QueryFirstAsync<TResult>(sql, parameters);

        public Task<TResult?> QueryFirstOrDefaultAsync<TResult>(string sql, object? parameters = null) =>
            _connection.QueryFirstOrDefaultAsync<TResult>(sql, parameters);

        public Task<TResult?> QueryScalarAsync<TResult>(string sql, object? parameters = null) =>
            _connection.ExecuteScalarAsync<TResult>(sql, parameters);
    }
}
