﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <TransformOnBuild>true</TransformOnBuild>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

	<ItemGroup>
		<None Update="appsettings.development.json" CopyToOutputDirectory="PreserveNewest" Condition="Exists('appsettings.development.json')" />
	</ItemGroup>
	
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.6" />
    <PackageReference Include="SwissRe.ADS.Ddd.Events.Dispatching" Version="2.1.1" />
    <PackageReference Include="SwissRe.ADS.Ddd.Events.Dispatching.Abstractions" Version="2.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Application.Common\SwissRe.ADS.ESP.Backend.Application.Common.csproj" />
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Domain\SwissRe.ADS.ESP.Backend.Domain.csproj" />
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Jobs.Common\SwissRe.ADS.ESP.Backend.Jobs.Common.csproj" />
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Persistence\SwissRe.ADS.ESP.Backend.Persistence.csproj" />
  </ItemGroup>

</Project>
