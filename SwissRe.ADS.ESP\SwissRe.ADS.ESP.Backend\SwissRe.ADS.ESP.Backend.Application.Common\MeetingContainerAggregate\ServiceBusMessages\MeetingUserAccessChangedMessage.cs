﻿using SwissRe.ADS.ServiceBus;
using SwissRe.ADS.ServiceBus.MessageConfiguration;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate.ServiceBusMessages
{
    public record MeetingUserAccessChangedMessage(Guid MeetingId, string UserId) : IMessageBody
    {
        public static void ConfigureMessage(MessageConfigBuilder builder)
        {
            builder
                .ToQueue("meeting-useraccess-changed");
        }
    }
}
