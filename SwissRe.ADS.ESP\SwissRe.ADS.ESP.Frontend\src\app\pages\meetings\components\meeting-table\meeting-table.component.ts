import { Component, input } from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef, ValueGetterParams } from 'ag-grid-community';
import { ContainerTypeEnum, IGetAdminVotingResponse, IGetContainerResponseBase } from '../../../../shared/apiServices';
import { AdditionalInfoCellRendererComponent } from '../../../../shared/agGridComponents/additional-info-cell-renderer/additional-info-cell-renderer.component';
import { StatusCellRendererComponent } from '../../../../shared/agGridComponents/status-cell-renderer/status-cell-renderer.component';

@Component({
  selector: 'app-meeting-table',
  imports: [AgGridAngular],
  templateUrl: './meeting-table.component.html',
  styleUrl: './meeting-table.component.scss'
})
export class MeetingTableComponent {
  readonly data = input.required<IGetContainerResponseBase[]>();

  columnDefs: ColDef[] = [
    {
      field: 'domain.parent',
      rowGroup: true,
      hide: true,
      colId: 'group',
      headerName: 'Group',
      valueGetter: params => params.data?.domain?.parent?.name || 'No Group'
    },
    { field: 'domain.name', colId: 'type', headerName: 'Type', rowGroup: true, hide: true },
    {
      field: 'startTime',
      colId: 'date',
      headerName: 'Date',
      valueGetter: val => {
        return this.formatDate(val.data?.startTime);
      }
    },
    { field: 'state', colId: 'status', headerName: 'Status', cellRenderer: StatusCellRendererComponent },
    { field: 'title', colId: 'title', headerName: 'Title' },
    { colId: 'additionalInfo', headerName: 'Additional Info', cellRenderer: AdditionalInfoCellRendererComponent },
    { colId: 'votingStatus', headerName: 'Status of voting', valueGetter: this.votingStatusValueGetter.bind(this) }
  ];

  defaultColDef: ColDef = {
    flex: 1
  };

  private votingStatusValueGetter(params: ValueGetterParams<IGetContainerResponseBase>) {
    const data = params.data;

    if (!data) {
      return;
    }

    if (data.containerType === ContainerTypeEnum.VotingContainer) {
      const votingContainer = data as IGetAdminVotingResponse;
      return `${votingContainer.totalUsersVoted} / ${votingContainer.totalUsersWithVotingRights}`;
    }

    return '-';
  }

  private formatDate(date: Date | string) {
    if (!date) {
      return;
    }

    date = new Date(date);

    const day = String(date.getDate()).padStart(2, '0');
    const month = date.toLocaleString('en-US', { month: 'long' });
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
  }
}
