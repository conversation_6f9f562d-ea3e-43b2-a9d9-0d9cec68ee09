﻿using SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata;

namespace SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate
{
    public class VotingAnswerSupportingDocumentFileMetadata : FileMetadataBase
    {
        private VotingAnswerSupportingDocumentFileMetadata() { }

        public static VotingAnswerSupportingDocumentFileMetadata Create(string originalFileName,
           Guid fileGroupId,
           Guid fileContentId,
           int versionNumber,
           string fileExtension,
           int size,
           int numberOfPages,
           int order,
           string createdByUserId,
           bool supplementary = false)
        {
            var result = new VotingAnswerSupportingDocumentFileMetadata();
            result.Initialize(originalFileName, fileGroupId, fileContentId, versionNumber, fileExtension, size, numberOfPages, order, createdByUserId, supplementary);
            return result;
        }
    }
}
