import { FlattenedAgenda } from '@/utils/FlattenAgendas';
import { useEffect, useRef, useState } from 'react';
import { NativeScrollEvent, NativeSyntheticEvent } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

/**
 * Hook: useAgendaHighlight
 * -------------------------
 * Manages agenda highlighting and scrolling behavior for a MeetingDetail screen.
 *
 * Responsibilities:
 * - Stores positions of all agendas using onLayout measurements.
 * - Tracks the currently active agenda based on scroll position.
 * - Provides scroll-to-agenda functionality for sidebar clicks.
 * - Handles automatic highlighting of the first agenda.
 *
 * Usage:
 * const {
 *   scrollViewRef,
 *   currentAgendaId,
 *   handleScroll,
 *   handleSelectAgenda,
 *   handleAgendaLayout
 * } = useAgendaHighlight(flatAgendas);
 *
 * Notes:
 * - Keeps all scrolling and highlight logic out of the screen component.
 * - Works with flat or nested agendas (flattened before passing to the hook).
 */
export function useAgendaHighlight(flatAgendas: FlattenedAgenda[]) {
  const scrollViewRef = useRef<ScrollView>(null);
  const agendaPositions = useRef<Record<string, number>>({});
  const [currentAgendaId, setCurrentAgendaId] = useState<string | null>(null);

  const handleAgendaLayout = (id: string, y: number) => {
    agendaPositions.current[id] = y;
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    const scrollY = event.nativeEvent.contentOffset.y;
    let activeId: string | null = null;

    // Check if at end
    const isAtTop = scrollY <= 0;
    const isAtEnd = scrollY + layoutMeasurement.height >= contentSize.height - 1;

    if (isAtEnd || isAtTop) {
      return;
    }

    for (let i = 0; i < flatAgendas.length; i++) {
      const { agenda } = flatAgendas[i];
      const pos = agendaPositions.current[agenda.id];
      if (pos !== undefined && scrollY >= pos - 50) {
        activeId = agenda.id;
      }
    }
    if (activeId !== currentAgendaId) {
      setCurrentAgendaId(activeId);
    }
  };

  const handleSelectAgenda = (agendaId: string) => {
    const y = agendaPositions.current[agendaId];
    if (y !== undefined) {
      scrollViewRef.current?.scrollTo({ y, animated: true });
    }
  };

  useEffect(() => {
    setCurrentAgendaId(flatAgendas[0]?.agenda.id ?? null);
  }, [flatAgendas]);

  return { scrollViewRef, currentAgendaId, handleScroll, handleSelectAgenda, handleAgendaLayout };
}
