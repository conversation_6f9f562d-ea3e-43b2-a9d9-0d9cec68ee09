﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DomainAggregate
{
    public class User : StringEntity, IAggregateRoot, ISyncJobEntity
    {
        private List<UserEspRole> _userRoles = [];
        public Guid AzureUserId { get; private set; } = Guid.Empty;
        public string Email { get; private set; } = null!;
        public string FullName { get; private set; } = null!;
        public bool IsActive { get; private set; }
        public string? JobTitle { get; private set; }
        public DateTime LastModifiedOnUTC { get; private set; }
        public IReadOnlyCollection<UserEspRole> UserRoles => _userRoles.AsReadOnly();

        //Database auto generated sequence ID for sync jobs
        public long SequenceId { get; }

        public static User Create(string userId, string email, string fullName, bool isActive = true, string? jobTitle = null)
        {
            Guard.Against.NullOrWhiteSpace(email, nameof(Email), "User email cannot be null or empty.");
            Guard.Against.NullOrWhiteSpace(fullName, nameof(FullName), "User full name cannot be null or empty.");
            Guard.Against.NullOrWhiteSpace(userId, nameof(Id), "User ID cannot be null or empty.");

            return new User() { Id = userId, Email = email, FullName = fullName, LastModifiedOnUTC = DateTime.UtcNow, IsActive = isActive, JobTitle = jobTitle };
        }

        public void UpdateUser(string email, string fullName)
        {
            Guard.Against.NullOrWhiteSpace(email, nameof(Email), "User email cannot be null or empty.");
            Guard.Against.NullOrWhiteSpace(fullName, nameof(FullName), "User full name cannot be null or empty.");

            Email = email;
            FullName = fullName;
            LastModifiedOnUTC = DateTime.UtcNow;
        }

        public void AddRole(Guid roleId)
        {
            if (_userRoles.Any(x => x.RoleId == roleId))
                return;

            _userRoles.Add(new UserEspRole(this.Id,roleId));
        
            LastModifiedOnUTC = DateTime.UtcNow;
        }

        private User() { }
    }
}
