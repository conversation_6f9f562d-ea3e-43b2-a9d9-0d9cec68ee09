﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.JobExecutionAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class JobExecutionMetadataConfiguration : IEntityTypeConfiguration<JobExecutionMetadata>
    {
        public void Configure(EntityTypeBuilder<JobExecutionMetadata> builder)
        {
            builder.ToTable("__JobExecutionMetadata");

            builder.Property(x => x.Id).IsRequired().HasMaxLength(100);
            builder.Property(m => m.LastUpdatedAt).IsRequired();
        }
    }
}
