﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="BouncyCastle.Cryptography" Version="2.6.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
		<PackageReference Include="SwissRe.ADS.ServiceBus" Version="11.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Application.Common\SwissRe.ADS.ESP.Backend.Application.Common.csproj" />
		<ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Domain\SwissRe.ADS.ESP.Backend.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" /> <!--https://learn.microsoft.com/en-us/aspnet/core/fundamentals/target-aspnetcore?view=aspnetcore-3.1&tabs=visual-studio#use-the-aspnet-core-shared-framework-->
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="VotingAggregate\Queries\" />
	</ItemGroup>

</Project>
