﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.VotingsAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using System.Linq.Expressions;

namespace SwissRe.ADS.ESP.Backend.Persistence.ReadModels
{
    public class VotingReadModelRepository(AppDbContext context) : IVotingReadModelRepository
    {
        private readonly AppDbContext _context = context;

        public async Task<GetVotingDetailResponse> GetVotingDetailById(Guid meetingContainerId, string SrUserId, CancellationToken cancellationToken = default)
        {
            var votingData = await _context.Set<VotingContainer>()
                .Where(m => m.Id == meetingContainerId)
                .Select(VotingContainerProjection(SrUserId))
                .AsNoTracking()
                .FirstOrDefaultAsync(cancellationToken);

            if (votingData == null)
                throw new ArgumentOutOfRangeException("Meeting was not found");

            return votingData.ConvertToResponse();
        }

        public async Task<PagedVotingDetailResponse> GetPagedVotingDetailsForUser(string SrUserId, DateTime lastSyncedOn, int take = 50, int skip = 0, CancellationToken cancellationToken = default)
        {
            //TODO: Use IMemoryCache for 30 minutes (not sliding expiration) to cache the accessible container IDs for the user.

            // Move skip before take for correct paging and performance
            var accessibleContainerIdsQuery = _context.Set<MeetingAgenda>()
                .Where(AgendaPermissionExtensions.HasReadAccessFor<MeetingAgenda>(SrUserId))
                .Select(a => a.ParentContainerId)
                .Distinct();

            // Materialize accessibleContainerIds to a HashSet for efficient Contains checks
            var accessibleContainerIds = await accessibleContainerIdsQuery.ToListAsync(cancellationToken).ConfigureAwait(false);
            if (accessibleContainerIds.Count == 0)
                return new PagedVotingDetailResponse { Meetings = [], HasMore = false };

            var userMeetings = await _context.Set<VotingContainer>()
                .Where(m => accessibleContainerIds.Contains(m.Id) && m.LastUpdatedOnUTC > lastSyncedOn)
                .OrderBy(m => m.Id)
                .Skip(skip)
                .Take(take + 1)
                .Select(VotingContainerProjection(SrUserId))
                .AsNoTracking()
                .ToListAsync(cancellationToken)
                .ConfigureAwait(false);

            if (userMeetings.Count == 0)
                return new PagedVotingDetailResponse { Meetings = [], HasMore = false };

            var hasMore = userMeetings.Count > take;
            var items = userMeetings.Take(take).Select(meeting => meeting.ConvertToResponse());

            return new PagedVotingDetailResponse
            {
                Meetings = items,
                HasMore = hasMore
            };
        }

        private static Expression<Func<VotingContainer, VotingContainerProjectionDto>> VotingContainerProjection(string srUserId)
        {
            return m => new VotingContainerProjectionDto
            {
                Id = m.Id,
                Name = m.Name,
                Description = m.Description,
                State = m.State,
                Agendas = m.Agendas
                    .AsQueryable()
                    .Where(AgendaPermissionExtensions.HasReadAccessFor<VotingAgenda>(srUserId))
                    .Select(a => new VotingContainerProjectionDto.VotingAgendaProjectionDto
                    {
                        Id = a.Id,
                        Name = a.Name,
                        Description = a.Description,
                        Order = a.Order,
                    }).ToList()
            };
        }

        private class VotingContainerProjectionDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = default!;
            public string? Description { get; set; }
            public ContainerStateEnum State { get; set; }
            public List<VotingAgendaProjectionDto> Agendas { get; set; } = [];

            internal class VotingAgendaProjectionDto
            {
                public Guid Id { get; set; }
                public string Name { get; set; } = default!;
                public string? Description { get; set; }
                public int Order { get; set; }
            }

            public GetVotingDetailResponse ConvertToResponse()
            {
                var convertedAgendas = Agendas
                    .Select(a => new GetVotingAgendaResponse
                    {
                        Id = a.Id,
                        Name = a.Name,
                        Description = a.Description,
                        Order = a.Order
                    })
                    .ToList();

                return new GetVotingDetailResponse
                {
                    Id = Id,
                    Name = Name,
                    Description = Description,
                    State = State,
                    Agendas = convertedAgendas
                };
            }
        }
    }
}
