import { Component, computed, inject } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { CurrentUserService } from '../../../shared/current-user.service';
import { CommonModule } from '@angular/common';
import { AppHeaderConfig } from '../config';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-header-right-section-mobile',
  imports: [CommonModule, MatToolbarModule, MatIconModule, MatTooltipModule, MatMenuModule, MatDividerModule, MatButtonModule, MatDividerModule],
  template: `
    <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="menu">
      <mat-icon>more_vert</mat-icon>
    </button>

    <mat-menu #menu="matMenu">
      @if (environment(); as environment) {
        <button mat-menu-item disabled>{{ environment }}</button>
      }

      <button mat-menu-item disabled>
        <mat-icon>account_circle</mat-icon>
        <span>{{ currentUser()?.fullName }}</span>
      </button>

      <mat-divider></mat-divider>

      @for (action of mainActions(); track action.key) {
        <button mat-menu-item [disabled]="action.enabled === false" (click)="action.action()">
          <mat-icon>{{ action.icon }}</mat-icon>
          <span>{{ action.title }}</span>
        </button>
      }

      @for (menuItem of menuItems(); track menuItem.key) {
        <button mat-menu-item [disabled]="menuItem.enabled === false" (click)="menuItem.action()">
          <mat-icon>{{ menuItem.icon }}</mat-icon>
          <span>{{ menuItem.title }}</span>
        </button>
      }
    </mat-menu>
  `
})
export class AppHeaderRightSectionMobileComponent {
  readonly currentUser = toSignal(inject(CurrentUserService).currentUser$);

  private readonly appHeader = toSignal(inject(AppHeaderConfig).appHeader$);

  readonly mainActions = computed(() => this.appHeader()?.mainActions?.filter(action => action.visible !== false) || []);
  readonly menuItems = computed(() => this.appHeader()?.menuItems?.filter(item => item.visible !== false) || []);

  readonly environment = computed(() => {
    var appHeader = this.appHeader();
    return [appHeader?.environmentName, appHeader?.appVersion].filter(x => x).join(': ');
  });
}
