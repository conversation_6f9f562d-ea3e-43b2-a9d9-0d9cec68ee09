﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate
{
    public class VotingAgenda : AgendaBase
    {
        private List<VotingFileMetadata> _files = [];
        private List<VotingQuestion> _votingQuestions = [];

        /// <summary>
        /// Foreign Key reference to VotingContainer
        /// </summary>
        public Guid ParentContainerId { get; private set; }

        public string Name { get; private set; } = null!;
        public string? Description { get; private set; }
        public int Order { get; private set; }

        public IReadOnlyList<VotingQuestion> VotingQuestions => _votingQuestions;

        public IReadOnlyCollection<VotingFileMetadata> Files => _files;

        private VotingAgenda() { }

        public static VotingAgenda Create(Guid parentContainerId, string name, string? description = null)
        {
            return new VotingAgenda()
            {
                ParentContainerId = parentContainerId,
                Name = name,
                Description = description,
                Order = 0
            };
        }

        internal void SetOrder(int order)
        {
            Guard.Against.Negative(order, nameof(order), "Order cannot be less than zero.");
            Order = order;
        }

        internal void AddQuestion(string question, byte[]? descriptionEncrypted, VotingOptions availableOptions, string createdByUserId)
        {
            Guard.Against.NullOrWhiteSpace(question, nameof(question), "Question cannot be blank.");
            Guard.Against.Zero(availableOptions?.Options?.Count ?? 0, nameof(availableOptions), "Available options cannot be null.");

            var votingQuestion = VotingQuestion.Create(Id, question, descriptionEncrypted, availableOptions!, createdByUserId);
            _votingQuestions.Add(votingQuestion);
        }

        internal void UpdateQuestion(Guid votingQuestionId, string newQuestion, byte[]? newDescriptionEncrypted, VotingOptions? newAvailableOptions, string updatedByUserId)
        {
            Guard.Against.NullOrWhiteSpace(newQuestion, nameof(newQuestion), "New question cannot be blank.");
            Guard.Against.Null(newAvailableOptions, nameof(newAvailableOptions), "New available options cannot be null.");
            Guard.Against.Zero(newAvailableOptions.Options.Count, nameof(newAvailableOptions.Options), "New available options must have at least one option.");

            var question = _votingQuestions.FirstOrDefault(q => q.Id == votingQuestionId);
            if (question == null)
                throw new NullReferenceException("Voting question was not found.");

            question.UpdateQuestion(newQuestion, newDescriptionEncrypted, updatedByUserId);
            question.UpdateVotingOptions(newAvailableOptions, updatedByUserId);
        }
        internal void AddQuestionSupportingDocument(Guid votingQuestionId, byte[] unencryptedFileContents, string fileName, string extension, int size, int order, int numberOfPages, string createdByUserId, bool isSupplementaryFile = false)
        {
            Guard.Against.Null(unencryptedFileContents, "FileContents", "File contents cannot be null.");
            Guard.Against.Zero(unencryptedFileContents.Length, "FileContents", "File contents cannot be empty.");

            var question = _votingQuestions.FirstOrDefault(q => q.Id == votingQuestionId);
            if (question == null)
                throw new NullReferenceException("Voting question was not found.");

            question.AddSupportingDocument(unencryptedFileContents, fileName, extension, size, order, numberOfPages, createdByUserId, isSupplementaryFile);
        }

        internal void RemoveQuestionSupportingDocument(Guid votingQuestionId, Guid supportingDocumentId)
        {
            var question = _votingQuestions.FirstOrDefault(q => q.Id == votingQuestionId);
            if (question == null)
                throw new NullReferenceException("Voting question was not found.");

            throw new NotImplementedException();
        }

        internal void LockQuestionOnUserAnswer(Guid votingQuestionId)
        {
            var question = _votingQuestions.FirstOrDefault(q => q.Id == votingQuestionId);
            if (question == null)
                throw new NullReferenceException("Voting question was not found.");

            question.LockForEditing();
        }
    }
}
