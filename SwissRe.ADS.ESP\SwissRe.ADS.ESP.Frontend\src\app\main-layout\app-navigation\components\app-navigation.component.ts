import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppNavigationItemListComponent } from './app-navigation-item-list.component';
import { AppNavigationConfig } from '../config';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-navigation',
  imports: [CommonModule, AppNavigationItemListComponent],
  template: `<app-navigation-item-list [items]="appNavigationConfig()?.items || []"></app-navigation-item-list>`
})
export class AppNavigationComponent {
  readonly appNavigationConfig = toSignal(inject(AppNavigationConfig).appNavigation$);
}
