﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAggregate.Commands
{
    public record CreateVotingAgendaCommand(string name, string description);

    public class CreateVotingAgendaEndpoint(UnitOfWork unitOfWork, IAgendaAuthorizationService agendaPermissionService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;

        public static void BuildRoute([EndpointRouteBuilder<VotingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapPost("CreateVotingAgenda", (CreateVotingAgendaCommand command, Guid containerId, CreateVotingAgendaEndpoint endpoint) => endpoint.HandleAsync(command, containerId))
                .WithAngularName<VotingContainerRouteGroup>("CreateVotingAgenda");

        public async Task HandleAsync(CreateVotingAgendaCommand command, Guid containerId)
        {
            var canCreateAgenda = await _agendaPermissionService.CanUserAddNewAgendaAsync(containerId, AgendaTypeEnum.VotingAgenda);
            if (canCreateAgenda == false)
                throw new UnauthorizedAccessException("Current user does not have permission to create a new agenda in this container.");

            var container = await _unitOfWork.Repository<VotingContainer>().GetAsync(containerId, true);

            var item = VotingAgenda.Create(containerId, command.name, command.description);

            container.AddAgenda(item);

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
