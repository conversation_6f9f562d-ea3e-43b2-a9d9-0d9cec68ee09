import { NavigationExtras } from '@angular/router';

export interface AppNavigation {
  items: AppNavigationItem[];
}

export type AppNavigationItem = AppNavigationCategoryItem | AppNavigationActionItem;

export interface AppNavigationCategoryItem {
  type: 'category';
  /** Unique constant identifier of the navigation item among all items in the app navigation. */
  key: AppNavigationItemKey;
  title: string;
  icon?: string;
  visible?: boolean;
  children: AppNavigationItem[];
  defaultExpanded?: boolean;
}

export interface AppNavigationActionItem {
  type: 'action';
  /** Unique constant identifier of the navigation item among all items in the app navigation. */
  key: AppNavigationItemKey;
  title: string;
  icon?: string;
  visible?: boolean;
  action: AppNavigationItemAction;
}

export type AppNavigationItemKey = unknown;

export type AppNavigationItemAction = AppNavigationRouterLink | AppNavigationExternalLink | AppNavigationFunction;

export type AppNavigationRouterLink = { type: 'routerLink'; commands: unknown[]; extras?: NavigationExtras };
export type AppNavigationExternalLink = { type: 'externalLink'; url: string; target?: AppNavigationExternalLinkTarget };
export type AppNavigationExternalLinkTarget = '_blank' | '_parent' | '_self' | '_top';
export type AppNavigationFunction = { type: 'function'; function: () => void };
