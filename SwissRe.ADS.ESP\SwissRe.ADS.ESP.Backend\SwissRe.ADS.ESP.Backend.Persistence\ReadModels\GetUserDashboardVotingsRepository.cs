﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.Common.Containers;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.ReadModels
{
    public class GetUserDashboardVotingsRepository(AppDbContext context, ICurrentUser currentUser) : IGetUserDashboardVotingsRepository
    {
        private readonly AppDbContext _context = context;
        private readonly ICurrentUser _currentUser = currentUser;

        public async Task<List<GetUserVotingResponse>> GetCurrentAndFutureUserVotings(CancellationToken cancellationToken = default)
        {
            return await GetUserDashboardMeetingsByState(ContainerStateEnum.Published, cancellationToken);
        }

        public async Task<List<GetUserVotingResponse>> GetPastUserVotings(CancellationToken cancellationToken = default)
        {
            return await GetUserDashboardMeetingsByState(ContainerStateEnum.PastMeeting, cancellationToken);
        }

        private async Task<List<GetUserVotingResponse>> GetUserDashboardMeetingsByState(ContainerStateEnum containerState, CancellationToken cancellationToken = default)
        {
            
            var accessibleVotingContainerIds = _context.Set<VotingAgenda>()
                .Where(AgendaPermissionExtensions.HasReadAccessFor<VotingAgenda>(_currentUser.SrUserId))
                .Select(a => a.ParentContainerId)
                .Distinct();


            var votingQuery = from voting in _context.Set<VotingContainer>().Where(x => accessibleVotingContainerIds.Contains(x.Id) && x.State == containerState)
                              join domain in _context.Set<EspDomain>() on voting.DomainId equals domain.Id
                              join parentDomain in _context.Set<EspDomain>() on domain.ParentId equals parentDomain.Id into parentDomains
                              from parentDomain in parentDomains.DefaultIfEmpty()
                              join agenda in _context.Set<VotingAgenda>() on voting.Id equals agenda.ParentContainerId
                              join questions in _context.Set<VotingQuestion>() on agenda.Id equals questions.AgendaId
                              join userVote in _context.Set<VotingAnswer>()
                                  .Where(v => v.IsLatest && ((v.CreatedByUserId == _currentUser.SrUserId && v.OnBehalfOfDocumenMetadatatId == null) || (v.CreatedByUserId != _currentUser.SrUserId && v.OnBehalfOfUserId == _currentUser.SrUserId)))
                                  on questions.Id equals userVote.VotingQuestionId into userVotes
                              from userVote in userVotes.DefaultIfEmpty()
                              group new { questions, userVote } by new
                              {
                                  voting.Id,
                                  voting.Name,
                                  voting.State,
                                  DomainId = domain.Id,
                                  DomainName = domain.Name,
                                  ParentDomainId = parentDomain != null ? parentDomain.Id : (Guid?)null,
                                  ParentDomainName = parentDomain != null ? parentDomain.Name : null
                              } into g
                              select new GetUserVotingResponse
                              {
                                  ContainerType = ContainerTypeEnum.VotingContainer,
                                  ContainerId = g.Key.Id,
                                  Title = g.Key.Name,
                                  TotalQuestions = g.Count(x => x.questions != null),
                                  TotalUserVotes = g.Count(x => x.userVote != null),
                                  State = g.Key.State,
                                  Domain = new GetDomainResponse
                                  {
                                      Id = g.Key.DomainId,
                                      Name = g.Key.DomainName,
                                      Parent = g.Key.ParentDomainId != null
                                          ? new GetDomainResponse
                                          {
                                              Id = g.Key.ParentDomainId.Value,
                                              Name = g.Key.ParentDomainName
                                          }
                                          : null
                                  }
                              };

            return await votingQuery.AsNoTracking().ToListAsync(cancellationToken);
        }
    }
}
