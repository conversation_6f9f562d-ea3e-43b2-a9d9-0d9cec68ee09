﻿namespace SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser
{
    /// <summary>
    /// Decorate a current user role to map it to a role claim value.
    /// </summary>
    [AttributeUsage(AttributeTargets.Field, Inherited = false, AllowMultiple = false)]
    public sealed class MapsToClaimAttribute(string roleClaimValue) : Attribute
    {
        /// <summary>
        /// The exact role claim value that belongs to the role that this attribute decorates.
        /// </summary>
        public string Claim { get; } = !string.IsNullOrWhiteSpace(roleClaimValue) ?
            roleClaimValue.Trim() :
            throw new ArgumentException("Cannot be empty.", nameof(roleClaimValue));
    }
}
