﻿namespace SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata
{
    /// <summary>
    /// Represents the read status of a user for a specific document or agenda item. Generic class not owned by specific aggregate
    /// </summary>
    public class ReadStatus
    {
        public string UserId { get; init; } = null!;
        public DateTime? FirstTimeOpenedAt { get; private set; }

        /// <summary>
        /// Read status is default to true. If user wants to mark file as unread, it should be removed from the List
        /// </summary>
        public bool IsRead { get; private set; } = true;

        public static ReadStatus Create(string userId)
        {
            return new ReadStatus
            {
                UserId = userId,
                FirstTimeOpenedAt = DateTime.UtcNow,
            };
        }

        private ReadStatus() { }
    }
}
