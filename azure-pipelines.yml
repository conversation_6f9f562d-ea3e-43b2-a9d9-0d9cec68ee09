variables:
- group: secrets
- name: configuration
  value: 'Release'
- name: sdk
  value: 'iphoneos'
- name: ProjectFolder
  value: '$(System.DefaultWorkingDirectory)/SwissRe.ADS.ESP/SwissRe.ADS.ESP.ReactNative'

trigger: none

pool:
  name: 'SR-Ubuntu-Default'
  vmImage: 'macOS-15'

steps:
- checkout: self
  fetchDepth: 1
  clean: true

- task: CmdLine@2
  displayName: 'Print xcode version'
  inputs:
   script: '/usr/bin/xcodebuild -version'

- task: CmdLine@2
  displayName: 'Print SDK version'
  inputs:
   script: 'xcodebuild -showsdks'

- task: InstallAppleCertificate@2
  inputs:
    certSecureFile: 'apple_distribution.p12'
    certPwd: '$(appleCertP12password)'
    keychain: 'temp'
    deleteCert: true

- task: InstallAppleProvisioningProfile@1
  inputs:
    provisioningProfileLocation: 'secureFiles'
    provProfileSecureFile: 'iESPPlusRN.mobileprovision'
    removeProfile: true

- task: CmdLine@2
  displayName: 'Prepare plist file'
  inputs:
    script: 'sed -i "" "s/APPLE_PROV_PROFILE_UUID/${APPLE_PROV_PROFILE_UUID}/g" "$(ProjectFolder)/ios/isp-rn-export-info.plist"'

- task: NodeTool@0
  displayName: 'Install Node.js'
  inputs:
    versionSpec: '18.x'

- script: |
    npm install -g expo-cli
    npm install
  displayName: 'Install Node.js Dependencies and Expo CLI'
  workingDirectory: '$(ProjectFolder)'

- script: |
    ls $(ProjectFolder)
  displayName: 'List Source Directory'

- task: CocoaPods@0
  displayName: 'Install CocoaPods'
  inputs:
    workingDirectory: '$(ProjectFolder)/ios'

- task: Xcode@5
  displayName: 'Archive Xcode project'
  inputs:
    actions: 'clean'
    sdk: 'iphoneos'
    packageApp: true
    xcWorkspacePath: '$(ProjectFolder)/ios/SwissReADSESPReactNative.xcworkspace'
    scheme: 'SwissReADSESPReactNative'
    archivePath: '$(Build.ArtifactStagingDirectory)/iESP_Plus_RN.xcarchive'
    exportPath: '$(Build.ArtifactStagingDirectory)/out'
    exportOptions: 'plist'
    exportOptionsPlist: '$(ProjectFolder)/ios/isp-rn-export-info.plist'
    signingOption: 'manual'
    signingIdentity: '$(APPLE_CERTIFICATE_SIGNING_IDENTITY)'
    provisioningProfileUuid: '$(APPLE_PROV_PROFILE_UUID)'

- script: |
    ls $(Build.ArtifactStagingDirectory)
  displayName: 'List Export Directory'

- task: AppStoreRelease@1
  inputs:
   appType: 'iOS'
   ipaPath: '$(Build.ArtifactStagingDirectory)/out/*.ipa'
   releaseTrack: 'TestFlight'
   authType: 'ApiKey'
   apiKeyId: '$(apiKeyId)'
   apiKeyIssuerId: '$(apiKeyIssuerId)'
   apitoken: '$(apitoken)'