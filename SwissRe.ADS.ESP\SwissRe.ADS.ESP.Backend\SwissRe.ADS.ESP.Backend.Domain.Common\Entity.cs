﻿using Ardalis.GuardClauses;
using SwissRe.ADS.Ddd.Events.Abstractions;

namespace SwissRe.ADS.ESP.Backend.Domain.Common
{
    /// <summary>
    /// Marker interface for entities. 
    /// Inherit from <see cref="Entity{TId}"/> to create your entities.
    /// </summary>
    public interface IEntity : IEventsContainer
    {
    }

    /// <summary>
    /// Base class for all entities.
    /// </summary>
    public abstract class Entity<TId> : IEntity
        where TId : notnull
    {
        public TId Id { get; protected init; } = default!;

        private readonly List<IEventBase> _events = [];
        public IReadOnlyCollection<IEventBase> Events => _events;

        public void ClearEvents() =>
            _events.Clear();

        protected void RaiseEvent(IEvent evt)
        {
            Guard.Against.Null<IEventBase>(evt);
            _events.Add(evt);
        }
    }
}
