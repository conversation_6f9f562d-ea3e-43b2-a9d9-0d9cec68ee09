﻿using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices
{
    public class AgendaAuthorizationService(
        UnitOfWork unitOfWork,
        IContainerAuthorizationService containerPermissionService,
        ICurrentUser currentUser) : IAgendaAuthorizationService
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IContainerAuthorizationService _containerPermissionService = containerPermissionService;
        private readonly ICurrentUser _currentUser = currentUser;

        private record ContainerWithPermissions(ContainerStateEnum ContainerState, IEnumerable<AgendaPermission> Permissions);

        public async Task<bool> CanUserAddNewAgendaAsync(Guid containerId, AgendaTypeEnum agendaType)
        {
            var containerDomainId = await GetContainerDomainId(containerId, agendaType);
            if (containerDomainId is null)
                return false;

            return await _containerPermissionService.CanUserCreateContainerAsync(containerDomainId.Value);
        }

        public async Task<bool> CanUserViewAgendaAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType) =>
            await HasPermissionAsync(containerId, agendaId, agendaType, AgendaPermissionEnum.Read);

        public async Task<bool> CanUserManageAgendaAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType) =>
            await HasPermissionAsync(containerId, agendaId, agendaType, AgendaPermissionEnum.Manage);

        public async Task<bool> CanUserSubmitVoteAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType) =>
            await HasPermissionAsync(containerId, agendaId, agendaType, AgendaPermissionEnum.Write);

        public async Task<bool> CanUserAddAnnotationAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType) =>
            await HasPermissionAsync(containerId, agendaId, agendaType, AgendaPermissionEnum.Write);

        private async Task<bool> HasPermissionAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType, AgendaPermissionEnum requiredRole)
        {
            var agenda = await GetAgendaPermissionsAsync(containerId, agendaId, agendaType);
            return agenda?.Permissions.Any(p =>
                p.UserId == _currentUser.SrUserId && (p.CurrentPermission & requiredRole) > 0) == true;
        }

        private async Task<Guid?> GetContainerDomainId(Guid containerId, AgendaTypeEnum agendaType)
        {
            return agendaType switch
            {
                AgendaTypeEnum.MeetingAgenda => await _unitOfWork.Repository<MeetingContainer>().GetFirstOrNullAsync(containers => containers.Where(o => o.Id == containerId).Select(o => o.DomainId)),
                AgendaTypeEnum.VotingAgenda => await _unitOfWork.Repository<VotingContainer>().GetFirstOrNullAsync(containers => containers.Where(o => o.Id == containerId).Select(o => o.DomainId)),
                AgendaTypeEnum.DocumentAgenda => await _unitOfWork.Repository<DocumentContainer>().GetFirstOrNullAsync(containers => containers.Where(o => o.Id == containerId).Select(o => o.DomainId)),
                _ => null
            };
        }


        private async Task<ContainerWithPermissions?> GetAgendaPermissionsAsync(Guid containerId, Guid agendaId, AgendaTypeEnum agendaType)
        {
            return agendaType switch
            {
                AgendaTypeEnum.MeetingAgenda =>
                    await _unitOfWork.Repository<MeetingContainer>()
                        .GetFirstOrNullAsync(containers =>
                            containers.Where(x => x.Id == containerId)
                                          .Select(x => new ContainerWithPermissions(x.State,
                                              x.Agendas.Where(a => a.Id == agendaId)
                                                .SelectMany(o => o.Permissions
                                                    .Where(p => p.UserId == _currentUser.SrUserId)
                                                    )
                                                )
                                              )
                                          ),

                AgendaTypeEnum.VotingAgenda =>
                    await _unitOfWork.Repository<VotingContainer>()
                        .GetFirstOrNullAsync(containers =>
                            containers.Where(x => x.Id == containerId)
                                        .Select(x => new ContainerWithPermissions(x.State,
                                            x.Agendas.Where(a => a.Id == agendaId)
                                                .SelectMany(o => o.Permissions
                                                    .Where(p => p.UserId == _currentUser.SrUserId)
                                                    )
                                                )
                                            )
                                        ),

                AgendaTypeEnum.DocumentAgenda =>
                    await _unitOfWork.Repository<DocumentContainer>()
                        .GetFirstOrNullAsync(containers =>
                            containers.Where(x => x.Id == containerId)
                                        .Select(x => new ContainerWithPermissions(x.State,
                                            x.Agendas.Where(a => a.Id == agendaId)
                                                .SelectMany(o => o.Permissions
                                                    .Where(p => p.UserId == _currentUser.SrUserId)
                                                    )
                                                )
                                            )
                                        ),

                _ => null
            };
        }
    }
}
