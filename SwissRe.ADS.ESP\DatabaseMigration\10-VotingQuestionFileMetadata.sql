-- VotingQuestionFileMetadata table
/*
select * from esp.t_mp_meeting where meeting_tid = 142346522
select * from esp.t_mp_agenda where meeting_tid = 142346522 -- agenda_tid = 142346523

select * from esp.t_cr_question where question_tid = 142346526
select * from esp.t_cr_mp_link where meeting_item_tid = 142346523 -- question_tid = 142346526
select * from esp.t_mp_file where meeting_item_tid = 142346523 -- agenda_tid = 142346523 file_tid = 142346642
select * from esp.t_file where file_tid = 142346642
*/

-- TODO: Delete only question files!!!!
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."FileContents") <> 0 THEN
        RAISE NOTICE 'Delete FROM espv2.FileContents';
        DELETE FROM espv2."FileContents";
    END IF;
END $$;

BEGIN; 

-- FileContents table - document part
INSERT INTO espv2."FileContents" (
    "Id"
    ,"ORIGINAL_DB_ID"
    ,"EncryptedContents"
    ,"ThumbnailImageContents"
)
SELECT
    gen_random_uuid() AS Id
    ,f.file_tid as ORIGINAL_DB_ID
    ,f.document as EncryptedContents
    ,''::bytea as "ThumbnailImageContents"
    --,a.agenda_tid
    --,q.question_tid
    --,*
FROM
    esp.t_cr_question q
    JOIN esp.t_cr_mp_link l ON l.question_tid = q.question_tid
    JOIN esp.t_mp_agenda a ON a.agenda_tid = l.meeting_item_tid
    JOIN esp.t_mp_file mf ON mf.meeting_item_tid = l.meeting_item_tid
    JOIN esp.t_file f ON f.file_tid = mf.file_tid
    JOIN esp.t_file_rel fr on fr.file_tid = f.file_tid AND fr.rel_tid = -100;

--select * from espv2."FileContents" limit 3
-- Update the thumbnail column

UPDATE espv2."FileContents" fc
SET "ThumbnailImageContents" = COALESCE((
    SELECT pkg_crypto.decrypt_blob(tf.document)
    FROM esp.t_file tf
    JOIN esp.t_file_rel fr ON fr.file_tid = tf.file_tid AND fr.rel_tid = -103
    WHERE fc."ORIGINAL_DB_ID" = fr.file_tid
    LIMIT 1
), ''::bytea)
WHERE EXISTS (
    SELECT 1
    FROM esp.t_file_rel fr
    WHERE fc."ORIGINAL_DB_ID" = fr.file_tid AND fr.rel_tid = -103
);

--select * from espv2."FileContents" limit 3

COMMIT;

DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."VotingFileMetadatas") <> 0 THEN
        RAISE NOTICE 'Delete FROM espv2.VotingFileMetadatas';
        DELETE FROM espv2."VotingFileMetadatas";
    END IF;
END $$;


-- query itself
INSERT INTO espv2."VotingFileMetadatas" (
    "Id"
    ,"VotingAgendaId"
    ,"VotingAgendaId1" -- TODO: remove this column once fixed in model
    ,"ORIGINAL_DB_ID"
    ,"FileContentId"
    ,"OriginalFileName"
    ,"DisplayFileName"
    ,"FileExtension"
    ,"Supplementary"
    ,"FileGroupId"
    ,"VersionNumber"
    ,"IsLatest"
    ,"Order"
    ,"Size"
    ,"NumberOfPages"
    ,"CreatedOn"
    ,"CreatedByUserId"
    ,"LastModifiedOn"
    ,"LastModifiedByUserId"
    ,"ReadStatuses"
)
SELECT
    gen_random_uuid() AS Id
    ,COALESCE((select "Id" from espv2."VotingsAgenda" where "ORIGINAL_DB_ID" = a.agenda_tid), '00000000-0000-0000-0000-000000000000') AS VotingAgendaId
    ,COALESCE((select "Id" from espv2."VotingsAgenda" where "ORIGINAL_DB_ID" = a.agenda_tid), '00000000-0000-0000-0000-000000000000') AS VotingAgendaId1 -- TODO: remove this column once fixed in model
    ,f.file_tid as ORIGINAL_DB_ID
    ,(SELECT "Id" from espv2."FileContents" WHERE "ORIGINAL_DB_ID" = f.file_tid) AS FileContentId
    ,f.filename AS OriginalFileName 
    ,COALESCE((SELECT "label" from esp.t_mp_file_label fl where fl.meeting_item_tid=l.meeting_item_tid AND fl.file_name=f.filename), f.filename) AS DisplayFileName
    ,SPLIT_PART(f.filename, '.', array_length(string_to_array(f.filename, '.'), 1)) AS FileExtension
    ,(CASE WHEN (SELECT "suppl_flag" FROM esp.t_mp_file_suppl fs WHERE fs.meeting_item_tid = l.meeting_item_tid)='Y' THEN TRUE ELSE FALSE END) AS Supplementary -- TODO: Luke, are NULLs for suppl_flag column considered as N?
    ,gen_random_uuid() AS FileGroupId
    ,1 AS VersionNumber
    ,TRUE AS IsLatest
    ,(SELECT ord FROM esp."t_mp_file_order" fo WHERE fo.agenda_tid = a.agenda_tid AND fo.filename = f.filename) as "Order"
    ,COALESCE(f.real_length, (SELECT octet_length(pkg_crypto.decrypt_blob("EncryptedContents")) FROM espv2."FileContents" WHERE "ORIGINAL_DB_ID" = f.file_tid)) as "Size"
    ,fr.target_value::int AS NumberOfPages -- YES! target_value column contians number of pages of document!
    ,COALESCE(COALESCE(f.creation_timestamp, m.create_time), '1970-01-01 00:00:00') AS CreatedOn 
    ,COALESCE(m.creation_user_id, 'SYSTEM') AS CreatedByUserId
    ,COALESCE(COALESCE(mf.updated, m.update_time), '1970-01-01 00:00:00') AS LastModifiedOn
    ,COALESCE(m.update_user_id, 'SYSTEM') AS LastModifiedByUserId
    ,(SELECT json_agg(json_build_object(
            'UserId', user_id,
            'FirstTimeOpenedAt', FirstTimeOpenedAt
        )
    ) AS users_first_opened
    FROM (
        SELECT
            user_id,
            MIN(request_time) AS FirstTimeOpenedAt
        FROM esp.t_user_request
        WHERE request_id = CONCAT('FILE_TID:', f.file_tid)
        GROUP BY user_id
    ) AS user_requests) AS ReadStatuses
FROM
    esp.t_cr_question q
    JOIN esp.t_cr_mp_link l ON l.question_tid = q.question_tid
    JOIN esp.t_mp_agenda a ON a.agenda_tid = l.meeting_item_tid -- a.meeting_tid
    INNER JOIN esp.t_mp_meeting m ON a.meeting_tid = m.meeting_tid
    JOIN esp.t_mp_file mf ON mf.meeting_item_tid = l.meeting_item_tid
    JOIN esp.t_file f ON f.file_tid = mf.file_tid
    JOIN esp.t_file_rel fr on fr.file_tid = f.file_tid AND fr.rel_tid = -100
--WHERE q.question_tid = 102194059
--WHERE f.file_tid = 136136914

--select * from esp.t_user_request limit 100 -- where request_id like 'FILE_TID:136136914'

--select "Id" from espv2."VotingQuestions" where "ORIGINAL_DB_ID" = 102194059



--  fr.rel_tid: -103 previewIcon; -100 - fileContent
/*
select * from esp.t_file f where f.file_tid = 142346642
select * from esp.t_file_rel WHERE file_tid = 142346642
select * from esp.t_file_rel_code
select * from esp.t_file_rel WHERE file_tid = 142346642
select * from esp.t_file where file_tid = 136184519
*/
