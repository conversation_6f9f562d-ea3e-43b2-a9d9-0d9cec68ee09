import { Component } from '@angular/core';
import { MatChipsModule } from '@angular/material/chips';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-enterprise';
import { ContainerStateEnum, IGetContainerResponseBase } from '../../apiServices';

@Component({
  selector: 'app-status-cell-renderer',
  imports: [MatChipsModule],
  templateUrl: './status-cell-renderer.component.html',
  styleUrl: './status-cell-renderer.component.scss'
})
export class StatusCellRendererComponent implements ICellRendererAngularComp {
  status?: ContainerStateEnum | null;

  agInit(params: ICellRendererParams<IGetContainerResponseBase, ContainerStateEnum>): void {
    this.status = params.value;
  }
  refresh(params: ICellRendererParams<IGetContainerResponseBase, ContainerStateEnum>): boolean {
    this.status = params.value;
    return true;
  }
}
