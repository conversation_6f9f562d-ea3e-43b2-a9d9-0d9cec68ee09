parameters:
    - name: sdkVersion
      displayName: .Net Core SDK
      type: string
      default: 9.0.x
    - name: buildConfiguration
      displayName: .Net Core build configuration
      type: string
      default: Release
    - name: targetPlatform
      displayName: .Net Core target platform
      type: string
      default: linux-x64
    - name: srcFolder
      displayName: .Net Core project folder
      type: string
      default: '**/backend/**'
    - name: projectName
      displayName: Project Name to publish
      type: string
      default: "*"
    - name: artifactName
      displayName: Dotnet core artifact name
      type: string 
      default: dotnetcore
    - name: hasSql
      displayName: Project has SQL
      type: boolean 
      default: false
    - name: sqlScriptArtifactName
      displayName: Dotnet core artifact name
      type: string 
      default: sqlScript
    - name: sonarQubeEnabled
      displayName: Switch for SonarQube Analysis
      type: string
      default: false
    - name: sonarProjectKey
      displayName: QA application id
      type: string 
    - name: nexusProjectKey
      displayName: nexus application id
      type: string       
    - name: sonarProjectName
      displayName: QA name
      type: string 
    - name: nexusIqEnabled
      displayName: Switch for Nexus IQ analysis
      type: string
      default: false      
    - name: sonarServiceConnectionName
      displayName: SonarQube service connection name
      type: string
      default: SonarQube Enterprise
    - name: appVersion
      displayName: Dotnet app version
      type: string 
    - name: adsSharedNugetFeed
      displayName: ADS Shared Nuget Feed
      type: string
      default: d14d9b9f-7712-4bcf-8126-75eea19ac902
    - name: stage
      displayName: Nexus and Sonar Scan stage
      type: string 
      default: "Build"

steps:
- task: UseDotNet@2  
  displayName: "Use .NET Core ${{ parameters.sdkVersion }}"
  inputs:    
    version: ${{ parameters.sdkVersion }}    

- task: SonarQubePrepare@6
  condition: eq( ${{ parameters.sonarQubeEnabled }}, true)
  inputs:
    SonarQube: ${{ parameters.sonarServiceConnectionName }}
    scannerMode: 'MSBuild'
    projectKey: ${{ parameters.sonarProjectKey }}
    projectName: ${{ parameters.sonarProjectName }}
    extraProperties: |
     sonar.projectVersion=${{ parameters.appVersion }}
     sonar.projectName=${{ parameters.projectName }}
     sonar.qualitygate=${{ parameters.stage }}

- task: DotNetCoreCLI@2
  displayName: "Dotnet restore"
  inputs:
    command: 'restore'
    feedsToUse: 'select'
    vstsFeed: '${{ parameters.adsSharedNugetFeed }}'
    projects: '${{ parameters.srcFolder }}/*.csproj'

- pwsh: |
    ls
  displayName: List content of working directory
  workingDirectory: $(System.DefaultWorkingDirectory)  
  failOnStderr: true

- task: DotNetCoreCLI@2
  displayName: "Dotnet test"
  inputs:
    command: 'test'
    projects: '${{ parameters.srcFolder }}/SwissRe.ADS.ESP.Backend.Jobs.SyncAgendaPermissions.IntegrationTests/*.csproj'
    arguments: --configuration Release --logger trx
  env:
    TESTCONTAINERS_DISABLE_RESOURCE_REAPER: 'true'
    TESTCONTAINERS_RYUK_DISABLED: 'true'

- task: DotNetCoreCLI@2
  displayName: "Dotnet build"
  inputs:
    command: 'build'
    projects: '${{ parameters.srcFolder }}/*.csproj'
    arguments: --configuration ${{ parameters.buildConfiguration }} --runtime ${{ parameters.targetPlatform }} /p:PublishReadyToRun=true --no-self-contained

- task: SonarQubeAnalyze@6
  condition: eq( ${{ parameters.sonarQubeEnabled }}, true)
  displayName: 'Run Code Analysis'

- task: SonarQubePublish@6
  condition: eq( ${{ parameters.sonarQubeEnabled }}, true)
  displayName: 'Publish Quality Gate Result'

- task: DeleteFiles@1
  displayName: 'Delete build.artifactstagingdirectory'
  inputs:
    SourceFolder: '$(build.artifactstagingdirectory)'
    Contents: '**'

- task: DotNetCoreCLI@2
  displayName: 'Dotnet publish: ${{ parameters.projectName }}.csproj'
  condition: eq( ${{ parameters.nexusIqEnabled }}, false)
  inputs:
    command: 'publish'
    projects: '${{ parameters.srcFolder }}/${{ parameters.projectName }}.csproj'
    publishWebProjects: false
    arguments: '--configuration ${{ parameters.buildConfiguration }} --runtime ${{ parameters.targetPlatform }} /p:PublishReadyToRun=true --no-self-contained --output $(Build.ArtifactStagingDirectory)'
    zipAfterPublish: false
    modifyOutputPath: false # this attribute ensures that in artifact folder with project name does not get generated. default value is true.

- task: DotNetCoreCLI@2
  displayName: 'Dotnet publish: ${{ parameters.projectName }}.csproj'
  condition: eq( ${{ parameters.nexusIqEnabled }}, true)
  inputs:
    command: 'publish'
    projects: '${{ parameters.srcFolder }}/${{ parameters.projectName }}.csproj'
    publishWebProjects: false
    arguments: '--configuration ${{ parameters.buildConfiguration }} --runtime ${{ parameters.targetPlatform }} /p:PublishReadyToRun=true --no-self-contained --output $(Build.ArtifactStagingDirectory)'
    zipAfterPublish: True

- task: NexusIqPipelineTask@1
  condition: eq( ${{ parameters.nexusIqEnabled }}, true)
  inputs:
    nexusIqService: 'Nexus Iq'
    applicationId: 'Moped_Backend_Stratum'
    stage: ${{ parameters.stage }}

- task: PublishPipelineArtifact@1
  displayName: 'Publish Artifact: ${{ parameters.artifactName }}'
  inputs:
    targetPath: $(Build.ArtifactStagingDirectory)
    artifact:  ${{ parameters.artifactName }}


- task: CopyFiles@2
  displayName: 'Copy SQL idempotent migration script'
  inputs:
    cleanTargetFolder: true 
    contents: '**/*.sql'
    flattenFolders: true 
    targetFolder: $(Build.ArtifactStagingDirectory)

- task: PublishPipelineArtifact@1
  displayName: 'Publish Artifact: SQL migration script'
  inputs:
    targetPath: $(Build.ArtifactStagingDirectory)
    artifact:  ${{ parameters.sqlScriptArtifactName }}