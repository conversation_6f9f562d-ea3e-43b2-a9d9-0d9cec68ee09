{"name": "swissre.ads.esp.reactnative", "main": "expo-router/entry", "version": "1.0.1", "type": "module", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo start --ios --localhost", "rebuild-ios": "expo run:ios", "rebuild-ios-device": "npx expo run:ios --device 'iPhone_F17DMAK5PLJQ'", "refresh-ios-pods": "cd ios && xcodebuild clean && pod install && cd ../", "web": "expo start --web", "test": "jest --watchAll", "lint": "eslint .", "postinstall": "xattr -w com.apple.xcode.CreatedByBuildSystem true ios/build || true && npm run refresh-ios-pods --foreground-scripts", "genApiServicesFromLocalhost": "nswag run"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@pdftron/react-native-pdf": "^3.0.4-16", "@react-native-menu/menu": "^1.2.4", "@react-native-segmented-control/segmented-control": "2.5.4", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.3.11", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.3.25", "@tanstack/react-query": "^5.84.2", "axios": "^1.11.0", "expo": "~52.0.42", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-dev-client": "^5.0.20", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-linking": "~7.0.5", "expo-network": "^7.1.5", "expo-router": "^4.0.20", "expo-splash-screen": "~0.29.22", "expo-sqlite": "~15.1.4", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "^0.76.9", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.20.2", "react-native-msal": "^4.0.4", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.12.1", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "zod": "^3.24.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.33.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-native": "^5.0.0", "globals": "^16.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "nswag": "^14.5.0", "prettier": "^3.6.2", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "18.3.1", "typescript": "^5.3.3", "typescript-eslint": "^8.39.1"}, "private": true}