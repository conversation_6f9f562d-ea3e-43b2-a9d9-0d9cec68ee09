﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate
{
    public class Annotation : GuidEntity, IAggregateRoot
    {
        public Guid FileMetadataId { get; private set; }
        public FileMetadataTypeEnum FileMetadataType { get; private set; }

        /// <summary>
        /// GUID generated by PDF Tron
        /// </summary>
        public Guid AnnotationUniqueId { get; private set; }
        public byte[] EncryptedAnnotationXML { get; private set; } = null!;
        public DateTime CreatedOn { get; private set; }

        public string CreatedByUserId { get; private set; } = null!;

        public DateTime LastModifiedOn { get; private set; }

        public static Annotation Create(Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType, Guid annotationUniqueId, string userId, byte[] annotationXML, string createdByUserId)
        {
            Guard.Against.NullOrEmpty(annotationXML, nameof(EncryptedAnnotationXML), "Annotation XML cannot be null or empty.");

            return new Annotation
            {
                FileMetadataId = fileMetadataId,
                FileMetadataType = fileMetadataType,
                AnnotationUniqueId = annotationUniqueId,
                EncryptedAnnotationXML = annotationXML,
                CreatedOn = DateTime.UtcNow,
                CreatedByUserId = createdByUserId,
                LastModifiedOn = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Annotations created from iPad have to generate their own unique database Id's
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="fileMetadataId"></param>
        /// <param name="annotationUniqueId"></param>
        /// <param name="encryptedAnnotationXML"></param>
        /// <param name="userId"></param>
        /// <param name="createdByUserId"></param>
        /// <returns></returns>
        public static Annotation CreateFromiPad(Guid Id, Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType, Guid annotationUniqueId, byte[] encryptedAnnotationXML, string userId, string createdByUserId)
        {
            Guard.Against.NullOrEmpty(encryptedAnnotationXML, nameof(EncryptedAnnotationXML), "Annotation XML cannot be null or empty.");
            return new Annotation
            {
                Id = Id,
                FileMetadataId = fileMetadataId,
                FileMetadataType = fileMetadataType,
                AnnotationUniqueId = annotationUniqueId,
                EncryptedAnnotationXML = encryptedAnnotationXML,
                CreatedOn = DateTime.UtcNow,
                CreatedByUserId = createdByUserId,
                LastModifiedOn = DateTime.UtcNow,
            };
        }

        public void Modify(byte[] encryptedAnnotationXML)
        {
            Guard.Against.NullOrEmpty(encryptedAnnotationXML, nameof(EncryptedAnnotationXML), "Annotation XML cannot be null or empty.");
            EncryptedAnnotationXML = encryptedAnnotationXML;
        }
    }
}
