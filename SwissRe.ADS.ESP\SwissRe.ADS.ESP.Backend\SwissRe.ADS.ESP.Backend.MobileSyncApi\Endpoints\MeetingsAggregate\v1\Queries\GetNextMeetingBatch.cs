﻿using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.MeetingsAggregate.Helpers;
using System.Text.Json;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Models;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Common;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.MeetingsAggregate.Queries
{

    public class GetNextMeetingBatchEndpoint(UnitOfWork unitOfWork, ICurrentUser currentUser, IMeetingSyncDataService meetingSyncDataService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;
        private readonly IMeetingSyncDataService _meetingSyncDataService = meetingSyncDataService;

        public static void BuildRoute([EndpointRouteBuilder<MeetingV1RouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/users/me/getNextBatch",
                    async (GetNextMeetingBatchEndpoint endpoint,
                           string userDeviceId,
                           CancellationToken cancellationToken) =>
                        await endpoint.HandleAsync(userDeviceId, cancellationToken))
                .WithSummary("Retrieves the next batch of meeting changes for the current user and device.")
                .WithDescription("Returns a batch of meeting change entries that require synchronization for the authenticated user and specified device. If this is the first sync for the device, all meetings accessible to the user are included. Each change entry contains the meeting's ID, last updated timestamp, and serialized meeting details. The endpoint ensures that only new or updated meetings since the last sync are returned, supporting incremental synchronization between the backend and client devices.")
                .WithAngularName<MeetingV1RouteGroup>("GetNextMeetingBatch");
        }

        public async Task<List<EspChangeEntryResponse>> HandleAsync(string userDeviceId, CancellationToken cancellationToken)
        {
            var userSyncTracker = await _unitOfWork.Repository<DeviceSyncFeedTracker>()
                                                    .GetFirstOrNullAsync(x => x.UserId == _currentUser.SrUserId && x.DeviceId == userDeviceId && x.EntityType == EspSyncEntityType.Meetings, true);
            if (userSyncTracker is null)
            {
                var userMeetings = await _meetingSyncDataService.GetAllUserMeetings();
                userSyncTracker = DeviceSyncFeedTracker.Create(_currentUser.SrUserId, userDeviceId, EspSyncEntityType.Meetings, ConvertMeetingDataToChangeEntry(userMeetings));
                _unitOfWork.Repository<DeviceSyncFeedTracker>().Insert(userSyncTracker);
            }

            userSyncTracker.TryGenerateNewBatch();

            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            return userSyncTracker.BatchedItems.Select(x => new EspChangeEntryResponse() { EntityId = x.EntityId, UserLostAccess = x.UserLostAccess, ValueAsJson = x.ValueAsJson }).ToList();
        }

        public List<EspChangeEntry> ConvertMeetingDataToChangeEntry(List<GetMeetingDetailResponse> data)
        {
            if (data.Count == 0) return new();
            return data.Select(x => EspChangeEntry.CreateWithValue(x.Id, x.LastUpdatedOn, JsonSerializer.Serialize(x, SharedJsonSerializerSettings.Shared_Json_Serializer_Options))).ToList();
        }
    }
}
