import { DatabaseMigration } from '../types/DatabaseMigration';

export const initialMigration: DatabaseMigration = {
  migrationName: 'initialMigrations',
  sql: `
                -- Table for MeetingEntity
            CREATE TABLE IF NOT EXISTS MeetingEntities (
                id TEXT PRIMARY KEY,
                valueAsJson TEXT NOT NULL,

                Name TEXT NOT NULL,
                Description TEXT,
                State TEXT NOT NULL,
                LastUpdatedOn INTEGER NOT NULL,

                StartTime INTEGER, -- Unix timestamp (nullable)
                EndTime INTEGER,   -- Unix timestamp (nullable)

                Location TEXT,
                NumberOfUnreadDocuments INTEGER NOT NULL
            );

            -- Table for CurrentDeviceInfoEntity
            CREATE TABLE IF NOT EXISTS CurrentDeviceInfoEntity (
                deviceId TEXT PRIMARY KEY
            );

            -- Generates unique ID for the current device
            INSERT INTO CurrentDeviceInfoEntity (deviceId)
            VALUES (
                lower(
                    hex(randomblob(4)) || '-' ||
                    hex(randomblob(2)) || '-' ||
                    '4' || substr(hex(randomblob(2)),2) || '-' ||
                    substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(2)),2) || '-' ||
                    hex(randomblob(6))
                )
            );
    `,
};
