import axios from 'axios';
import { isSimulator, getAccessToken } from './authToken';
import { env } from '@/env';
import fakeGetMeetingBatchResponse from './_fakeResponses/fakeGetMeetingBatchResponse.json';

//base url is controlled by nswag generated classes
const api = axios.create({
  baseURL: env.EXPO_PUBLIC_ESP_API_URL,
  timeout: 50000,
});

api.interceptors.request.use(
  async (config) => {
    const simulator = await isSimulator();

    const token = simulator ? 'TestToken' : await getAccessToken();

    // Use AxiosHeaders set method
    config.headers?.set('Authorization', `Bearer ${token}`);

    return config;
  },
  (error) => Promise.reject(error),
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.isMock) return Promise.reject(error);

    console.error('API error:', error);
    return Promise.reject(error);
  },
);

//DEV mocks for the time being
const useMockDataForDevelopmentPurposes = true;

if (useMockDataForDevelopmentPurposes) {
  api.interceptors.request.use((config) => {
    if (config.url?.includes('/api/MeetingSync/v1/users/me/getNextBatch')) {
      // short-circuit the request
      return Promise.reject({
        config,
        isMock: true,
      });
    }
    return config;
  });

  api.interceptors.response.use(undefined, (error) => {
    if (error.isMock) {
      return {
        status: 200,
        data: fakeGetMeetingBatchResponse,
        config: error.config,
        headers: {},
      };
    }
    return Promise.reject(error);
  });
}

export default api;
