﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.MinimalApi.Endpoints;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Queries
{
    public record MeetingFileMetadataResponse(Guid Id,
        string FileName);

    public class GetAllMeetingFileMetadataVersions(UnitOfWork unitOfWork, IAgendaAuthorizationService agendaPermissionService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/containers/{containerId}/agendas/{agendaId}/files/{fileGroupId}/versions", async (GetAllMeetingFileMetadataVersions endpoint, Guid containerId, Guid agendaId, Guid fileGroupId) => await endpoint.HandleAsync(containerId, agendaId, fileGroupId))
                .WithSummary("Get all versions of a meeting file in an agenda.")
                .WithDescription("Returns metadata for all versions of a file (by file group) within a specific agenda in a meeting container. Requires view permission on the agenda.")
                .WithAngularName<MeetingContainerRouteGroup>("GetAllMeetingFileMetadataVersions");
        }

        public async Task<IEnumerable<MeetingFileMetadataResponse>?> HandleAsync(Guid containerId, Guid agendaId, Guid fileGroupId)
        {
            var canViewAgenda = await _agendaPermissionService.CanUserViewAgendaAsync(containerId, agendaId, AgendaTypeEnum.MeetingAgenda);
            if (!canViewAgenda)
                throw new UnauthorizedAccessException("Current user does not have permission to view agenda.");

            return await _unitOfWork.Repository<MeetingContainer>()
                        .GetFirstOrNullAsync(containers =>
                            containers.Where(container => container.Id == containerId)
                                      .Select(c => c.Agendas
                                                    .Where(a => a.Id == agendaId)
                                                    .SelectMany(o => o.Files)
                                                        .Where(file => file.FileGroupId == fileGroupId)
                                                            .Select(x => new MeetingFileMetadataResponse(x.Id, x.DisplayFileName))));
        }
    }
}
