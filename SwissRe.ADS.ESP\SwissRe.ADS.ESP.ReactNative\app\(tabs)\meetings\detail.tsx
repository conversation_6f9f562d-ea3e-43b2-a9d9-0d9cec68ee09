import { <PERSON>, BackHandler, Alert } from 'react-native';

import { Config, DocumentView, RNPdftron } from '@pdftron/react-native-pdf';
import { useEffect } from 'react';

export default function MeetingDetail() {
  useEffect(() => {
    // RNPdftron.initialize("Insert commercial license key here after purchase");
    RNPdftron.enableJavaScript(true);
    console.log('This is a test if this even works');
  }, []);

  const onLeadingNavButtonPressed = () => {
    console.log('leading nav button pressed');
    if (Platform.OS === 'ios') {
      Alert.alert('App', 'onLeadingNavButtonPressed', [{ text: 'OK', onPress: () => console.log('OK Pressed') }], { cancelable: true });
    } else {
      BackHandler.exitApp();
    }
  };

  const path = 'https://pdftron.s3.amazonaws.com/downloads/pl/PDFTRON_mobile_about.pdf';

  return (
    <DocumentView
      topAppNavBarRightBar={[Config.Buttons.thumbnailSlider, Config.Buttons.thumbnailsButton]}
      document={path}
      thumbnailViewEditingEnabled={true}
      showLeadingNavButton={true}
      userBookmarksListEditingEnabled={true}
      leadingNavButtonIcon={Platform.OS === 'ios' ? 'ic_close_black_24px.png' : 'ic_arrow_back_white_24dp'}
      onLeadingNavButtonPressed={onLeadingNavButtonPressed}
    />
  );
}
