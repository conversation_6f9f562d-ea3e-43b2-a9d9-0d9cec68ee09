﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <!--<InvariantGlobalization>true</InvariantGlobalization> Removed because SqlConnection does not support it. Was throwing by 'dotnet ef database update' -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.2" />
    <PackageReference Include="Microsoft.VisualStudio.SlowCheetah" Version="4.0.50">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
	<PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
	<PackageReference Include="SwissRe.ADS.Ddd.Events.Dispatching" Version="2.1.1" />
	<PackageReference Include="SwissRe.ADS.ServiceBus" Version="11.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Application\SwissRe.ADS.ESP.Backend.Application.csproj" />
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Persistence\SwissRe.ADS.ESP.Backend.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.development.json">
      <TransformOnBuild>true</TransformOnBuild>
    </Content>
    <Content Update="appsettings.Development.json">
      <TransformOnBuild>true</TransformOnBuild>
    </Content>
    <Content Update="appsettings.json">
      <TransformOnBuild>true</TransformOnBuild>
    </Content>
  </ItemGroup>

</Project>
