-- VotingQuestions table
/*
to decrypt column content use pkg_crypto.decrypt_varchar(column)

select
    a.agenda_tid as ORIGINAL_DB_ID,
    a.name as Name
from 
    esp.t_mp_agenda a 
where 
    a.agenda_tid in (select meeting_item_tid from esp.t_cr_mp_link) and a.item_type = 'CR'


select * from esp.t_cr_question
select * from esp.t_cr_mp_link -- meeting_item_tid is agenda_tid in esp.t_mp_agenda

select * from esp.t_cr_mp_link where question_tid = 142342236

select * from esp.t_mp_agenda where agenda_tid = 142342233
*/ 
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."VotingQuestions") <> 0 THEN
        RAISE NOTICE 'Delete FROM espv2.VotingQuestions';
        DELETE FROM espv2."VotingQuestions";
    END IF;
END $$;

INSERT INTO espv2."VotingQuestions" (
    "Id"
    ,"OR<PERSON>INAL_DB_ID"
    ,"AgendaId"
    ,"Question"
    ,"DescriptionEncrypted"
    ,"AvailableOptions"
    ,"CreatedOn"
    ,"CreatedByUserId"
    ,"LastModifiedOn"
    ,"LastModifiedByUserId"
    ,"LockedForEditing"
    ,"DueDate"
    ,"VotesVisibleForPublic"
)
SELECT
    gen_random_uuid() AS Id
    ,q.question_tid AS ORIGINAL_DB_ID
    ,COALESCE((SELECT "Id" FROM espv2."VotingsAgenda" va WHERE va."ORIGINAL_DB_ID" = ma.agenda_tid), '00000000-0000-0000-0000-000000000000') AS AgendaId
    ,q.question AS Question
--    pkg_crypto.decrypt_varchar(q.nme_enc) as NameEncrypted, -- this is the same as VotingAgenda name given from t_mp_agenda (linked via t_cr_mp_link) and it's unencrypted there
    ,q.details_enc AS DescriptionEncrypted
    ,to_json(string_to_array(q.options , ',')) AS AvailableOptions
--    ,q.option_type TODO: Ask about this!
    ,m.create_time AS CreatedOn
    ,COALESCE(m.creation_user_id, 'SYSTEM') AS CreatedByUserId
    ,m.update_time AS LastModifiedOn
    ,COALESCE(m.update_user_id, 'SYSTEM') AS LastModifiedByUserId
    ,FALSE AS LockedForEditing
--    ,(SELECT count(*) FROM esp.t_cr_answer a WHERE a.question_tid = q.question_tid) AS NumberOfAnswers
    ,COALESCE(q.due, '1970-01-01 00:00:00') AS DueDate
    ,(CASE WHEN q.votes_visible IS NULL OR q.votes_visible = 'N' THEN FALSE ELSE TRUE END) AS VotesVisibleForPublic -- TODO: Up to discussion with Luke if NULLs are considered as Y, or N
--    ,q.votes_visible AS VotesVisibleForPublicStr -- TODO remove this one after NULLs above are resolved
FROM
    esp.t_cr_question q
INNER JOIN 
    esp.t_cr_mp_link ml ON ml.question_tid = q.question_tid
INNER JOIN 
    esp.t_mp_agenda ma ON ml.meeting_item_tid = ma.agenda_tid
INNER JOIN
        esp.t_mp_meeting m ON ma.meeting_tid = m.meeting_tid
