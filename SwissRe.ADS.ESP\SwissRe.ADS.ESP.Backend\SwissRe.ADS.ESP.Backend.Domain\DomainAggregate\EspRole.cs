﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DomainAggregate
{
    public class EspRole : GuidEntity, IAggregateRoot
    {
        public string Name { get; private set; } = null!;
        public string DisplayName { get; private set; } = null!;
        public string? Description { get; set; }

        public static EspRole Create(Guid id, string name, string displayName, string? description = null)
        {
            Guard.Against.NullOrWhiteSpace(name, nameof(Name), "Role name cannot be blank");
            Guard.Against.NullOrWhiteSpace(displayName, nameof(DisplayName), "Role display name cannot be blank");

            return new EspRole
            {
                Id = id,
                Name = name,
                DisplayName = displayName,
                Description = description
            };
        }

        private EspRole() { }
    }
}
