using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using Microsoft.AspNetCore.Http;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAggregate.Commands
{
    public record CreateVotingQuestionCommand(string question, VotingOptions votingOptions, byte[]? description = null);

    public class CreateVotingQuestionEndpoint(UnitOfWork unitOfWork,
        IAgendaAuthorizationService agendaPermissionService,
        ICurrentUser currentUser,
        IEspEncryptionService encryptionService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly ICurrentUser _currentUser = currentUser;
        private readonly IEspEncryptionService _encryptionService = encryptionService;

        public static void BuildRoute([EndpointRouteBuilder<VotingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapPost("/containers/{containerId}/agendas/{agendaId}/questions", (CreateVotingQuestionCommand command, Guid containerId, Guid agendaId, CreateVotingQuestionEndpoint endpoint) => endpoint.HandleAsync(command, containerId, agendaId))
                .WithSummary("Create a new voting question in a voting agenda.")
                .WithDescription("Creates a new voting question within the specified voting agenda in a voting container. Requires manage permission on the agenda.")
                .WithAngularName<VotingContainerRouteGroup>("CreateVotingQuestion");

        public async Task HandleAsync(CreateVotingQuestionCommand command, Guid containerId, Guid agendaId)
        {
            var canCreateQuestion = await _agendaPermissionService.CanUserManageAgendaAsync(containerId, agendaId, AgendaTypeEnum.VotingAgenda);
            if (canCreateQuestion == false)
                throw new UnauthorizedAccessException("Current user does not have permission to create a new voting question in this container.");

            //TODO: Encrypt question
            var container = await _unitOfWork.Repository<VotingContainer>().GetAsync(containerId, true);
            container.AddVotingQuestion(agendaId, command.question, command.description, command.votingOptions, _currentUser.SrUserId);
            
            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
