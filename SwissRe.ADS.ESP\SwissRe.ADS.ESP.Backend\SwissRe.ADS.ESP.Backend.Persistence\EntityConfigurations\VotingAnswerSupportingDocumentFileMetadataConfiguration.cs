﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate;
using SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class VotingAnswerSupportingDocumentFileMetadataConfiguration : FileMetadataBaseConfiguration<VotingAnswerSupportingDocumentFileMetadata>
    {
        public override void Configure(EntityTypeBuilder<VotingAnswerSupportingDocumentFileMetadata> builder)
        {
            builder.ToTable("VotingAnswerSupportingDocumentFileMetadatas");
            base.Configure(builder);
        }
    }
}
