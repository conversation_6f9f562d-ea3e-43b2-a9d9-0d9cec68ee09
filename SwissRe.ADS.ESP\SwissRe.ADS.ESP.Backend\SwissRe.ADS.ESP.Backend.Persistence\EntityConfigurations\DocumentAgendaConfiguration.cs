﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    internal class DocumentAgendaConfiguration : IEntityTypeConfiguration<DocumentAgenda>
    {
        public void Configure(EntityTypeBuilder<DocumentAgenda> builder)
        {
            builder.ToTable("DocumentsAgenda");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.Property(x => x.Name).IsRequired().HasMaxLength(512);
            builder.Property(x => x.Description).HasMaxLength(5092);

            builder.OwnsMany(a => a.Permissions,
                ownedNav =>
                {
                    ownedNav.ToJson("Permissions");
                    ownedNav.Property(p => p.CurrentPermission).HasConversion<int>();
                    ownedNav.Property(p => p.InheritedPermission).HasConversion<int>();
                });

            //builder.HasIndex(x => x.DocumentPermissions).HasMethod("gin");

            builder.HasMany(x => x.Files)
                .WithOne()
                .HasForeignKey(x => x.DocumentAgendaId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Navigation(x => x.Files).AutoInclude();
        }
    }
}
