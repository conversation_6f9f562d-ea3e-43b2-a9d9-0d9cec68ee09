using Microsoft.Extensions.Caching.Memory;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.DomainServices
{
    public record AgendaInfo(Guid AgendaId, Guid ParentContainerId);

    public interface IAgendaInfoProviderService
    {
        Task<AgendaInfo> GetAgendaInfoAsync(Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType);
    }

    public class AgendaInfoProviderService : IAgendaInfoProviderService
    {
        private readonly ISqlQueryService _sqlQueryService;
        private readonly IMemoryCache _memoryCache;

        public AgendaInfoProviderService(ISqlQueryService sqlQueryService, IMemoryCache memoryCache)
        {
            _sqlQueryService = sqlQueryService;
            _memoryCache = memoryCache;
        }

        public async Task<AgendaInfo> GetAgendaInfoAsync(Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType)
        {
            var agendaInfoKey = $"AgendaInfo_{fileMetadataType}_{fileMetadataId}";
            if (_memoryCache.TryGetValue(agendaInfoKey, out AgendaInfo? agendaInfo))
            {
                if (agendaInfo is not null)
                    return agendaInfo;
            }

            var targetTable = fileMetadataType switch
            {
                FileMetadataTypeEnum.Documents => "DocumentsAgenda",
                FileMetadataTypeEnum.Meetings => "DocumentFileMetadatas",
                _ => throw new ArgumentException("Invalid file metadata type.", nameof(fileMetadataType))
            };

            var sql = fileMetadataType switch
            {
                FileMetadataTypeEnum.Meetings => @"SELECT ma.""Id"" as ""AgendaId"", ma.""ParentContainerId"" FROM espv2.""MeetingFileMetadatas"" dFile left join espv2.""MeetingsAgenda"" ma on ma.""Id"" = dFile.""MeetingAgendaId"" WHERE dFile.""Id""= @fileMetadataId",
                FileMetadataTypeEnum.Documents => @"SELECT ma.""Id"" as ""AgendaId"", ma.""ParentContainerId"" FROM espv2.""DocumentFileMetadatas"" dFile left join espv2.""DocumentsAgenda"" ma on ma.""Id"" = dFile.""DocumentAgendaId"" WHERE dFile.""Id""= @fileMetadataId",
                _ => throw new ArgumentException("Invalid file metadata type.", nameof(fileMetadataType))
            };

            var retrievedAgenda = await _sqlQueryService.QueryFirstOrDefaultAsync<AgendaInfo>(sql, new { fileMetadataId });

            if (retrievedAgenda is null)
                throw new ArgumentException("Requested file does not exist under the target Agenda.", nameof(fileMetadataId));

            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(10)
            };

            _memoryCache.Set(agendaInfoKey, retrievedAgenda, cacheEntryOptions);

            return retrievedAgenda;
        }
    }
}
