-- DocumentsAgenda Table
/*
select type_tid, * FROM esp.t_mp_type WHERE type_tid in  (select type_tid FROM esp.t_mp_meeting WHERE item_type = 'GS');
select * FROM esp.t_mp_type WHERE item_type = 'GD' AND type_tid not in (select type_tid FROM esp.t_mp_type WHERE type_tid in  (select type_tid FROM esp.t_mp_meeting WHERE item_type = 'GS'))

select 
    *
FROM 
    esp.t_mp_meeting 
WHERE 
    type_tid in (select type_tid FROM esp.t_mp_type WHERE item_type = 'GD' AND type_tid in (select type_tid FROM esp.t_mp_type WHERE type_tid in  (select type_tid FROM esp.t_mp_meeting WHERE item_type = 'GS')))
;
--- query to compare document agenda counts
select 
    COUNT(*) 
FROM 
    esp.t_mp_meeting 
WHERE 
    type_tid in (select type_tid FROM esp.t_mp_type WHERE item_type = 'GD' AND type_tid in (select type_tid FROM esp.t_mp_type WHERE type_tid in (select type_tid FROM esp.t_mp_meeting WHERE item_type = 'GS')))
;
select 
    COUNT(*) 
FROM 
    esp.t_mp_meeting 
WHERE 
    type_tid in (select type_tid FROM esp.t_mp_type WHERE item_type = 'GD')
;
*/
-- query itself
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."DocumentsAgenda") <> 0 THEN
        RAISE NOTICE 'Delete FROM espv2.DocumentsAgenda';
        DELETE FROM espv2."DocumentsAgenda";
    END IF;
END $$;

INSERT INTO espv2."DocumentsAgenda" (
    "Id"
    ,"ParentContainerId"
    ,"ORIGINAL_DB_ID"
    ,"Name"
    ,"Description"
    ,"Order"
)
SELECT
    gen_random_uuid() AS Id
    ,COALESCE(
        (SELECT dc."Id" FROM espv2."DocumentsContainer" dc WHERE m.meeting_tid = dc."ORIGINAL_DB_ID"), 
        '00000000-0000-0000-0000-000000000000') 
        AS ParentContainerId
    ,m.meeting_tid as ORIGINAL_DB_ID
    ,m.Name as "Name"
    ,m.Description as "Description"
    ,m.ord as "Order"
FROM 
    esp.t_mp_meeting m
WHERE 
    m.type_tid in (SELECT t.type_tid FROM esp.t_mp_type t WHERE t.item_type = 'GD') and m.ord = 1
