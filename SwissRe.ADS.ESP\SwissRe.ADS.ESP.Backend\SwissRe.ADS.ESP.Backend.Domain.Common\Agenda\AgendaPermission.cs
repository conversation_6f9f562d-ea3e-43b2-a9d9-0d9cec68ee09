﻿namespace SwissRe.ADS.ESP.Backend.Domain.Common.Agenda
{
    /// <summary>
    /// Default agenda permission is automatically managed by sync job and is derived from Containers Domain relation (DomainRoles). This does not include any manual overrides by the end user.
    /// Manual overrides are managed in a separate entity ManualAgendaPermissionOverride.
    /// </summary>
    public class AgendaPermission
    {
        public string UserId { get; private set; } = null!;
        public AgendaPermissionEnum CurrentPermission { get; private set; }
        public AgendaPermissionEnum InheritedPermission { get; private set; }

        private AgendaPermission() { }

        /// <summary>
        /// Factory method to create a new AgendaPermission instance.
        /// </summary>
        public static AgendaPermission Create(string userId, AgendaPermissionEnum agendaPermission)
            => new AgendaPermission() { UserId = userId, CurrentPermission = agendaPermission, InheritedPermission = agendaPermission };

        /// <summary>
        /// Method used by front-end to manually tamper with current user permissions.
        /// </summary>
        /// <param name="permission">The permission to overwrite with.</param>
        internal void ManuallyOverwritePermission(AgendaPermissionEnum permission)
        {
            if (permission == AgendaPermissionEnum.Deny)
            {
                CurrentPermission = AgendaPermissionEnum.Deny;
                return;
            }

            // Always preserve Manage if inherited, otherwise just set the permission
            CurrentPermission = (InheritedPermission.HasFlag(AgendaPermissionEnum.Manage))
                ? (permission | AgendaPermissionEnum.Manage)
                : permission;
        }

        /// <summary>
        /// Updates the inherited agenda permission for this user if it differs from the current value.
        /// If the inherited permission is changed, the current permission is also updated unless it was manually overridden.
        /// Returns <c>true</c> if the permission was changed; otherwise, <c>false</c>.
        /// </summary>
        /// <param name="permission">The new inherited permission to apply.</param>
        /// <returns>
        /// <c>true</c> if the inherited or current permission was updated; 
        /// <c>false</c> if the inherited permission was already set to the specified value and no change was made.
        /// </returns>
        internal bool UpdateInheritedPermissionIfChangedFromSync(AgendaPermissionEnum permission)
        {
            if (InheritedPermission == permission) return false;

            if (InheritedPermission == CurrentPermission)
            {
                CurrentPermission = permission;
            }
            else if (CurrentPermission.HasFlag(AgendaPermissionEnum.Manage) && !permission.HasFlag(AgendaPermissionEnum.Manage))
            {
                // Remove Manage permission if new inherited does not have it
                CurrentPermission &= ~AgendaPermissionEnum.Manage;
            }

            InheritedPermission = permission;
            return true;
        }
    }
}
