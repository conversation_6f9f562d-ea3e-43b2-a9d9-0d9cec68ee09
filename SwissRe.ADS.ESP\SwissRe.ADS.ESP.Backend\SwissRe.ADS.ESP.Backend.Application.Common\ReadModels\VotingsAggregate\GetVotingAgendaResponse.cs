﻿using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.VotingsAggregate
{
    public class GetVotingAgendaResponse
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public int Order { get; set; }
    }
}
