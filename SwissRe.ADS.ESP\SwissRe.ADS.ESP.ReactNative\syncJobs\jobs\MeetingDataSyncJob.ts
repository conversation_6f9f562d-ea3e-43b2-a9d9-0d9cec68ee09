import { TimeUtils } from '@/utils/TimeUtils';
import { ISyncJob } from '../types/ISyncJob';
import { MeetingV1Client } from '@/api/apiServices';
import { MeetingRepository } from '@/database/repositories/meetingRepository';

export class MeetingDataSyncJob implements ISyncJob {
  id = 'meetingDataSyncJob';
  intervalInMs = TimeUtils.secondsToMs(10);
  _uniqueDeviceId: string;

  private readonly _meetingRepository = new MeetingRepository();

  constructor(uniqueDeviceId: string) {
    this._uniqueDeviceId = uniqueDeviceId;
  }

  async run(): Promise<boolean> {
    const client = new MeetingV1Client();
    const result = await client.getNextMeetingBatch(this._uniqueDeviceId);
    console.log(`[MeetingDataSyncJob] Retrieved ${result.length} meeting changes for device ${this._uniqueDeviceId}`);
    if (result.length === 0) return false;

    await this._meetingRepository.syncNewMeetingChanges(result);

    return true;
  }
}
