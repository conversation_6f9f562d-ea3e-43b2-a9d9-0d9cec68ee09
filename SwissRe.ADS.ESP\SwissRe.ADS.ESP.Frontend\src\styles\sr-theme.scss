@use '@angular/material' as mat;

$mat-srprimary: (
  50: #e4f4ff,
  100: #bee3ff,
  200: #95d3ff,
  300: #69c1ff,
  400: #47b3ff,
  500: #27a6ff,
  600: #2497f8,
  700: #1f84e5,
  800: #1b73d3,
  900: #1454b4,
  A100: #8c9eff,
  A200: #536dfe,
  A400: #3d5afe,
  A700: #304ffe,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: #000000,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #000000,
    A200: #000000,
    A400: #ffffff,
    A700: #ffffff
  )
);
$mat-sraccent: (
  50: #f3e4f5,
  100: #e1bce8,
  200: #ce8fda,
  300: #ba62cb,
  400: #ab3ebf,
  500: #9c14b4,
  600: #8e12ae,
  700: #7a0ea7,
  800: #680b9f,
  900: #450593,
  A100: #ea80fc,
  A200: #e040fb,
  A400: #d500f9,
  A700: #aa00ff,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #000000,
    400: #ffffff,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #000000,
    A200: #000000,
    A400: #ffffff,
    A700: #ffffff
  )
);
$mat-srwarn: (
  50: #fce9ec,
  100: #f8c9ce,
  200: #e39495,
  300: #d56b6d,
  400: #dd4b4a,
  500: #df3931,
  600: #d12f30,
  700: #bf262a,
  800: #b21f24,
  900: #a31419,
  A100: #ff8a80,
  A200: #ff5252,
  A400: #ff1744,
  A700: #d50000,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #000000,
    A200: #ffffff,
    A400: #ffffff,
    A700: #ffffff
  )
);

:root {
  --primary-color: mat.m2-get-color-from-palette($mat-srprimary, 900);
  --primary-color-dark: mat.m2-get-color-from-palette($mat-srprimary, 800);
  --accent-color: mat.m2-get-color-from-palette($mat-sraccent, 500);
  --warn-color: mat.m2-get-color-from-palette($mat-srwarn, 900);
}

// Create typography based on your application needs
// Use this guide for reference https://material.angular.io/guide/typography
// Do NOT change the font-family as we should use SwissRe fonts as standard
$general-typography: mat.m2-define-typography-config(
  $font-family: 'SwissReSans, Arial, sans-serif',
  $body-2: mat.m2-define-typography-level(16px, 20px, 400),
  $body-1: mat.m2-define-typography-level(16px, 18px, 100),
  $caption: mat.m2-define-typography-level(11px, 20px, 400),
  $button: mat.m2-define-typography-level(16px, 16px, 500),
  $overline: mat.m2-define-typography-level(16px, 1.125, 400)
);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$app-primary: mat.m2-define-palette($mat-srprimary, 900, A100, 700);
$app-accent: mat.m2-define-palette($mat-sraccent, 500, 300, 700);
$app-warn: mat.m2-define-palette($mat-srwarn, 900);
$sr-app-theme: mat.m2-define-light-theme(
  (
    color: (
      primary: $app-primary,
      accent: $app-accent,
      warn: $app-warn
    ),
    typography: $general-typography,
    density: 0
  )
);

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
//@include angular-material-theme($sr-app-theme);
@include mat.all-component-themes($sr-app-theme);

@include mat.button-overrides(
  (
    filled-container-shape: 100px,
    outlined-container-shape: 100px
  )
);
