﻿using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web;
using System.Net;

namespace SwissRe.ADS.ESP.Backend.Api
{
    public static class Auth
    {
        public static void AddAuth(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                AddDevAuthentication(services, configuration);
            }
            else
            {
                services.AddMicrosoftIdentityWebApiAuthentication(configuration, "AzureAd");
            }

            services.AddAuthorizationBuilder()
                .SetFallbackPolicy(new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build());
        }

        private static void AddDevAuthentication(IServiceCollection services, IConfiguration configuration)
        {
            services.AddMicrosoftIdentityWebAppAuthentication(configuration, "AzureAd");

            services.Configure<OpenIdConnectOptions>(OpenIdConnectDefaults.AuthenticationScheme, options =>
            {
                options.TokenValidationParameters.NameClaimType = CurrentUser.AzureUserIdClaimType;

                options.Events = new OpenIdConnectEvents
                {
                    OnRedirectToIdentityProvider = ctx =>
                    {
                        if (ctx.Request.Path.StartsWithSegments("/api") && ctx.Response.StatusCode == 200)
                        {
                            ctx.Response.StatusCode = 401;
                            ctx.HandleResponse();
                        }
                        return Task.CompletedTask;
                    }
                };

                var shouldUseProxyForLocalDev = configuration.GetValue<bool>("Authentication:ShouldUseProxyForLocalDevelopment");

                if (shouldUseProxyForLocalDev)
                {
                    options.BackchannelHttpHandler = new HttpClientHandler()
                    {
                        UseProxy = true,
                        UseDefaultCredentials = true,
                        Proxy = new WebProxy
                        {
                            Credentials = CredentialCache.DefaultNetworkCredentials,
                            Address = new Uri(configuration.GetValue<string>("Authentication:ProxyUrl")!)
                        }
                    };
                }
            });
        }
    }
}
