﻿namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate
{
    /// <summary>
    /// Response DTO representing meeting-specific metadata for a user's meeting container.
    /// Used to display summary information about a meeting on the home dashboard.
    /// </summary>
    public class GetUserMeetingResponse : GetContainerResponseBase
    {
        /// <summary>
        /// Total number of documents associated with the meeting.
        /// </summary>
        public int TotalDocuments { get; set; }

        /// <summary>
        /// Number of documents the user has marked as read.
        /// </summary>
        public int ReadDocuments { get; set; }

        /// <summary>
        /// Number of documents the user has not yet read.
        /// Calculated as the difference between <see cref="TotalDocuments"/> and <see cref="ReadDocuments"/>.
        /// </summary>
        public int UnreadDocuments => TotalDocuments - ReadDocuments;

        /// <summary>
        /// Location of the meeting.
        /// </summary>
        public string? Location { get; set; }
    }
}
