﻿using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.DomainAggregate
{
    public interface IDomainReadModelRepository: IReadModelRepository
    {
        Task<List<(AgendaPermissionEnum accessType, string userId)>> GetDomainAgendaPermissions(Guid domainId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Gets combined flags of AgendaPermissionEnum for specific user based on DomainId
        /// </summary>
        /// <param name="domainId"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken"></param>
        /// <returns>Combined flags of all user roles or NULL in case user is not associated with this role</returns>
        Task<AgendaPermissionEnum?> GetDomainAgendaPermissionForUser(Guid domainId, string userId, CancellationToken cancellationToken = default);

        Task<List<(AgendaPermissionEnum accessType, Guid domainId)>> GetAgendaPermissionsForAllDomainsForUser(string userId, CancellationToken cancellationToken = default);
    }
}
