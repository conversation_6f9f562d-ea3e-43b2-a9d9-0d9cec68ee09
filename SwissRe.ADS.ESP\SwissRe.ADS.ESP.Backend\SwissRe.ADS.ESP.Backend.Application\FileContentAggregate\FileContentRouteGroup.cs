﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;

namespace SwissRe.ADS.ESP.Backend.Application.FileContentAggregate
{
    public class FileContentRouteGroup: IRouteGroup
    {
        public static IEndpointRouteBuilder? BuildRoute([EndpointRouteBuilder<ApiRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
               builder.MapGroup("FileContent").WithTags("FileContent");
    }
}
