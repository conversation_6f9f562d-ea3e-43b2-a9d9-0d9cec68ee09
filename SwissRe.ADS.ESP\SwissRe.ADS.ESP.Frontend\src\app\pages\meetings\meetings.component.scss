.row {
  position: relative;
  min-width: 890px;
  display: flex;
  height: 100%;

  .meetings-tab {
    flex: 1;

    ::ng-deep .mat-mdc-tab-body-wrapper {
      flex: 1;

      .mat-mdc-tab-body-content {
        $padding: 10px;
        padding-top: $padding;
        height: calc(100% - $padding);
      }
    }
  }

  .action-buttons {
    position: absolute;
    top: 4px;
    right: 0;
    display: flex;
    flex-direction: row;
    column-gap: 16px;
  }
}
