import { useMemo, useState } from 'react';
import { ActivityIndicator, SafeAreaView, ScrollView, View, StyleSheet } from 'react-native';
import MeetingsFilter, { MeetingFilters, MeetingFilterGroupByEnum, MeetingSelectedTimeFrame } from './components/MeetingsFilter';
import { useEspAppTheme } from '@/hooks/useEspTheme';
import MeetingsList from './components/MeetingsList';
import { useQuery } from '@tanstack/react-query';
import { MeetingRepository } from '@/database/repositories/meetingRepository';

export default function AllMeetings() {
  const theme = useEspAppTheme();
  const meetingRepository = useMemo(() => new MeetingRepository(), []);

  const [filters, setFilters] = useState<MeetingFilters>({
    selectedTimeframe: MeetingSelectedTimeFrame.Current,
    groupBy: MeetingFilterGroupByEnum.ByDate,
  });

  const { data, isLoading, error } = useQuery({
    queryKey: ['allMeetings', filters.selectedTimeframe],
    queryFn: () => (filters.selectedTimeframe === MeetingSelectedTimeFrame.Current ? meetingRepository.getAllCurrentMeetings() : meetingRepository.getAllPastMeetings()),
  });

  const [isScrolled, setIsScrolled] = useState(false);

  return (
    <SafeAreaView>
      <ScrollView
        contentInsetAdjustmentBehavior="never"
        stickyHeaderIndices={[0]}
        style={styles.scrollViewContainer}
        onScroll={(e) => {
          const offsetY = e.nativeEvent.contentOffset.y;
          setIsScrolled(offsetY > 0); // mark as scrolled when > 0
        }}
      >
        <View style={[styles.headerWrapper, { borderBottomColor: isScrolled ? theme.colors.border : theme.colors.transparent }]}>
          <MeetingsFilter onChange={setFilters} />
        </View>
        {isLoading && <ActivityIndicator size="large" />}
        {!isLoading && <MeetingsList groupBy={filters.groupBy} meetings={data ?? []} />}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerWrapper: {
    borderBottomWidth: 1,
    marginLeft: -16,
    marginRight: -16,
    paddingLeft: 16,
    paddingRight: 16,
  },
  scrollViewContainer: {
    padding: 16,
  },
});
