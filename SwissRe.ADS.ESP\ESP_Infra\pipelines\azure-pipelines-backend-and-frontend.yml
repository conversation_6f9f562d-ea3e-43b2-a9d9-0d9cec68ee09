parameters:
  - name: enableqascan
    displayName: Enable QA scan
    type: boolean
    default: false

trigger: none

pool:
  name: 'SR-Ubuntu-Default' 

variables:
  apmId: 'ESP'  #don`t know why lower doesn`t work
  versionMajorMinor: 1.0
  versionRevision: $[counter(variables['versionMajorMinor'], 0)]
  appVersion: $[format('{0}.{1}', variables.versionMajorMinor, variables.versionRevision)]
  dockerImageTag: $[format('v{0}', variables.appVersion)]

  dotnetBuildTemplate: 'app-templates/build-dotnet.template.yaml'
  angularBuildTemplate: 'app-templates/build-angular.template.yaml'  
  stratumBuildTemplate: 'stratum-templates/build-docker.stratum.template.yaml'
  
  stratumReleaseTemplate: 'stratum-templates/deploy-docker.stratum.template.yaml'
  sqlScriptReleaseTemplate: "app-templates/deploy-sqlscript.template.yml"

stages:
  - stage: Build
    jobs:
      - job: Backend
        steps:
          - template: ${{ variables.dotnetBuildTemplate }}
            parameters:
              appVersion: $(appVersion)
              srcFolder: '**/SwissRe.ADS.ESP.Backend/**'
              projectName: "SwissRe.ADS.ESP.Backend.Api"
              hasSql: false
              sonarQubeEnabled: ${{ parameters.enableqascan }}
              sonarProjectName: '<MISSING>'
              sonarProjectKey: '<MISSING>'
              nexusIqEnabled: ${{ parameters.enableqascan }}
              nexusProjectKey: '<MISSING>'
              artifactName: esp-backend

  # - stage: DEV
  #   jobs:
  #     - deployment:
  #       displayName: "Deploy App"
  #       environment: Dev
  #       strategy:
  #         runOnce:
  #           deploy:
  #             steps:           
  #             - template: ${{ variables.sqlScriptReleaseTemplate }}
  #               parameters:
  #                 serviceConnectionName: MOPED DEV CONNECTED 1-33d5d667-e667-436b-ac77-97d9da0dd904
  #                 serverName: sqlsrvrdcs0001618kwfran
  #                 database: MOPED_DEV                
  #             - template: ${{ variables.stratumReleaseTemplate }}
  #               parameters:
  #                 serviceConnectionName: MOPED DEV CONNECTED 1-33d5d667-e667-436b-ac77-97d9da0dd904
  #                 clusterEnv: DEV-WE
  #                 environment: Dev
  #                 stage: main
  #                 apmId: ${{ variables.apmId }}
  #                 imageTag: $(dockerImageTag)
  #                 components: ["moped-authproxy", "moped-app"]     

  # - stage: NONPROD
  #   condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/master'))
  #   variables:
  #     tfOutput: "{}"
  #     enableChangeManagementIntegration: false  
  #   jobs:
  #     - deployment:
  #       displayName: "Deploy App"
  #       environment: NONPROD
  #       strategy:
  #         runOnce:
  #           deploy:
  #             steps:           
  #             - template: ${{ variables.sqlScriptReleaseTemplate }}
  #               parameters:
  #                 serviceConnectionName: MOPED NONPROD CONNECTED 1-92efd697-426d-49e5-8987-991c2e33fbad
  #                 serverName: sqlsrvrncs0001929aixyhd
  #                 database: MOPED_NONPROD                
  #             - template: ${{ variables.stratumReleaseTemplate }}
  #               parameters:
  #                 serviceConnectionName: MOPED NONPROD CONNECTED 1-92efd697-426d-49e5-8987-991c2e33fbad
  #                 clusterEnv: NONPROD-WE
  #                 environment: Nonprod
  #                 stage: main
  #                 apmId: ${{ variables.apmId }}
  #                 imageTag: $(dockerImageTag)
  #                 components: ["moped-authproxy", "moped-app"]     

  # - stage: PROD
  #   condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/master'))
  #   variables:
  #     tfOutput: "{}"
  #     enableChangeManagementIntegration: true
  #     standardChangeTemplateid: "STDTEMPL1011370"
  #   jobs:
  #     - deployment:
  #       displayName: "Deploy App"
  #       environment: PROD
  #       strategy:
  #         runOnce:
  #           deploy:
  #             steps:
  #             - template: ${{ variables.sqlScriptReleaseTemplate }}
  #               parameters:
  #                 serviceConnectionName: MOPED PROD CONNECTED 1-3b581b05-c77c-447f-bf81-402b3ec23935
  #                 serverName: sqlsrvrpcs0001930jgmegh
  #                 database: MOPED_PROD                
  #             - template: ${{ variables.stratumReleaseTemplate }}
  #               parameters:
  #                 serviceConnectionName: MOPED PROD CONNECTED 1-3b581b05-c77c-447f-bf81-402b3ec23935
  #                 clusterEnv: PROD-WE
  #                 environment: Prod
  #                 stage: main
  #                 apmId: ${{ variables.apmId }}
  #                 imageTag: $(dockerImageTag)
  #                 components: ["moped-authproxy", "moped-app"]     
