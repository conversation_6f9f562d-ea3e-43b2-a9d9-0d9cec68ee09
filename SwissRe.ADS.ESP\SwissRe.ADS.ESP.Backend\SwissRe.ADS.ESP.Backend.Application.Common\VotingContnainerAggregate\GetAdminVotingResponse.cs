﻿using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate
{
    /// <summary>
    /// Response DTO representing voting-specific metadata for a user's voting container.
    /// Used to display summary information about a voting session on the home dashboard.
    /// </summary>
    public class GetAdminVotingResponse : GetContainerResponseBase
    {
        /// <summary>
        /// Total number of users with voting rights in the voting container.
        /// </summary>
        public int TotalUsersWithVotingRights { get; set; }

        /// <summary>
        /// Number of users, who already voted on each question.
        /// </summary>
        public int TotalUsersVoted { get; set; }
    }
}
