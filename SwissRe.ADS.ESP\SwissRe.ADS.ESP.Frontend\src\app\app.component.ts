import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { AppNavigationComponent } from './main-layout/app-navigation/components/app-navigation.component';
import { CommonModule } from '@angular/common';
import { AppHeaderComponent } from './main-layout/app-header/components/app-header.component';

@Component({
  selector: 'app-root',
  imports: [CommonModule, RouterOutlet, AppHeaderComponent, MatSidenavModule, AppNavigationComponent],
  template: `
    <app-header></app-header>

    <mat-sidenav-container class="sidenav-container">
      <mat-sidenav mode="side" [opened]="true" class="sidenav mat-elevation-z4">
        <app-navigation></app-navigation>
      </mat-sidenav>

      <mat-sidenav-content class="sidenav-content">
        <router-outlet></router-outlet>
      </mat-sidenav-content>
    </mat-sidenav-container>
  `,
  styles: `
    :host {
      display: flex;
      flex-direction: column;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .sidenav-container {
      flex: 1;

      .sidenav {
        width: 250px;
        padding: 16px 0;
      }

      .sidenav-content {
        padding: 16px;
        box-sizing: border-box;
      }
    }
  `
})
export class AppComponent {}
