-- MeetingsContainer table
/*
select * from esp.t_mp_meeting where item_type='M'
select * from esp.t_mp_type where t_mp_type.type_tid = 64500487


NOTE:
Is esp.t_mp_meeting.status_tid upload_status_tid from
select * from esp.t_upload_status
???

Do we need updated column? Why updated and update_time?
select * from esp.t_mp_meeting where updated is not null and updated <> update_time

*/

DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."MeetingsContainer") <> 0 THEN
        RAISE NOTICE 'Delete from espv2.MeetingsContainer';
        DELETE FROM espv2."MeetingsContainer";
    END IF;
END $$;

INSERT INTO espv2."MeetingsContainer" (
    "Id"
    ,"ORIGINAL_DB_ID"
    ,"DomainId"
    ,"Name"
    ,"Description"
    ,"State"
    ,"StartTime"
    ,"EndTime"
    ,"Location"
    ,"IncludeGuestRole"
    ,"AutomaticallyExcludeNewUsers"
    ,"LastUpdatedOnUTC"
    ,"Order"
)
select 
    gen_random_uuid() AS Id
    ,m.meeting_tid as ORIGINAL_DB_ID
    ,COALESCE(
        (SELECT "Id" FROM espv2."Domains" WHERE "ORIGINAL_DB_ID" = m.type_tid),
        '00000000-0000-0000-0000-000000000000'
        ) AS "DomainId"
    ,m.name as Name
    ,m.description as Description
    ,(select nme from esp.t_upload_status where upload_status_tid = m.status_tid) as State
    ,(m.time_start) as StartTime
    ,(m.time_end) as EndTime
    ,m.location as Location
    ,false AS IncludeGuestRole
    ,(CASE WHEN (SELECT COUNT(*) FROM esp.t_mp_access_detail ad WHERE ad.meeting_item_tid = m.meeting_tid AND ad.access_right_tid = 0) > 0 THEN TRUE ELSE FALSE END) AS AutomaticallyExcludeNewUsers
    ,COALESCE(m.updated, '1970-01-01 00:00:00') AS LastUpdatedOnUTC
    ,m.ord AS "Order"
/*
-- //consider storing creation/update time/name as it chould be in AuditLog
    ,m.creation_time
    ,m.creation_user_id
    ,m.creation_user_id as CreatedBy
    ,m.update_user_id
    ,m.update_user_id as UpdatedBy
    ,m.update_time

    ,m.geo_lat
    ,m.geo_long
    ,m.location_city
    ,m.location_address
    ,m.location_room
    ,m.time_start
    ,m.time_end

--    m.updated_stat_current,
--    m.updated_stat_firstpub,
--    m.updated_stat_lastarch,

--    m.old_name,
    ,m.updated -- select COUNT(*) FROM esp.t_mp_meeting where updated is not null AND updated <> update_time
    ,m.item_type
    ,m.owner
--    m.store_oracle,
--    m.store_sp,
    ,m.agenda_titles_visibility
    ,*
*/
from 
    esp.t_mp_meeting m
where m.item_type='M' -- and m.start_time is not null

/*
select 
    *,
    (select access_right from esp.t_access_right where access_right_tid = ad.access_right_tid)
from 
    esp.t_mp_access_detail ad;
select * from esp.t_access_right

select count(distinct meeting_item_tid) from esp.t_mp_access_detail where access_right_tid = 0
*/