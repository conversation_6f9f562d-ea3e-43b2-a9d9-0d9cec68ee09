﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.ReadModels
{
    public class DomainReadModelRepository : IDomainReadModelRepository
    {
        private readonly AppDbContext _context;

        public DomainReadModelRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<List<(AgendaPermissionEnum accessType, string userId)>> GetDomainAgendaPermissions(Guid domainId, CancellationToken cancellationToken = default)
        {
            var roleUserGroups = await (from domainRole in _context.Set<EspDomainRole>()
                                        where domainRole.DomainId == domainId
                                        join role in _context.Set<EspRole>() on domainRole.RoleId equals role.Id
                                        join userRole in _context.Set<UserEspRole>() on role.Id equals userRole.RoleId
                                        join user in _context.Set<User>() on userRole.UserId equals user.Id
                                        select new
                                        {
                                            RoleId = role.Id,
                                            RoleName = role.Name,
                                            domainRole.RoleType,
                                            UserId = user.Id
                                        })
                                        .AsNoTracking()
                                        .ToListAsync(cancellationToken);

            var userRoles = roleUserGroups.Select(item => (item.RoleType.ToAgendaPermission(), item.UserId)).ToList();

            // group by userId and combine AgendaPermissionEnum into single flag in case of multiple user entries
            return userRoles
                .GroupBy(x => x.UserId)
                .Select(g => (g.Aggregate((AgendaPermissionEnum)0, (current, item) => current | item.Item1), g.Key))
                .ToList();
        }


        //TODO: Move to sync jobs directly?
        public async Task<AgendaPermissionEnum?> GetDomainAgendaPermissionForUser(Guid domainId, string userId, CancellationToken cancellationToken = default)
        {
            var roleUserGroups = await (from domainRole in _context.Set<EspDomainRole>()
                                        where domainRole.DomainId == domainId
                                        join role in _context.Set<EspRole>() on domainRole.RoleId equals role.Id
                                        join userRole in _context.Set<UserEspRole>() on role.Id equals userRole.RoleId
                                        join user in _context.Set<User>() on userRole.UserId equals user.Id
                                        where user.Id == userId
                                        select new
                                        {
                                            domainRole.RoleType,
                                            UserId = user.Id
                                        })
                                        .AsNoTracking()
                                        .ToListAsync(cancellationToken);

            if (roleUserGroups.Count == 0)
                return null;

            // Combine all permissions for this user
            var combinedPermission = roleUserGroups
                .Select(rt => rt.RoleType.ToAgendaPermission())
                .Aggregate((AgendaPermissionEnum)0, (acc, perm) => acc | perm);

            return combinedPermission;
        }

        //TODO: Move to sync jobs directly?
        public async Task<List<(AgendaPermissionEnum accessType, Guid domainId)>> GetAgendaPermissionsForAllDomainsForUser(string userId, CancellationToken cancellationToken = default)
        {
            var roleUserGroups = await (from domainRole in _context.Set<EspDomainRole>()
                                        join role in _context.Set<EspRole>() on domainRole.RoleId equals role.Id
                                        join userRole in _context.Set<UserEspRole>() on role.Id equals userRole.RoleId
                                        join user in _context.Set<User>() on userRole.UserId equals user.Id
                                        where user.Id == userId
                                        select new
                                        {
                                            domainRole.RoleType,
                                            domainRole.DomainId
                                        })
                                        .AsNoTracking()
                                        .ToListAsync(cancellationToken);
            
            var userRoles = roleUserGroups.Select(item => (item.RoleType.ToAgendaPermission(), item.DomainId)).ToList();
            
            // group by domainId and combine AgendaPermissionEnum into single flag in case of multiple user entries
            return userRoles
                .GroupBy(x => x.DomainId)
                .Select(g => (g.Aggregate((AgendaPermissionEnum)0, (current, item) => current | item.Item1), g.Key))
                .ToList();
        }
    }
}
