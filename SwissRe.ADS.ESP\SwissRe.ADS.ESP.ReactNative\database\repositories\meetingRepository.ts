import { ContainerStateEnum, EspChangeEntryResponse, GetMeetingDetailResponse } from '@/api/apiServices';
import { MeetingEntity, meetingEntityColumnSchema } from '../types/MeetingEntity';
import { BaseRepository } from './baseRepository';

export class MeetingRepository extends BaseRepository<MeetingEntity> {
  constructor() {
    super('MeetingEntities', meetingEntityColumnSchema);
  }

  async syncNewMeetingChanges(addedOrUpdatedMeetingChanges: EspChangeEntryResponse[]) {
    const meetings: MeetingEntity[] = addedOrUpdatedMeetingChanges.map((newItem) => {
      const deserializedMeetingObject = GetMeetingDetailResponse.fromJS(JSON.parse(newItem.valueAsJson ?? ''));

      return {
        id: newItem.entityId,
        valueAsJson: newItem.valueAsJson!,
        name: deserializedMeetingObject.name,
        description: deserializedMeetingObject.description ?? '',
        state: deserializedMeetingObject.state,
        lastUpdatedOn: deserializedMeetingObject.lastUpdatedOn,
        numberOfUnreadDocuments: 5,
      };
    });

    await this.insertOrReplaceMany(meetings);
  }

  async getMeetingById(id: string): Promise<GetMeetingDetailResponse | undefined> {
    const meeting = await this.getById(id);
    if (!meeting?.valueAsJson) return undefined;

    const parsedMeetingObject = JSON.parse(meeting.valueAsJson);
    if (!parsedMeetingObject) return undefined;

    return GetMeetingDetailResponse.fromJS(parsedMeetingObject);
  }

  async hasMeetingByIdChanged(meetingId: string, lastUpdatedOn: Date): Promise<boolean> {
    const meeting = await this.getById(meetingId);
    if (!meeting) return true; // If no meeting found, consider it changed

    console.log('Current meeting last updated on', lastUpdatedOn);
    console.log('Retrieved database last updated on', meeting.lastUpdatedOn);
    return meeting.lastUpdatedOn !== lastUpdatedOn;
  }

  async getAllCurrentMeetings(): Promise<MeetingEntity[]> {
    console.log('Retrieving all current meetings');
    return await this.getAll(`state IN ('${ContainerStateEnum.Draft}', '${ContainerStateEnum.Published}')`);
  }

  async getAllPastMeetings(): Promise<MeetingEntity[]> {
    console.log('Retrieving all past meetings');
    return await this.getAll(`state IN ('${ContainerStateEnum.Archived}', '${ContainerStateEnum.PastMeeting}')`);
  }
}
