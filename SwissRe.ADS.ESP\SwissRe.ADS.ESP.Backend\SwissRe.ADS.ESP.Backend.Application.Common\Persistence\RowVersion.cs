﻿namespace SwissRe.ADS.ESP.Backend.Application.Common.Persistence
{
    /// <summary>
    /// Represents the value of RowVersion column.
    /// </summary>
    /// <param name="Base64Value"></param>
    public readonly record struct RowVersion(string Base64Value)
    {
        public RowVersion(byte[] value) : this(Convert.ToBase64String(value))
        {
        }

        public static implicit operator RowVersion(byte[] value) => new(value);
        public static implicit operator byte[](RowVersion rowVersion) => Convert.FromBase64String(rowVersion.Base64Value);

        public static implicit operator RowVersion(string base64Value) => new(base64Value);
        public static implicit operator string(RowVersion rowVersion) => rowVersion.Base64Value;
    }
}
