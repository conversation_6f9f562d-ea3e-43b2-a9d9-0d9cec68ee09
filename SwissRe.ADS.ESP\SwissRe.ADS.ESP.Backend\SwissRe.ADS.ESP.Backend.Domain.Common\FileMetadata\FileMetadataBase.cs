﻿using Ardalis.GuardClauses;

namespace SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata
{
    public abstract class FileMetadataBase : GuidEntity
    {
        private List<ReadStatus> _readStatuses = [];

        /// <summary>
        /// Foreign Key reference to FileContents table
        /// </summary>
        public Guid FileContentId { get; private set; }

        public string OriginalFileName { get; private set; } = null!;
        public string DisplayFileName { get; private set; } = null!;
        public string FileExtension { get; private set; } = null!;
        public bool Supplementary { get; private set; } = false;

        /// <summary>
        /// Groups multiple versions of the same file under a single file group.
        /// </summary>
        public Guid FileGroupId { get; private set; }

        /// <summary>
        /// Current version number in case of multiple versions of the same file
        /// </summary>
        public int VersionNumber { get; private set; }

        /// <summary>
        /// Determines whether this is the latest version of a file
        /// </summary>
        public bool IsLatest { get; private set; }

        public int Order { get; private set; } = 0;
        public int Size { get; private set; } = 0;
        public int NumberOfPages { get; private set; } = 0;
        public DateTime CreatedOn { get; private set; }
        public string CreatedByUserId { get; private set; } = null!;
        public string LastModifiedByUserId { get; private set; } = null!;
        public DateTime LastModifiedOn { get; private set; }
        public IReadOnlyCollection<ReadStatus> ReadStatuses => _readStatuses;

        protected void Initialize(string originalFileName,
            Guid fileGroupId,
            Guid fileContentId,
            int versionNumber,
            string fileExtension,
            int size,
            int numberOfPages,
            int order,
            string createdByUserId,
            bool supplementary = false)
        {
            Guard.Against.NullOrWhiteSpace(originalFileName, nameof(OriginalFileName), "File name cannot be blank.");
            Guard.Against.NullOrWhiteSpace(fileExtension, nameof(FileExtension), "File extension cannot be blank.");
            Guard.Against.NegativeOrZero(numberOfPages, nameof(numberOfPages), "Number of pages must be greater than zero.");
            Guard.Against.NegativeOrZero(size, nameof(size), "File size must be greater than zero.");

            FileContentId = fileContentId;
            OriginalFileName = originalFileName;
            DisplayFileName = originalFileName;
            FileGroupId = fileGroupId;
            FileExtension = fileExtension;
            VersionNumber = versionNumber;
            Order = order;
            NumberOfPages = numberOfPages;
            Size = size;
            Supplementary = supplementary;
            IsLatest = true;
            CreatedByUserId = createdByUserId;
            CreatedOn = DateTime.UtcNow;
            LastModifiedByUserId = createdByUserId;
            LastModifiedOn = DateTime.UtcNow;
        }

        /// <summary>
        /// When uploading a newer version of the file, 
        /// we have to mark original as obsolete.
        /// </summary>
        internal void MarkAsOld()
        {
            IsLatest = false;

            //remove tracking from read statuses
            _readStatuses.Clear();
        }

        public void MarkAsRead(string userId)
        {
            Guard.Against.NullOrEmpty(userId, nameof(userId), "User ID cannot be null or empty.");

            if (IsLatest == false)
                throw new ArgumentException("Only the latest version of the file can be marked as read.");

            if (_readStatuses.Any(rs => rs.UserId == userId))
            {
                // User has already read the file
                return;
            }

            _readStatuses.Add(ReadStatus.Create(userId));
        }

        public void MarkAsUnread(string userId)
        {
            Guard.Against.NullOrEmpty(userId, nameof(userId), "User ID cannot be null or empty.");

            if (IsLatest == false)
                throw new ArgumentException("Only the latest version of the file can be marked as unread.");

            var readStatus = _readStatuses.FirstOrDefault(rs => rs.UserId == userId);
            if (readStatus != null)
            {
                _readStatuses.Remove(readStatus);
            }
        }
    }
}
