﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace SwissRe.ADS.ESP.Backend.Application.Common
{
    public static class ProblemDetailsUtils
    {
        public static ValueTask WriteProblemDetailsAsync(this HttpContext context, string title, IDictionary<string, string[]> validationErrors, int statusCode = StatusCodes.Status400BadRequest, string? detail = null)
        {
            var problemDetails = new HttpValidationProblemDetails { Errors = validationErrors }.WithValues(title, statusCode, detail);
            return context.WriteProblemDetailsAsync(problemDetails);
        }

        public static ValueTask WriteProblemDetailsAsync(this HttpContext context, string title, int statusCode, string? detail = null)
        {
            var problemDetails = new ProblemDetails().WithValues(title, statusCode, detail);
            return context.WriteProblemDetailsAsync(problemDetails);
        }

        public static ValueTask WriteProblemDetailsAsync(this HttpContext context, ProblemDetails problemDetails)
        {
            context.Response.StatusCode = problemDetails.Status ?? StatusCodes.Status500InternalServerError;

            return context.RequestServices.GetRequiredService<IProblemDetailsService>()
                .WriteAsync(new()
                {
                    HttpContext = context,
                    ProblemDetails = problemDetails
                });
        }

        private static ProblemDetails WithValues(this ProblemDetails problemDetails, string title, int statusCode, string? detail)
        {
            problemDetails.Type = $"https://httpstatuses.io/{statusCode}";
            problemDetails.Status = statusCode;
            problemDetails.Title = title;
            problemDetails.Detail = detail;

            return problemDetails;
        }
    }
}
