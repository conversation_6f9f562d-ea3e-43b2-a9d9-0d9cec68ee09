﻿using Microsoft.Extensions.Logging;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.JobExecutionAggregate;

namespace SwissRe.ADS.ESP.Backend.Jobs.Common
{
    public abstract class DeltaSyncJobBase<TEntity> where TEntity : class, IEntity, IAggregateRoot, ISyncJobEntity
    {
        private readonly UnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly string _jobName;
        private readonly int _batchSize = 100;

        protected DeltaSyncJobBase(UnitOfWork unitOfWork, ILogger logger, string jobName)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _jobName = jobName ?? throw new ArgumentNullException(nameof(jobName));
        }

        /// Override this to filter/project entities to process.
        protected abstract Func<IQueryable<TEntity>, IQueryable<TEntity>> BuildSourceQuery();

        /// <summary>
        /// Override this to process a single entity.
        /// </summary>
        protected abstract Task ProcessEntityAsync(TEntity entity, CancellationToken token);

        public async Task RunAsync(CancellationToken token = default)
        {
            var metadata = await LoadOrInitMetadataAsync(token);

            bool hasMore = true;

            while (hasMore && !token.IsCancellationRequested)
            {
                var query = ApplyDeltaFiltering(metadata, BuildSourceQuery());

                //add pagination to the query
                var pageQuery = new Func<IQueryable<TEntity>, IQueryable<TEntity>>(q => query(q).Take(_batchSize));
                var batch = await _unitOfWork.Repository<TEntity>().GetAllAsync(pageQuery, forUpdate: false);

                if (batch.Count == 0)
                {
                    hasMore = false;
                    break;
                }

                foreach (var entity in batch)
                {
                    try
                    {
                        await ProcessEntityAsync(entity, token);

                        // Update metadata using the last item in batch
                        metadata.LastProcessedTimestampUtc = entity.LastModifiedOnUTC;
                        metadata.LastProcessedSequenceId = entity.SequenceId;
                        metadata.LastUpdatedAt = DateTime.UtcNow;

                        await _unitOfWork.CommitTransactionAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing entity {sequenceId}", entity.SequenceId);

                        //TODO: introduce critical exception or regular exception with retry mechanism
                    }
                }
            }

            if (!token.IsCancellationRequested)
            {
                metadata.LastSuccessfulRunUtc = metadata.JobRunWindowEndUtc;
                metadata.JobRunWindowEndUtc = null;
                metadata.LastProcessedTimestampUtc = null;
                metadata.LastProcessedSequenceId = 0;
                metadata.LastUpdatedAt = DateTime.UtcNow;

                await _unitOfWork.CommitTransactionAsync();
                _logger.LogInformation("Job {JobName} completed successfully.", _jobName);
            }
        }

        private Func<IQueryable<TEntity>, IQueryable<TEntity>> ApplyDeltaFiltering(
                JobExecutionMetadata metadata,
                Func<IQueryable<TEntity>, IQueryable<TEntity>> sourceQuery)
        {
            return query =>
                sourceQuery(query)
                .Where(e =>
                    (e.LastModifiedOnUTC > metadata.LastProcessedTimestampUtc ||
                    (e.LastModifiedOnUTC == metadata.LastProcessedTimestampUtc &&
                     e.SequenceId > metadata.LastProcessedSequenceId)) &&
                    e.LastModifiedOnUTC < metadata.JobRunWindowEndUtc)
                .OrderBy(e => e.LastModifiedOnUTC)
                .ThenBy(e => e.SequenceId);
        }

        private async Task<JobExecutionMetadata> LoadOrInitMetadataAsync(CancellationToken token)
        {
            var metadata = await _unitOfWork.Repository<JobExecutionMetadata>().GetOrNullAsync(_jobName, true);
            if (metadata == null)
            {
                metadata = JobExecutionMetadata.CreateBlank(_jobName);
                _unitOfWork.Repository<JobExecutionMetadata>().Insert(metadata);
                await _unitOfWork.CommitTransactionAsync();
            }

            if (metadata.JobRunWindowEndUtc == null)
            {
                metadata.JobRunWindowEndUtc = DateTime.UtcNow;
                metadata.LastProcessedTimestampUtc ??= metadata.LastSuccessfulRunUtc ?? DateTime.MinValue;
                metadata.LastProcessedSequenceId = 0;
                metadata.LastUpdatedAt = DateTime.UtcNow;
                await _unitOfWork.CommitTransactionAsync();
            }

            return metadata;
        }
    }
}
