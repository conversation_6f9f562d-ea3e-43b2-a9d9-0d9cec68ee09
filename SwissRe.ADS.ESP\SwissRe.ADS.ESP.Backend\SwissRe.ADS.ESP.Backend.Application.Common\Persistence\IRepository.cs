﻿using SwissRe.ADS.ESP.Backend.Domain.Common;
using System.Linq.Expressions;

namespace SwissRe.ADS.ESP.Backend.Application.Common.Persistence
{
    public interface IRepository<TAggregateRoot>
        where TAggregateRoot : class, IEntity, IAggregateRoot
    {
        Task<TAggregateRoot> GetFirstAsync(Expression<Func<TAggregateRoot, bool>> predicate, bool forUpdate = false);
        Task<TAggregateRoot?> GetFirstOrNullAsync(Expression<Func<TAggregateRoot, bool>> predicate, bool forUpdate = false);
        Task<TResult?> GetFirstOrNullAsync<TResult>(Func<IQueryable<TAggregateRoot>, IQueryable<TResult>> query, bool forUpdate = false);
        Task<List<TAggregateRoot>> GetAllAsync(bool forUpdate = false);
        Task<List<TAggregateRoot>> GetAllAsync(Expression<Func<TAggregateRoot, bool>> predicate, bool forUpdate = false);
        Task<List<TResult>> GetAllAsync<TResult>(Func<IQueryable<TAggregateRoot>, IQueryable<TResult>> query, bool forUpdate = false);
        void Insert(TAggregateRoot aggregateRoot);
        void Delete(TAggregateRoot aggregateRoot);

        /// <summary>
        /// Compares the expected RowVersion value with the actual one stored in the database. Throws <see cref="AggregateChangedException"/> if they are different.
        /// </summary>
        void CheckRowVersionUnchanged(TAggregateRoot aggregateRoot, RowVersion expected);

        /// <summary>
        /// Gets the value of RowVersion column for the specified <see cref="TAggregateRoot">.
        /// </summary>
        RowVersion GetRowVersion(TAggregateRoot aggregateRoot);

        /// <summary>
        /// Can be used to retrieve shadow properties.
        /// </summary>
        object? GetPropertyValue(TAggregateRoot aggregateRoot, string propertyName);
    }
}
