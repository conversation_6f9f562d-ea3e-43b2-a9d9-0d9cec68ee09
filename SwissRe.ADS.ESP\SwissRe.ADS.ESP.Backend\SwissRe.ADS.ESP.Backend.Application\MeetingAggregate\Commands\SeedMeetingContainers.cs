﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using Microsoft.AspNetCore.Http;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Commands
{
    public class SeedMeetingContainersEndpoint(UnitOfWork unitOfWork, ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
                   builder
                       .MapPost("/seed-TOREMOVE", (Guid domainId, SeedMeetingContainersEndpoint endpoint) => endpoint.HandleAsync(domainId))
                       .WithAngularName<MeetingContainerRouteGroup>("SEED-TOREMOVE");

        public async Task HandleAsync(Guid domainId)
        {
            var repo = _unitOfWork.Repository<MeetingContainer>();
            var random = new Random();

            for (int i = 1; i <= 1000; i++)
            {
                // Randomly decide if the user has access to this container's agendas
                bool hasAccess = random.Next(2) == 0;

                string containerName = hasAccess
                    ? $"MeetingContainer_{i}_ACCESS"
                    : $"MeetingContainer_{i}_NOACCESS";

                var startTime = DateTime.UtcNow.AddDays(i);
                var endTime = startTime.AddHours(2);

                var container = MeetingContainer.Create(
                    containerName,
                    domainId,
                    startTime,
                    endTime,
                    location: $"Room {i}",
                    description: $"Seeded container {i}");

                // Prepare agenda permissions
                var agendaUserAccess = new List<(AgendaPermissionEnum, string)>();
                if (hasAccess)
                {
                    // Give Manage access to the user
                    agendaUserAccess.Add((AgendaPermissionEnum.Manage, _currentUser.SrUserId));
                }
                else
                {
                    // Explicitly deny access
                    agendaUserAccess.Add((AgendaPermissionEnum.Deny, _currentUser.SrUserId));
                }

                // Add two agendas per container
                container.AddAgenda(
                    $"Agenda 1 for {containerName}",
                    $"Description 1 for {containerName}",
                    order: 1,
                    agendaUserAccess: agendaUserAccess);

                container.AddAgenda(
                    $"Agenda 2 for {containerName}",
                    $"Description 2 for {containerName}",
                    order: 2,
                    agendaUserAccess: agendaUserAccess);

                repo.Insert(container);
            }

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
