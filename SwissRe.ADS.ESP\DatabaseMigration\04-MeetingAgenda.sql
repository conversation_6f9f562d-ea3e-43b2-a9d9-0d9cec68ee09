-- MeetingAgenda Table
/*
select * FROM esp.t_mp_agenda

NOTE: updated is not needed! 

select COUNT(*) FROM esp.t_mp_agenda where updated is not null 

returns 0
*/
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."MeetingsAgenda") <> 0 THEN
        RAISE NOTICE 'Delete from espv2.MeetingsAgenda';
        DELETE FROM espv2."MeetingsAgenda";
    END IF;
END $$;


INSERT INTO espv2."MeetingsAgenda" (
    "Id"
    ,"ORIGINAL_DB_ID"
    ,"ParentContainerId"
    ,"Name"
    ,"Description"
    ,"Order"
    ,"ParentAgendaId"
)
SELECT 
    gen_random_uuid() AS Id
    ,a.agenda_tid AS ORIGINAL_DB_ID
    ,(SELECT mc."Id" FROM espv2."MeetingsContainer" mc WHERE a.meeting_tid = mc."ORIGINAL_DB_ID") AS ParentContainerId
    ,a.name AS "Name"
    ,a.description AS "Description"
    ,a.ord AS "Order"
    , NULL AS ParentAgendaId
-- //consider storing creation/update time/name as it chould be in AuditLog
/*
    ,a.creation_time
    ,a.creation_user_id as CreatedBy
    ,a.update_user_id as UpdatedBy
    ,a.update_time

--    a.updated, -- all null not needed: select COUNT(*) FROM esp.t_mp_agenda where updated is not null
--    a.item_type -- we do not need this
*/
FROM 
    esp.t_mp_agenda a
where 
    a.meeting_tid in (select meeting_tid from esp.t_mp_meeting where item_type='M')
    -- a.meeting_tid in (select ORIGINAL_DB_ID from MeetingContainer)
