import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

@Component({
    imports: [MatIconModule],
    template: `
    <mat-icon class="back-icon">celebration</mat-icon>
    <p class="title">You found it!</p>
    <div class="text">
      <p>Well done! You have found a very <strong>rare exception</strong> in our application.</p>
      <p>If you have a couple of minutes, we would really appreciate if you could tell us how you did it.</p>
    </div>
    <div class="actions">
      <mat-icon role="button" color="accent" class="action-icon" matTooltip="Send us an email">email</mat-icon>
      <mat-icon role="button" color="accent" class="action-icon" matTooltip="Chat with us via MS Teams">chat</mat-icon>
    </div>
  `,
    styles: `
    :host {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      gap: 24px;
    }
    .title {
      font-size: 1.7rem;
      font-weight: bold;
    }
    .text {
      text-align: center;
    }
    .actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    .action-icon {
      width: 32px;
      height: 32px;
      font-size: 32px;
      cursor: pointer;
    }
    .back-icon {
      width: 300px;
      height: 300px;
      font-size: 300px;
      position: absolute;
      bottom: 0;
      right: 0;
      color: #006d3621;
    }
  `
})
export class Error500Component {}
