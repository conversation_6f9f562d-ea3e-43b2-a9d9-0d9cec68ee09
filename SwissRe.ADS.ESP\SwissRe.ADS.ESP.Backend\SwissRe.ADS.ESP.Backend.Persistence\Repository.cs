﻿using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using System.Linq.Expressions;

namespace SwissRe.ADS.ESP.Backend.Persistence
{
    public class Repository<TAggregateRoot>(AppDbContext dbContext) : IRepository<TAggregateRoot>
        where TAggregateRoot : class, IEntity, IAggregateRoot
    {
        /// <summary>
        /// The underlying <see cref="DbSet{TEntity}"/>.
        /// </summary>
        protected DbSet<TAggregateRoot> Set { get; } = dbContext.Set<TAggregateRoot>();

        public async Task<TAggregateRoot> GetFirstAsync(Expression<Func<TAggregateRoot, bool>> predicate, bool forUpdate = false) =>
            await GetFirstOrNullAsync(predicate, forUpdate) ?? throw new AggregateRootNotFoundException<TAggregateRoot>();

        public Task<TAggregateRoot?> GetFirstOrNullAsync(Expression<Func<TAggregateRoot, bool>> predicate, bool forUpdate = false) =>
            AsQueryable(forUpdate).FirstOrDefaultAsync(predicate);

        public Task<TResult?> GetFirstOrNullAsync<TResult>(Func<IQueryable<TAggregateRoot>, IQueryable<TResult>> query, bool forUpdate = false) =>
            query(AsQueryable(forUpdate)).FirstOrDefaultAsync();

        public Task<List<TAggregateRoot>> GetAllAsync(bool forUpdate = false) =>
            AsQueryable(forUpdate).ToListAsync();

        public Task<List<TAggregateRoot>> GetAllAsync(Expression<Func<TAggregateRoot, bool>> predicate, bool forUpdate = false) =>
            AsQueryable(forUpdate).Where(predicate).ToListAsync();

        public Task<List<TResult>> GetAllAsync<TResult>(Func<IQueryable<TAggregateRoot>, IQueryable<TResult>> query, bool forUpdate = false) =>
            query(AsQueryable(forUpdate)).ToListAsync();

        public void Insert(TAggregateRoot aggregateRoot) =>
            Set.Add(aggregateRoot);

        public void Delete(TAggregateRoot aggregateRoot) =>
            Set.Remove(aggregateRoot);

        /// <summary>
        /// Compares the expected RowVersion value with the actual one stored in the database. Throws <see cref="AggregateChangedException"/> if they are different.
        /// </summary>
        public void CheckRowVersionUnchanged(TAggregateRoot aggregateRoot, RowVersion expected)
        {
            var actual = GetRowVersion(aggregateRoot);

            if (expected != actual)
            {
                throw new AggregateChangedException(aggregateRoot.GetType().Name, expected, actual);
            }
        }

        /// <summary>
        /// Gets the value of RowVersion column for the specified <see cref="TAggregateRoot">.
        /// </summary>
        public RowVersion GetRowVersion(TAggregateRoot aggregateRoot)
        {
            var rowVersion = Set.Entry(aggregateRoot).Property(RowVersionUtils.ColumnName).OriginalValue ??
                throw new InvalidOperationException($"Aggregate root of type {aggregateRoot.GetType().Name} does not have a row version.");

            return (RowVersion)(byte[])rowVersion;
        }

        /// <summary>
        /// Can be used to retrieve shadow properties.
        /// </summary>
        public object? GetPropertyValue(TAggregateRoot aggregateRoot, string propertyName) =>
            Set.Entry(aggregateRoot).Property(propertyName).CurrentValue;

        protected IQueryable<TAggregateRoot> AsQueryable(bool forUpdate) =>
            forUpdate ? Set : Set.AsNoTracking();
    }
}
