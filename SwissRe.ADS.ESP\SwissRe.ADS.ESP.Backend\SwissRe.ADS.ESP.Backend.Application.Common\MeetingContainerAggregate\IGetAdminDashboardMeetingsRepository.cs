﻿using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate
{
    public interface IGetAdminDashboardMeetingsRepository : IReadModelRepository
    {
        /// <summary>
        /// Retrieves all meeting containers accessible to the specified admin that are in the current or future state.
        /// Only containers with <see cref="ContainerStateEnum.Published"/> are included.
        /// The result includes meeting metadata, agenda information where applicable.
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token for async operation.</param>
        /// <returns>
        /// A list of <see cref="GetAdminMeetingResponse"/> objects representing current and future meetings to the admin.
        /// </returns>
        Task<List<GetAdminMeetingResponse>> GetCurrentAndFutureAdminMeetings(CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves all meeting containers accessible to the specified admin that are in the completed/past state.
        /// Only containers with <see cref="ContainerStateEnum.PastMeeting"/> are included.
        /// The result includes meeting metadata, agenda information where applicable.
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token for async operation.</param>
        /// <returns>
        /// A list of <see cref="GetAdminMeetingResponse"/> objects representing completed meetings to the admin.
        /// </returns>
        Task<List<GetAdminMeetingResponse>> GetPastAdminMeetings(CancellationToken cancellationToken = default);
    }
}
