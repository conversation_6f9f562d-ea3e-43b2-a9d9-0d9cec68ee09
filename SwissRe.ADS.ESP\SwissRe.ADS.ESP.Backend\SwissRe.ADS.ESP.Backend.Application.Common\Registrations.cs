﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Application.Common.DomainServices;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using SwissRe.ADS.ESP.Backend.Application.Common.PDFTron;

namespace SwissRe.ADS.ESP.Backend.Application.Common
{
    public static class Registrations
    {
        public static void AddApplication(this IServiceCollection services, IConfiguration configuration)
        {
            ConfigureOptions(services, configuration);

            //one time registration of PDFTron
//            pdftron.PDFNet.Initialize(configuration.GetValue<string>($"{PDFTronConfigurationOptions.SectionName}:{nameof(PDFTronConfigurationOptions.LicenseKey)}"));

            services.AddScoped<UnitOfWork>();
            services.AddScoped<IAgendaAuthorizationService, AgendaAuthorizationService>();
            services.AddScoped<IContainerAuthorizationService, ContainerAuthorizationService>();
            services.AddScoped<IPdfThumbnailGeneratorService, PdfThumbnailGeneratorService>();
            services.AddScoped<IPdfPageCounterService, PdfPageCounterService>();
            services.AddScoped<IAgendaInfoProviderService, AgendaInfoProviderService>();
            
            //TODO: Should this be Singleton? This never changes
            services.AddScoped<IEspEncryptionService, EspEncryptionService>();
        }

        private static void ConfigureOptions(IServiceCollection services, IConfiguration configuration)
        {
            services.AddOptions<EspEncryptionConfigurationOptions>().Configure<IConfiguration>((options, config) =>
            {
                config.GetSection(EspEncryptionConfigurationOptions.SectionName).Bind(options);
            });

            services.AddOptions<PDFTronConfigurationOptions>().Configure<IConfiguration>((options, config) =>
            {
                config.GetSection(PDFTronConfigurationOptions.SectionName).Bind(options);
            });

        }
    }
}
