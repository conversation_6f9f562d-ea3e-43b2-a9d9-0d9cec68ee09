import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { AppNavigationActionItem } from '../model';

@Component({
  selector: 'app-navigation-action-item',
  imports: [RouterModule, MatIconModule],
  template: `
    <div class="container" routerLinkActive="active">
      @if (item(); as item) {
        <mat-icon>{{ item.icon }}</mat-icon>

        @switch (item.action.type) {
          @case ('routerLink') {
            <a
              [routerLink]="item.action.commands"
              [queryParams]="item.action.extras?.queryParams"
              [queryParamsHandling]="item.action.extras?.queryParamsHandling"
              [relativeTo]="item.action.extras?.relativeTo"
              [fragment]="item.action.extras?.fragment"
              [preserveFragment]="item.action.extras?.preserveFragment"
              [skipLocationChange]="item.action.extras?.skipLocationChange"
              [replaceUrl]="item.action.extras?.replaceUrl"
              [state]="item.action.extras?.state"
              >{{ item.title }}
            </a>
          }

          @case ('externalLink') {
            <a [href]="item.action.url" [target]="item.action.target">{{ item.title }}</a>
          }

          @case ('function') {
            <div role="button" (click)="item.action.function()">{{ item.title }}</div>
          }
        }
      }
    </div>
  `,
  styles: `
    .container {
      display: grid;
      align-items: center;
      grid-template-columns: 24px 1fr;
      gap: 8px;
      padding: 16px;
      cursor: pointer;

      &:not(.active) {
        color: #3C4150;

        icon {
          color: #636773;
        }
      }

      &.active {
        color: #1455B4;
        background-color: #DCE6F5;
      }
    }
    a {
      font-size: 16px;
      font-weight: 400;

      &:link,
      &:visited,
      &:hover,
      &:active {
        color: unset;
        text-decoration: none;
      } 
    }
  `
})
export class AppNavigationActionItemComponent {
  readonly item = input.required<AppNavigationActionItem>();
}
