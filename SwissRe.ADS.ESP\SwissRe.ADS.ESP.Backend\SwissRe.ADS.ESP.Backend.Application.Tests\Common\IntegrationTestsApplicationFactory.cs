﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SwissRe.ADS.ESP.Backend.Api;
using SwissRe.ADS.ESP.Backend.Persistence;
using System.Reflection;

namespace SwissRe.ADS.ESP.Backend.Application.Tests.Common
{
    public class IntegrationTestsApplicationFactory : WebApplicationFactory<Program>
    {
        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureServices(services =>
            {
                services.Configure<PersistenceOptions>(options => options.AssembliesWithConfigurations.Add(Assembly.GetExecutingAssembly()));
            });

            builder.UseEnvironment("Test");
        }
    }
}
