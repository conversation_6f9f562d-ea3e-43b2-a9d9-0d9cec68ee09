{"expo": {"name": "SwissRe.ADS.ESP.ReactNative", "slug": "SwissRe.ADS.ESP.ReactNative", "version": "1.0.1", "jsEngine": "hermes", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.swissre.iesp.plus.rn", "appleTeamId": "38N62U27YJ"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.anonymous.SwissRe.ADS.ESP.ReactNative"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-sqlite"], "experiments": {"typedRoutes": true}}}