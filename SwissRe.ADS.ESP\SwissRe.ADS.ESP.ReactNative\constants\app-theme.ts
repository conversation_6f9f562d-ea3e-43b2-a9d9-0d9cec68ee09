import { Platform, TextStyle } from 'react-native';
import { Theme as NavigationTheme } from '@react-navigation/native';

type FontWeight = TextStyle['fontWeight'];

export type ColorPalette = NavigationTheme['colors'] & {
  primary: string;
  secondary: string;
  tertiary: string;
  background: string;
  backgroundColors: BackgroundColors;
  textColors: TextColors;
  card: string;
  border: string;
  notification: string;
  icon: string;
  link: string;
  transparent: string;
};

export type BackgroundColors = {
  primary: string;
  secondary: string;
  tertiary: string;
};

export type TextColors = {
  primary: string;
  secondary: string;
};

export type FontDefinition = {
  fontFamily: string;
  fontWeight: FontWeight;
};

export type Fonts = NavigationTheme['fonts'] & {
  regular: FontDefinition;
  medium: FontDefinition;
  bold: FontDefinition;
  heavy: FontDefinition;
};

export type AppTheme = NavigationTheme & {
  dark: boolean;
  colors: ColorPalette;
  fonts: Fonts;
};

const fontDetails: Fonts = Platform.select({
  ios: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    bold: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    heavy: {
      fontFamily: 'System',
      fontWeight: '700',
    },
  },
  default: {
    regular: {
      fontFamily: 'sans-serif',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'sans-serif-medium',
      fontWeight: 'normal',
    },
    bold: {
      fontFamily: 'sans-serif',
      fontWeight: '600',
    },
    heavy: {
      fontFamily: 'sans-serif',
      fontWeight: '700',
    },
  },
});

export const appLightTheme: AppTheme = {
  dark: false,
  colors: {
    primary: '#007AFF',
    secondary: '#34C759',
    tertiary: '#FFFFFF',
    background: '#F2F2F7',
    backgroundColors: {
      primary: '#F2F2F7',
      secondary: '#F2F2F7',
      tertiary: '#FFFFFF',
    },
    text: '#000000',
    textColors: {
      primary: '#000000',
      secondary: '#7D7D7D',
    },
    card: '#F0F0F0',
    border: '#E0E0E0',
    notification: '#FF453A',
    icon: '#007AFF',
    link: '#007AFF',
    transparent: 'rgba(0, 0, 0, 0)',
  },
  fonts: fontDetails,
};

export const appDarkTheme: AppTheme = {
  dark: true,
  colors: {
    primary: '#0A84FF',
    secondary: '#30D158',
    tertiary: '#FFFFFF',
    background: '#000000',
    backgroundColors: {
      primary: '#F2F2F7',
      secondary: '#F2F2F7',
      tertiary: '#FFFFFF',
    },
    text: '#FFFFFF',
    textColors: {
      primary: '#FFFFFF',
      secondary: '#7D7D7D',
    },
    card: '#1C1C1E',
    border: '#3A3A3C',
    notification: '#FF453A',
    icon: '#FFFFFF',
    link: '#0A84FF',
    transparent: 'rgba(0, 0, 0, 0)',
  },
  fonts: fontDetails,
};
