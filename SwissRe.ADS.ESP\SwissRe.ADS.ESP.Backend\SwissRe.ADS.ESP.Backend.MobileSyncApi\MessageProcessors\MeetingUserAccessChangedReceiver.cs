﻿using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate.ServiceBusMessages;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ServiceBus.Receiving;
using SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Common;
using System.Text.Json;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.MessageProcessors
{
    public class MeetingUserAccessChangedReceiver(UnitOfWork unitOfWork, IContainerAuthorizationService containerAuthorizationService) : Receiver<MeetingUserAccessChangedMessage>
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IContainerAuthorizationService _containerAuthorizationService = containerAuthorizationService;

        protected override void Configure(ReceiverConfig config)
        {
            config.MaxConcurrentCalls = 1; //TODO: test with multiple receivers, should be fine as they are scoped
        }

        protected override async Task<IHandleMessageResult<MeetingUserAccessChangedMessage>> HandleMessageAsync(HandleMessageArgs<MeetingUserAccessChangedMessage> args)
        {
            var userDeviceSync = await _unitOfWork.Repository<DeviceSyncFeedTracker>().GetFirstOrNullAsync(x => x.UserId == args.Message.UserId && x.EntityType == EspSyncEntityType.Meetings, true);

            if (userDeviceSync is null)
            {
                // If the user does not have a sync tracker, we cannot process the access change
                return Complete();
            }

            //check if user has access to the meeting itself
            var hasUserAccessToMeetingContainer = await _containerAuthorizationService.CanUserViewContainerAsync(args.Message.MeetingId, args.Message.UserId, AgendaTypeEnum.MeetingAgenda);

            if (hasUserAccessToMeetingContainer == false)
                await ProcessUserLostAccessToMeeting(userDeviceSync, args.Message.MeetingId, args.Message.UserId).ConfigureAwait(false);
            else
                await ProcessUsersAccessWasModified(userDeviceSync, args.Message.MeetingId, args.Message.UserId).ConfigureAwait(false);

            await _unitOfWork.CommitTransactionAsync();

            return Complete();
        }


        private async Task ProcessUserLostAccessToMeeting(DeviceSyncFeedTracker userDeviceSync, Guid meetingId, string SrUserId)
        {
            var meetingLastModifiedOn = await _unitOfWork.Repository<MeetingContainer>()
                                            .GetFirstOrNullAsync(meetings => meetings
                                                    .Where(m => m.Id == meetingId)
                                                    .Select(x => x.LastUpdatedOnUTC)
                                            );

            userDeviceSync.UpsertUserLostAccessToEntity(meetingId, DateTime.UtcNow);
        }

        private async Task ProcessUsersAccessWasModified(DeviceSyncFeedTracker userDeviceSync, Guid meetingId, string SrUserId)
        {
            var userMeetingData = await _unitOfWork.Repository<MeetingContainer>()
            .GetFirstOrNullAsync(meetings =>
                                 meetings.Where(m => m.Id == meetingId)
            .Select(MeetingContainerEntityFrameworkExtensions.MeetingContainerProjection(SrUserId)));

            if (userMeetingData is null)
            {
                //TODO: add logging here instead of exception
                throw new Exception("Meeting data for the user was not found. This should never happend as user was actually taken from the meeting itself");
            }

            var stringifiedMeetingData = JsonSerializer.Serialize(userMeetingData, SharedJsonSerializerSettings.Shared_Json_Serializer_Options);
            userDeviceSync.UpsertPendingChange(meetingId, userMeetingData.LastUpdatedOn, stringifiedMeetingData);
        }

    }
}
