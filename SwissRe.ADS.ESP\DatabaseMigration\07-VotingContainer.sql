-- VotingsContainer table
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."VotingsContainer") <> 0 THEN
        RAISE NOTICE 'Delete FROM espv2.VotingsContainer';
        DELETE FROM espv2."VotingsContainer";
    END IF;
END $$;

-- insert dummy container
INSERT INTO espv2."VotingsContainer" (
    "Id"
    ,"DomainId"
    ,"ORIGINAL_DB_ID"
    ,"Name"
    ,"Description"
    ,"State"
    ,"DefaultVotingOptions"
    ,"IncludeGuestRole"
    ,"LastUpdatedOnUTC"
)
VALUES (
    '00000000-0000-0000-0000-000000000000'
    ,'00000000-0000-0000-0000-000000000000'
    ,0
    ,'Dummy Container name'
    ,'Dummy Container description'
    ,'Disabled'
    ,'{}'
    ,FALSE
    ,'1970-01-01 00:00:00'
);

-- Containers

INSERT INTO espv2."VotingsContainer" (
    "Id"
    ,"DomainId"
    ,"ORIGINAL_DB_ID"
    ,"Name"
    ,"Description"
    ,"State"
    ,"DefaultVotingOptions"
    ,"IncludeGuestRole"
    ,"LastUpdatedOnUTC"
)
SELECT
    gen_random_uuid() AS Id
    ,(SELECT "Id" FROM espv2."Domains" where "ORIGINAL_DB_ID" = m.type_tid) as DomainId
    ,m.meeting_tid AS ORIGINAL_DB_ID
    ,COALESCE(m.name, '--NAME MUST NOT BE EMPTY!--') AS "Name"
    ,m.description AS "Description"
    ,(SELECT nme FROM esp.t_upload_status us WHERE us.upload_status_tid = m.status_tid) AS "State"
    ,to_json(string_to_array(q.options , ',')) as DefaultVotingOptions
    ,FALSE AS IncludeGuestRole
    ,COALESCE(m.update_time, '1970-01-01 00:00:00') AS "LastUpdatedOnUTC"
    -- ,m.time_start as StartDate
    -- TODO: ask where is end time? equivalent to time_start
    -- is it fixed time period?
FROM
    esp.t_mp_meeting m
INNER JOIN 
    esp.t_mp_agenda ma ON ma.meeting_tid = m.meeting_tid
INNER JOIN 
    esp.t_cr_mp_link ml ON ml.meeting_item_tid = ma.agenda_tid
-- meeting_item_tid is agenda_tid in esp.t_mp_agenda
INNER JOIN 
    esp.t_cr_question q ON ml.question_tid = q.question_tid
WHERE
    m.item_type = 'CR';
