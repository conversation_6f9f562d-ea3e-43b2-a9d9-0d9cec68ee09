import { Injectable } from '@angular/core';
import { shareReplay } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { AppNavigation, AppNavigationItem } from './model';

/**
 * Setup your navigation items here.
 *
 * Creates and provides the app navigation model as an observable.
 */
@Injectable({
  providedIn: 'root'
})
export class AppNavigationConfig {
  readonly appNavigation$ = this.create().pipe(shareReplay(1));

  private create(): Observable<AppNavigation> {
    // READ ME! - If you want to make your navigation dynamic, just base your observable on something (i.e. on this.currentUserService.currentUser$ to show/hide items based on user roles). A more detailed version of this comment is in AppHeaderConfig.

    // TODO: Get user role and see if display admin tools

    const items: AppNavigationItem[] = [
      {
        type: 'action',
        key: 'meetings',
        title: 'Meetings',
        icon: 'calendar_today',
        visible: true,
        action: {
          type: 'routerLink',
          commands: ['/meetings']
        }
      },
      {
        type: 'action',
        key: 'documents',
        title: 'Documents',
        icon: 'insert_drive_file',
        visible: true,
        action: {
          type: 'routerLink',
          commands: ['/documents']
        }
      },
      {
        type: 'action',
        key: 'feedback',
        title: 'Feedback',
        icon: 'message',
        visible: true,
        action: {
          type: 'routerLink',
          commands: ['/feedback']
        }
      }
    ];

    return of({ items });
  }
}
