﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate
{
    public class DeviceSyncFeedTracker : GuidEntity, IAggregateRoot
    {
        private const int MaxBatchSize = 50;

        public string UserId { get; private set; } = null!;

        public string DeviceId { get; private set; } = null!;

        /// <summary>
        /// Gets the type of entity this sync tracker is associated with (e.g., Meetings, Votings, Documents, Annotations).
        /// </summary>
        public EspSyncEntityType EntityType { get; private set; }

        /// <summary>
        /// Gets the timestamp of the last successful synchronization confirmed by the client device.
        /// </summary>
        public DateTime? LastSuccessfulSync { get; private set; }

        /// <summary>
        /// Gets the timestamp of the last time the client device requested a new batch of items for synchronization.
        /// </summary>
        public DateTime LastSyncRequestedAt { get; private set; }

        /// <summary>
        /// Gets the list of items that have changed and are pending download by the client device.
        /// </summary>
        public List<EspChangeEntry> PendingChangedItems { get; private set; } = [];

        /// <summary>
        /// Gets the current batch of items waiting to be downloaded and processed by the client device.
        /// </summary>
        public List<EspChangeEntry> BatchedItems { get; private set; } = [];

        /// <summary>
        /// Gets the list of items that have been successfully downloaded and confirmed by the client device.
        /// This is used for audit purposes and is not auto-included in queries.
        /// </summary>
        public List<EspSynchronizedChange> SyncedItems { get; private set; } = [];

        private DeviceSyncFeedTracker() { }

        public static DeviceSyncFeedTracker Create(string userId, string deviceId, EspSyncEntityType entityType, List<EspChangeEntry>? initialData = null)
        {
            Guard.Against.NullOrWhiteSpace(userId, nameof(userId), "UserId cannot be null or empty.");
            Guard.Against.NullOrWhiteSpace(deviceId, nameof(deviceId), "DeviceId cannot be null or empty.");

            return new DeviceSyncFeedTracker
            {
                UserId = userId,
                DeviceId = deviceId,
                EntityType = entityType,
                LastSyncRequestedAt = DateTime.UtcNow,
                PendingChangedItems = initialData is null ? new() : initialData
            };
        }

        /// <summary>
        /// Attempts to generate a new batch of items for synchronization by moving up to <see cref="MaxBatchSize"/> items
        /// from <see cref="PendingChangedItems"/> to <see cref="BatchedItems"/>, if no batch is currently pending.
        /// Updates the <see cref="LastSyncRequestedAt"/> timestamp.
        /// </summary>
        public void TryGenerateNewBatch()
        {
            LastSyncRequestedAt = DateTime.UtcNow;

            if (BatchedItems.Count > 0 || PendingChangedItems.Count == 0) return;

            //take the next batch from pending changes and convert it to batched items
            var batch = PendingChangedItems.Take(MaxBatchSize).ToList();
            PendingChangedItems.RemoveRange(0, batch.Count);
            foreach (var item in batch)
                BatchedItems.Add(item);
        }

        /// <summary>
        /// Confirms that the current batch has been successfully synchronized by the mobile client.
        /// Moves all items from <see cref="BatchedItems"/> to <see cref="SyncedItems"/>, clears the batch,
        /// and updates the <see cref="LastSuccessfulSync"/> timestamp.
        /// </summary>
        public void ConfirmSyncSuccessByMobileClient()
        {
            LastSuccessfulSync = DateTime.UtcNow;

            //move batched items to SyncedItems as they were succesfully downloaded and processed by the client device
            foreach (var item in BatchedItems)
            {
                if (item.UserLostAccess)
                    SyncedItems.Add(EspSynchronizedChange.CreateUserLostAccess(Id, item.EntityId, item.UpdatedOn));
                else
                    SyncedItems.Add(EspSynchronizedChange.CreateWithValue(Id, item.EntityId, item.UpdatedOn, item.ValueAsJson));
            }

            //clear the batch and prepare it for the next run
            BatchedItems.Clear();
        }

        /// <summary>
        /// Adds or updates a pending change entry for the specified entity in <see cref="PendingChangedItems"/>.
        /// If the entity already exists, updates its value and last updated timestamp; otherwise, adds a new entry.
        /// </summary>
        /// <param name="entityId">The unique identifier of the entity.</param>
        /// <param name="entityLastUpdatedOn">The last updated timestamp of the entity.</param>
        /// <param name="entityValueAsJson">The serialized value of the entity.</param>
        public void UpsertPendingChange(Guid entityId, DateTime entityLastUpdatedOn, string entityValueAsJson)
        {
            var existingEntry = PendingChangedItems.FirstOrDefault(x => x.EntityId == entityId);
            if (existingEntry != null)
                existingEntry.EntityValueChanged(entityValueAsJson, entityLastUpdatedOn);
            else
                PendingChangedItems.Add(EspChangeEntry.CreateWithValue(entityId, entityLastUpdatedOn, entityValueAsJson));
        }

        /// <summary>
        /// Adds or updates a pending change entry to indicate that the user has lost access to the specified entity.
        /// If the entity already exists in <see cref="PendingChangedItems"/>, marks it as access removed; otherwise, adds a new entry.
        /// </summary>
        /// <param name="entityId">The unique identifier of the entity.</param>
        /// <param name="entityLastUpdatedOn">The last updated timestamp of the entity.</param>
        public void UpsertUserLostAccessToEntity(Guid entityId, DateTime entityLastUpdatedOn)
        {
            var existingEntry = PendingChangedItems.FirstOrDefault(x => x.EntityId == entityId);
            if (existingEntry != null)
                existingEntry.UserAccessRemovedToEntity();
            else
                PendingChangedItems.Add(EspChangeEntry.CreateUserLostAccess(entityId, entityLastUpdatedOn));

        }
    }

    //Integration event on meeting update > get all users belonging to user agendas and update their sync entity data > either add it into pending changes if not existing or modify Updatedon column on the existing entity
}
