﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class VotingAnswerConfiguration : IEntityTypeConfiguration<VotingAnswer>
    {
        public void Configure(EntityTypeBuilder<VotingAnswer> builder)
        {
            builder.ToTable("VotingAnswers");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion();

            builder.Property(x => x.VotingQuestionId).IsRequired();
            builder.Property(x => x.AnswerEncrypted).HasMaxLength(1024);
            builder.Property(x => x.CommentEncrypted).HasMaxLength(5012);
            builder.Property(x => x.CreatedByUserId).IsRequired().HasMaxLength(256);
            builder.Property(x => x.OnBehalfOfUserId).IsRequired(false).HasMaxLength(256);

            builder.Property(x => x.CreatedOn).IsRequired();
            builder.Property(x => x.CreatedByUserId).IsRequired().HasMaxLength(128);

            builder.HasOne(x => x.OnBehalfOfDocumentMetadata)
                .WithOne()
                .HasForeignKey<VotingAnswer>(x => x.OnBehalfOfDocumenMetadatatId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasIndex(x => x.VotingQuestionId);
            builder.HasOne<VotingQuestion>()
                .WithMany()
                .HasForeignKey(x => x.VotingQuestionId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne<User>()
                .WithMany()
                .HasForeignKey(x => x.CreatedByUserId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne<User>()
                .WithMany()
                .HasForeignKey(x => x.OnBehalfOfUserId)
                .OnDelete(DeleteBehavior.NoAction);

        }
    }
}
