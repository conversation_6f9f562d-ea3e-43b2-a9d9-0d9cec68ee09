import { Component, inject, input } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { AppNavigationCategoryItemComponent } from './app-navigation-category-item.component';
import { AppNavigationActionItemComponent } from './app-navigation-action-item.component';
import { AppNavigationItem } from '../model';

@Component({
  selector: 'app-navigation-item-list',
  imports: [CommonModule, RouterModule, MatIconModule, AppNavigationCategoryItemComponent, AppNavigationActionItemComponent],
  template: `
    @for (item of items(); track item.key) {
      @if (item.visible !== false) {
        @if (item.type === 'category') {
          <app-navigation-category-item [item]="item"></app-navigation-category-item>
        } @else {
          <app-navigation-action-item [item]="item"></app-navigation-action-item>
        }
      }
    }
  `,
  styles: `
    :host {
      display: flex;
      flex-direction: column;
      gap: 16px;
      font-weight: normal;
      font-size: 1rem;
      user-select: none;
      &.is-root {
        gap: 0;
        font-weight: bold;
        font-size: 1.125rem;
      }
    }
  `,
  host: {
    '[class.is-root]': 'isRoot'
  }
})
export class AppNavigationItemListComponent {
  readonly items = input.required<AppNavigationItem[]>();
  isRoot = !inject(AppNavigationItemListComponent, { skipSelf: true, optional: true });
}
