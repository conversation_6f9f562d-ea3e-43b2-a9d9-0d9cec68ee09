using SwissRe.ADS.ESP.Backend.Application;
using SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.MeetingsAggregate;
using SwissRe.ADS.MinimalApi.Endpoints;

public class MeetingV1RouteGroup : IRouteGroup
{
    private static int RouteVersion = 1;

    public static IEndpointRouteBuilder? BuildRoute([EndpointRouteBuilder<ApiRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
    {
        
        var routePrefix = MeetingsAggregateRouteBase.RoutePrefix + RouteVersion;
        return builder.MapGroup(routePrefix).WithTags(routePrefix);
    }
}