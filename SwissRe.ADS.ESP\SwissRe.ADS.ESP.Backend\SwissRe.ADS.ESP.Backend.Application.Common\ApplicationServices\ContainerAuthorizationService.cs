﻿using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using System.ComponentModel;

namespace SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices
{
    public interface IContainerAuthorizationService
    {
        Task<bool> CanUserCreateContainerAsync(Guid domainId);
        Task<bool> CanUserViewContainerAsync(Guid containerId, string SrUserId, AgendaTypeEnum agendaType);
    }

    public class ContainerAuthorizationService(UnitOfWork unitOfWork, ICurrentUser currentUser) : IContainerAuthorizationService
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;

        public async Task<bool> CanUserCreateContainerAsync(Guid domainId)
        {
            var domain = await GetDomain(domainId);

            return domain.DomainRoles
                .Where(r => r.RoleType == DomainRoleTypeEnum.Admin)
                .Any(x => _currentUser.EspRoles.Contains(x.RoleId));
        }

        public async Task<bool> CanUserViewContainerAsync(Guid containerId, string SrUserId, AgendaTypeEnum agendaType)
        {
            var container = await GetContainerPermissionsAsync(containerId, SrUserId, agendaType);
            if (container is null)
                return false;

            if (container.numberOfAgendas != container.Permissions.Count())
                return false;

            if (container.Permissions.All(x => x.CurrentPermission == AgendaPermissionEnum.Deny))
                return false;

            return true;
        }
                
        private async Task<EspDomain> GetDomain(Guid domainId)
        {
            var domain = await _unitOfWork.Repository<EspDomain>().GetAsync(domainId);

            if (domain == null)
                throw new AggregateRootNotFoundException<EspDomain>();

            return domain;
        }

        private record ContainerWithPermissions(ContainerStateEnum ContainerState, int numberOfAgendas, IEnumerable<AgendaPermission> Permissions);
        private async Task<ContainerWithPermissions?> GetContainerPermissionsAsync(Guid containerId, string SrUserId, AgendaTypeEnum agendaType)
        {
            return agendaType switch
            {
                AgendaTypeEnum.MeetingAgenda =>
                    await _unitOfWork.Repository<MeetingContainer>()
                        .GetFirstOrNullAsync(containers =>
                            containers.Where(x => x.Id == containerId)
                                          .Select(x => new ContainerWithPermissions(x.State, x.Agendas.Count,
                                              x.Agendas.SelectMany(o => o.Permissions
                                                    .Where(p => p.UserId == SrUserId)
                                                    )
                                                )
                                              )
                                          ),

                AgendaTypeEnum.VotingAgenda =>
                    await _unitOfWork.Repository<VotingContainer>()
                        .GetFirstOrNullAsync(containers =>
                            containers.Where(x => x.Id == containerId)
                                        .Select(x => new ContainerWithPermissions(x.State, x.Agendas.Count,
                                            x.Agendas.SelectMany(o => o.Permissions
                                                    .Where(p => p.UserId == SrUserId)
                                                    )
                                                )
                                            )
                                        ),

                AgendaTypeEnum.DocumentAgenda =>
                    await _unitOfWork.Repository<DocumentContainer>()
                        .GetFirstOrNullAsync(containers =>
                            containers.Where(x => x.Id == containerId)
                                        .Select(x => new ContainerWithPermissions(x.State, x.Agendas.Count,
                                            x.Agendas.SelectMany(o => o.Permissions
                                                    .Where(p => p.UserId == SrUserId)
                                                    )
                                                )
                                            )
                                        ),

                _ => null
            };
        }
    }
}
