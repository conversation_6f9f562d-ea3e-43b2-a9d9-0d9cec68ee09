﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    internal class DocumentContainerConfiguration : IEntityTypeConfiguration<DocumentContainer>
    {
        public void Configure(EntityTypeBuilder<DocumentContainer> builder)
        {
            builder.ToTable("DocumentsContainer");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion(); // All aggregate roots should have a row version.

            builder.Property(x => x.Name).IsRequired().HasMaxLength(512);
            builder.Property(x => x.Description).HasMaxLength(5092);
            builder.Property(x => x.State).HasConversion<string>().<PERSON><PERSON>ax<PERSON>ength(256);

            builder.HasMany(x => x.Agendas)
                .WithOne()
                .HasForeignKey(x => x.ParentContainerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(x => x.Domain)
                .WithMany()
                .HasForeignKey(x => x.DomainId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Navigation(x => x.Agendas).AutoInclude();
        }
    }
}
