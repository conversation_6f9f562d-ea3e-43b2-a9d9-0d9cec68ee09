﻿using Npgsql;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SwissRe.ADS.ESP.DataMigration
{
    public class ProgramClass
    {
        public record AnnotationRecord(long AnnotatedFile_ORIGINAL_DB_ID, string Annotation);

        public static void Main()
        {
            // Read JSON text from file
            var jsonFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Sample", "AnnotationSample.json");
            string jsonText = File.Exists(jsonFilePath) ? File.ReadAllText(jsonFilePath) : string.Empty;

            string dbJsonText =  ReadAnnotationsJsonFromDb(142347483) ?? string.Empty; //; 136614362
            AnnotationRecord? annotationRecord = new AnnotationRecord(0, jsonText);

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var anno = JsonSerializer.Deserialize<OldAnnotationDocument>(annotationRecord?.Annotation ?? string.Empty, options);
            Console.WriteLine($"AnnotatedFile: {annotationRecord}");
        }

        private static string? ReadAnnotationsJsonFromDb(int fileId)
        {
            Console.WriteLine("Connected via EF Core!");
            var dbConnector = new DbConnector();
            var npgsqlConnection = dbConnector.NpgsqlConnection;
            npgsqlConnection.Open();

            var queryString = "SELECT f.file_tid as AnnotatedFile_ORIGINAL_DB_ID," +
                "convert_from(pkg_crypto.decrypt_blob(document), 'UTF8') as Annotation " +
                "FROM esp.t_file f join esp.t_file_rel fr on fr.target_tid = f.file_tid" +
                $" where fr.rel_tid = -101 and f.file_tid={fileId}";
            using var cmd = new NpgsqlCommand(queryString, npgsqlConnection);
            using var reader = cmd.ExecuteReader();
            AnnotationRecord? annotationRecord = null;
            var annotation = string.Empty;
            if (reader.Read())
            {
                var id = reader.GetInt64(reader.GetOrdinal("AnnotatedFile_ORIGINAL_DB_ID"));
                annotation = reader.IsDBNull(reader.GetOrdinal("Annotation")) ? string.Empty : reader.GetString(reader.GetOrdinal("Annotation"));
                annotationRecord = new AnnotationRecord(id, annotation);
            }
            npgsqlConnection.Close();
            return annotation;
        }
    }

    // Example: Count questions
    //var questionCount = db.Set<VotingQuestion>().Count();
    //Console.WriteLine($"Questions in DB: {questionCount}");


}

