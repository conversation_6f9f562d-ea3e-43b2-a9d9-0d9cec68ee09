﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Persistence;
using Testcontainers.PostgreSql;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using Microsoft.Extensions.Configuration;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;

namespace SwissRe.ADS.ESP.Backend.Jobs.SyncAgendaPermissions.IntegrationTests
{
    public class Tests
    {
        private PostgreSqlContainer _postgresContainer;
        private ServiceProvider _serviceProvider;

        [OneTimeSetUp]
        public async Task GlobalSetup()
        {
            // Load configuration from appsettings.Test.json
            var _configuration = new ConfigurationBuilder()
                .SetBasePath(TestContext.CurrentContext.TestDirectory) // base path for test output
                .AddJsonFile("appsettings.Test.json", optional: true)
                .Build();

            _postgresContainer = new PostgreSqlBuilder()
                .WithImage("postgres:15-alpine")
                .WithDatabase("testdb")
                .WithUsername("postgres")
                .WithPassword("postgres")
                .WithPortBinding(6543, 5432) // host port 6543 → container port 5432
                .WithCleanUp(true)
                .Build();

            await _postgresContainer.StartAsync();

            var services = new ServiceCollection();

            services.AddPersistence(_configuration, null);
            services.AddScoped<UnitOfWork>();
            services.AddScoped<AgendaPermissionSyncService>();

            _serviceProvider = services.BuildServiceProvider();

            var dbContext = _serviceProvider.GetRequiredService<AppDbContext>();
            await dbContext.Database.MigrateAsync();
        }

        [OneTimeTearDown]
        public async Task GlobalTeardown()
        {
            if (_postgresContainer != null)
                await _postgresContainer.DisposeAsync();

            if (_serviceProvider is IDisposable disposable)
                disposable.Dispose();
        }

        [SetUp]
        public async Task TestSetup()
        {
            var dbContext = _serviceProvider.GetRequiredService<AppDbContext>();
            dbContext.Set<UserEspRole>().RemoveRange(dbContext.Set<UserEspRole>()); // Clear users
            dbContext.Set<User>().RemoveRange(dbContext.Set<User>()); // Clear users
            dbContext.Set<EspRole>().RemoveRange(dbContext.Set<EspRole>()); // Clear users

            var unitOfWork = _serviceProvider.GetRequiredService<UnitOfWork>();

            var role = EspRole.Create(Guid.NewGuid(), "TestRole", "Test role for integration tests");
            var user = User.Create("S1BTJG", "<EMAIL>", "Robert Jokl");
            user.AddRole(role.Id);

            unitOfWork.Repository<EspRole>().Insert(role);
            unitOfWork.Repository<User>().Insert(user);

            await unitOfWork.CommitTransactionAsync();
        }

        [Test]
        public async Task UserShouldBeFoundTest()
        {
            var unitOfWork = _serviceProvider.GetRequiredService<UnitOfWork>();

            var user = await unitOfWork.Repository<User>().GetAsync("S1BTJG");

            //assert that user should not be null
            Assert.That(user, Is.Not.Null);
        }

        [Test]
        public void UserShouldNotBeFoundTest()
        {
            var unitOfWork = _serviceProvider.GetRequiredService<UnitOfWork>();
            Assert.ThrowsAsync<AggregateRootNotFoundException<User>>(async () =>
            await unitOfWork.Repository<User>().GetAsync("S1BTJGNOTEXISTING")
            );
        }
    }
}
