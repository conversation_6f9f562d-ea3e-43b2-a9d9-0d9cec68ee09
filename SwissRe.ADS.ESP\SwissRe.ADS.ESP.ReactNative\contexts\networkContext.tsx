// NetworkContext.tsx
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import * as Network from 'expo-network';

export interface NetworkContextType {
  isConnected: boolean | null;
}

// ✅ Create the actual context object
export const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

// ✅ Provider that supplies the context
export const NetworkProvider = ({ children }: { children: ReactNode }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);

  const checkInitialConnection = async () => {
    try {
      const status = await Network.getNetworkStateAsync();
      setIsConnected((status.isConnected && status.isInternetReachable !== false) ?? false);
    } catch (e) {
      console.log('Unexpected error:', e);
      setIsConnected(false);
    }
  };

  useEffect(() => {
    checkInitialConnection();

    const networkSubscription = Network.addNetworkStateListener((status) => {
      setIsConnected((status.isConnected && status.isInternetReachable !== false) ?? false);
    });

    return () => {
      networkSubscription.remove();
    };
  }, []);

  return <NetworkContext.Provider value={{ isConnected }}>{children}</NetworkContext.Provider>;
};

// ✅ Custom hook to access the context
export const useNetwork = () => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};
