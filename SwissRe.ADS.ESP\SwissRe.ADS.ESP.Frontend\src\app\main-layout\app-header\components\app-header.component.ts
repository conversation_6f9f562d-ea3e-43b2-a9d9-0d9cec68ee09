import { Component, computed, inject } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { AppHeaderRightSectionDesktopComponent } from './app-header-right-section-desktop.component';
import { AppHeaderRightSectionMobileComponent } from './app-header-right-section-mobile.component';
import { MatButtonModule } from '@angular/material/button';
import { AppHeaderConfig } from '../config';
import { MediaIfDirective } from '../../../shared/responsive/media-if.directive';
import { Environment } from '../../../shared/environment-enum';
import { MatChipsModule } from '@angular/material/chips';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-header',
  imports: [
    CommonModule,
    MatToolbarModule,
    MatIconModule,
    AppHeaderRightSectionMobileComponent,
    AppHeaderRightSectionDesktopComponent,
    MatButtonModule,
    MediaIfDirective,
    MatChipsModule
  ],
  template: `
    <mat-toolbar color="primary" [ngClass]="{ prod: isProd() }">
      <svg class="logo">
        <use href="/assets/images/logo-sr.svg#root" />
      </svg>

      <div class="app-metadata">
        <span class="app-name">{{ appName() }} </span>
        @if (!isProd()) {
          <mat-chip [disableRipple]="true" class="environment-badge">{{ environmentName() }}</mat-chip>
        }
      </div>

      <span class="spacer"></span>

      <app-header-right-section-desktop *mediaIf="{ from: 'desktop' }"></app-header-right-section-desktop>
      <app-header-right-section-mobile *mediaIf="{ until: 'desktop' }"></app-header-right-section-mobile>
    </mat-toolbar>
  `,
  styles: `
    .logo {
      cursor: pointer;
      margin: 0 16px;
      width: 120px;
      height: 100%;
      fill: white;
    }
      
    .app-metadata {
      display: flex;
      align-items: center;
      gap: 12px;
      .app-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;    
      }
      .environment-badge {
        --mat-chip-label-text-color: white;
        --mat-chip-label-text-size: 12px;
        --mat-chip-container-height: 24px;
        background-color: #2175D3; //blue
      }
    }
    
    .spacer {
        flex: 1;
    }
  `
})
export class AppHeaderComponent {
  private readonly appHeader = toSignal(inject(AppHeaderConfig).appHeader$);

  readonly environmentName = computed(() => this.appHeader()?.environmentName);
  readonly isProd = computed(() => this.appHeader()?.environment === Environment.PROD);
  readonly appName = computed(() => this.appHeader()?.appName || '');
}
