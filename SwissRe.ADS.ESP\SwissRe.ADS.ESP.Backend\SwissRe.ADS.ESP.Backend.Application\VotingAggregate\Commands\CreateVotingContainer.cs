﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using Microsoft.AspNetCore.Http;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAggregate.Commands
{
    public record CreateVotingContainerCommand(string name, string description, Guid domainId, VotingOptions defaultVotingOptions);

    public class CreateVotingContainerEndpoint(UnitOfWork unitOfWork, IContainerAuthorizationService containerPermissionService) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IContainerAuthorizationService _containerPermissionService = containerPermissionService;

        public static void BuildRoute([EndpointRouteBuilder<VotingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
                    builder
                        .MapPost("/containers", (CreateVotingContainerCommand command, CreateVotingContainerEndpoint endpoint) => endpoint.HandleAsync(command))
                        .WithSummary("Create a new voting container.")
                        .WithDescription("Creates a new voting container in the specified domain. Requires permission to create containers in the domain.")
                        .WithAngularName<VotingContainerRouteGroup>("CreateVotingContainer");

        public async Task HandleAsync(CreateVotingContainerCommand command)
        {
            var canCreateContainer = await _containerPermissionService.CanUserCreateContainerAsync(command.domainId);
            if (canCreateContainer == false)
                throw new UnauthorizedAccessException("Current user does not have permission to create a new container in this domain.");

            var repo = _unitOfWork.Repository<VotingContainer>();
            var item = VotingContainer.Create(command.name, command.domainId, DateTime.UtcNow.AddDays(-1), DateTime.UtcNow.AddDays(1), command.defaultVotingOptions, null, command.description);

            repo.Insert(item);

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
