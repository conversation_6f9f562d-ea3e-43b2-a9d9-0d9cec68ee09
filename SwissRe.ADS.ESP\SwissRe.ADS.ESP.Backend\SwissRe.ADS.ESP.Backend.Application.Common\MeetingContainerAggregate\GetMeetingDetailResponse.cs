﻿using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using System.Text.Json.Serialization;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate
{
    /// <summary>
    /// DTO for detailed meeting information including agendas. As it is, Agenda hierarchy is not built by default.
    /// If you want to return actual hierarchical structure of agendas, use <see cref="ReturnMeetingWithAgendaTreeHierachy"/> method.   
    /// </summary>
    public class GetMeetingDetailResponse
    {
        public Guid Id { get; init; }
        public string Name { get; init; } = null!;
        public string? Description { get; init; }
        public ContainerStateEnum State { get; init; }
        public DateTime LastUpdatedOn { get; set; }

        /// <summary>
        /// Entity Framework can only output flat list of meeting agendas. This is a flat representation of all agendas associated with the meeting. In order to output Hierachical version use Agendas property.
        /// </summary>
        [JsonIgnore]
        public IEnumerable<GetMeetingAgendaResponse> FlatAgendas { get; init; } = [];

        /// <summary>
        /// Returns the hierarchical structure of meeting agendas.
        /// </summary>
        public IEnumerable<GetMeetingAgendaResponse> Agendas
        {
            get
            {
                // Avoid multiple enumerations and ToDictionary overhead by using a single pass and Dictionary<Guid, GetMeetingAgendaResponse>
                var dtoLookup = new Dictionary<Guid, GetMeetingAgendaResponse>();
                var rootNodes = new List<GetMeetingAgendaResponse>();

                // Build lookup and clear children to avoid duplicate children if property is accessed multiple times
                foreach (var agenda in FlatAgendas)
                {
                    dtoLookup[agenda.Id] = agenda;
                    agenda.Children?.Clear();
                }

                foreach (var agenda in FlatAgendas)
                {
                    if (agenda.ParentAgendaId.HasValue && dtoLookup.TryGetValue(agenda.ParentAgendaId.Value, out var parentDto))
                    {
                        parentDto.Children.Add(agenda);
                    }
                    else
                    {
                        rootNodes.Add(agenda);
                    }
                }

                return rootNodes;
            }
        }
    }
}
