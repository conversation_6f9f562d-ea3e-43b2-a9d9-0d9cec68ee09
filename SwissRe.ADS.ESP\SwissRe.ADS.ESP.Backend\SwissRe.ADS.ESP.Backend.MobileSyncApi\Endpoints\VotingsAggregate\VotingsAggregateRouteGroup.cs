﻿using SwissRe.ADS.ESP.Backend.Application;
using SwissRe.ADS.MinimalApi.Endpoints;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.VotingsAggregate
{
    public class VotingsAggregateRouteGroup : IRouteGroup
    {
        public static IEndpointRouteBuilder? BuildRoute([EndpointRouteBuilder<ApiRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder.MapGroup("Votings").WithTags("Votings");
    }
}
