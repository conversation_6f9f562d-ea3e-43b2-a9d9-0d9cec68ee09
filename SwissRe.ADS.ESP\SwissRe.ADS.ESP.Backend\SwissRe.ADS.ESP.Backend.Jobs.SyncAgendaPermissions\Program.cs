﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SwissRe.ADS.ESP.Backend.Persistence;
using SwissRe.ADS.Ddd.Events.Dispatching.IntegrationEvents;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;

namespace SwissRe.ADS.ESP.Backend.Jobs.SyncAgendaPermissions
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var host = Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {

                    // Register services
                    //var integrationEventsRegistration = services.AddIntegrationEventDispatching<AppDbContext>(config => config.AddHandlersFrom(typeof(ApiRouteGroup).Assembly));
                    services.AddPersistence(context.Configuration, null);
                    services.AddScoped<DatabaseHelpers>();
                    services.AddScoped<UnitOfWork>();
                    services.AddScoped<AgendaPermissionSyncService>();
                })
                .Build();

            using var scope = host.Services.CreateScope();
            var syncService = scope.ServiceProvider.GetRequiredService<AgendaPermissionSyncService>();

            try
            {
                await syncService.RunAsync();
            }
            catch (Exception ex)
            {
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
                logger.LogError(ex, "An error occurred while syncing users.");
                throw;
            }
        }
    }
}
