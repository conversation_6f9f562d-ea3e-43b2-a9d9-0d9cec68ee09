﻿using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Builder;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using Microsoft.Extensions.Configuration;

namespace SwissRe.ADS.ESP.Backend.Application.AppConfiguration.Queries
{
    public record GetAppConfigurationResponse(string AgGridLicenseKey);
    public class GetAppConfiguration(IConfiguration configuration) : IEndpoint
    {
        private readonly IConfiguration _configuration = configuration;
        public static void BuildRoute([EndpointRouteBuilder<AppConfigurationRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder
                .MapGet("Get", (GetAppConfiguration endpoint) => endpoint.Handle())
                .WithAngularName<AppConfigurationRouteGroup>("Get");

        public GetAppConfigurationResponse Handle()
        {
            var agGridLicenseKey = _configuration["AgGridSettings:LicenseKey"];

            return new GetAppConfigurationResponse(agGridLicenseKey);
        }
    }
}
