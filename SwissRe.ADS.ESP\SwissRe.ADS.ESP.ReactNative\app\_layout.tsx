import { ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Slot } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { appDarkTheme, appLightTheme } from '@/constants/app-theme';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DatabaseProvider, useDatabaseReady } from '@/database/contexts/databaseProvider';
import { SyncJobManager } from '@/syncJobs/SyncJobManager';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

const queryClient = new QueryClient();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <DatabaseProvider>
        <ThemeProvider value={colorScheme === 'dark' ? appDarkTheme : appLightTheme}>
          <MainLayout />
          <StatusBar style="auto" />
        </ThemeProvider>
      </DatabaseProvider>
    </QueryClientProvider>
  );
}

function MainLayout() {
  const isDatabaseReady = useDatabaseReady();

  useEffect(() => {
    if (!isDatabaseReady) return;

    // Initialize sync job manager
    SyncJobManager.getInstance().initializeSyncJobs();
  }, [isDatabaseReady]);

  return <Slot />;
}
