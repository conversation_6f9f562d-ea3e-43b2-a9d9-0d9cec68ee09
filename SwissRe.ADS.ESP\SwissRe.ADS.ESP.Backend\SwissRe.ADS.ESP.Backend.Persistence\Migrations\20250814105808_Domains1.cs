﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class Domains1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Domains_ParentId",
                schema: "espv2",
                table: "Domains");

            migrationBuilder.CreateIndex(
                name: "IX_Domains_ParentId",
                schema: "espv2",
                table: "Domains",
                column: "ParentId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Domains_ParentId",
                schema: "espv2",
                table: "Domains");

            migrationBuilder.CreateIndex(
                name: "IX_Domains_ParentId",
                schema: "espv2",
                table: "Domains",
                column: "ParentId",
                unique: true);
        }
    }
}
