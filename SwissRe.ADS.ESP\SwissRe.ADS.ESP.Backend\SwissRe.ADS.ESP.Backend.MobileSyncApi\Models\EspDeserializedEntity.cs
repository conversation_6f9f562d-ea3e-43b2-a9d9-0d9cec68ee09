﻿namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.Models
{
    public class EspDeserializedEntity<T> where T : class
    {
        /// <summary>
        /// Primary key of the Entity that we are syncinng (DocumentContainer, MeetingContainer, VotingContainer, etc.).
        /// </summary>
        public Guid EntityId { get; private set; }

        /// <summary>
        /// Indicates whether the user has lost access to this entity and it should be deleted on the target device.
        /// </summary>
        public bool UserLostAccess { get; private set; }

        /// <summary>
        /// Deserialized ValueAsJson from the EspChangeEntry.
        /// </summary>
        public T? Item { get; private set; }

        public static EspDeserializedEntity<T> CreateLostAccesss(Guid entityId)
        {
            return new EspDeserializedEntity<T>
            {
                EntityId = entityId,
                UserLostAccess = true,
                Item = null
            };
        }

        public static EspDeserializedEntity<T> Create(Guid entityId, T item)
        {
            return new EspDeserializedEntity<T>
            {
                EntityId = entityId,
                UserLostAccess = false,
                Item = item
            };
        }
    }
}
