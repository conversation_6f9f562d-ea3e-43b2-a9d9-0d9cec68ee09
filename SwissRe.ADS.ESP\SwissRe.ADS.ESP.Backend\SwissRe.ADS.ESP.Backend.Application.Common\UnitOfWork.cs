﻿using Microsoft.Extensions.DependencyInjection;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.Ddd.Events.Dispatching.Abstractions;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels;

namespace SwissRe.ADS.ESP.Backend.Application.Common
{
    public class UnitOfWork(IServiceProvider services, IDbTransaction dbTransaction, IEventsContainerProvider eventsContainerProvider, IEventDispatcher? eventDispatcher = null)
    {
        private readonly IServiceProvider _services = services;
        private readonly IDbTransaction _dbTransaction = dbTransaction;
        private readonly IEventsContainerProvider _eventsContainerProvider = eventsContainerProvider;
        private readonly IEventDispatcher? _eventDispatcher = eventDispatcher;
        private bool _hasTransactionFailed;
        private bool _isCommitingTransaction;
        private readonly object _isCommitingTransactionLock = new();

        /// <summary>
        /// Provides a <see cref="IRepository{TAggregateRoot}"/> implementation.
        /// </summary>
        public IRepository<TAggregateRoot> Repository<TAggregateRoot>()
            where TAggregateRoot : class, IEntity, IAggregateRoot
        {
            return _services.GetRequiredService<IRepository<TAggregateRoot>>();
        }

        /// <summary>
        /// Provides a read/query-side service such as a read model repository or projection handler.
        /// These are not tracked for changes and do not affect domain events or transactions.
        /// </summary>
        /// <typeparam name="TReadModel"></typeparam>
        /// <returns></returns>
        public TReadModel ReadRepository<TReadModel>() where TReadModel : class, IReadModelRepository
        {
            return _services.GetRequiredService<TReadModel>();
        }

        /// <summary>
        /// Executes domain event handlers, schedules integration event handlers and saves changes into the database. All within a single transaction.
        /// </summary>
        public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_hasTransactionFailed)
            {
                throw new InvalidOperationException($"This {GetType().Name} is in failed state. It cannot be used to commit transactions anymore.");
            }

            lock (_isCommitingTransactionLock)
            {
                if (_isCommitingTransaction)
                {
                    throw new InvalidOperationException("A concurrent transaction is already being commited. Make sure you are not calling this inside of a DomainEventHandler.");
                }
                _isCommitingTransaction = true;
            }

            try
            {
                var dispatchIntegrationEvents = _eventDispatcher != null ?
                    await _eventDispatcher.DispatchAsync(_eventsContainerProvider.GetEventsContainers) :
                    () => { };

                await _dbTransaction.SaveChangesAsync(cancellationToken);
                dispatchIntegrationEvents();
            }
            catch
            {
                _hasTransactionFailed = true;
                throw;
            }
            finally
            {
                _isCommitingTransaction = false;
            }
        }
    }
}
