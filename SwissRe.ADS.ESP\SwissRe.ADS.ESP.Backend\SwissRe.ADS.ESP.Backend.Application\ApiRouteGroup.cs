﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;

namespace SwissRe.ADS.ESP.Backend.Application
{
    /// <summary>
    /// The root route group for api endpoints - specifies the 'api' route prefix.
    /// </summary>
    public class ApiRouteGroup : IRouteGroup
    {
        public static IEndpointRouteBuilder? BuildRoute(IEndpointRouteBuilder builder, IServiceProvider services) =>
            builder.MapGroup("api").WithOpenApi();
    }
}
