import js from '@eslint/js';
import react from 'eslint-plugin-react';
import reactNative from 'eslint-plugin-react-native';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import prettierPlugin from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier';
import { defineConfig, globalIgnores } from 'eslint/config';

export default defineConfig([
  globalIgnores(['ios', 'env.ts']),

  {
    ignores: ['node_modules', 'dist', 'build', 'eslint.config.js', '_nswag/nswag.imports.ts', 'expo-env.d.ts', '**/text-size-adjust.js'],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parser: tseslint.parser,
      parserOptions: {
        ecmaFeatures: { jsx: true },
        project: './tsconfig.json',
      },
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
    plugins: {
      react,
      'react-native': reactNative,
      '@typescript-eslint': tseslint.plugin,
      prettier: prettierPlugin,
    },
    rules: {
      // ESLint base rules
      ...js.configs.recommended.rules,
      // React rules
      ...react.configs.recommended.rules,
      // React Native rules
      ...reactNative.configs.all.rules,
      // TypeScript rules
      ...tseslint.configs.recommended.rules,
      // Prettier integration
      ...prettierConfig.rules,
      'prettier/prettier': 'warn',

      // Custom tweaks
      'react/react-in-jsx-scope': 'off',
      'react-native/no-inline-styles': 'warn',
      'react-native/no-unused-styles': 'warn',
      'react/prop-types': 'off',
      'no-unused-vars': [
        'warn', // or "error" if you want strict enforcement
        {
          argsIgnorePattern: '^_', // ignore unused function args starting with _
          ignoreRestSiblings: true, // useful for object rest destructuring
        },
      ],
      'react-native/no-raw-text': [
        'error',
        {
          skip: ['ThemedText'], // add your custom component here
        },
      ],
      'no-restricted-imports': [
        'error',
        {
          paths: [
            {
              name: 'axios',
              message: 'Use API clients from @api instead of importing axios directly.',
            },
            {
              name: '@/syncJobs/jobs/MeetingDataSyncJob',
              message: 'You cannot directly import MeetingDataSyncJob, use SyncJobManager instead.',
            },
            {
              name: '@/database/databaseInitializer',
              message: 'You cannot directly import DatabaseInitializer. This is inner responsibility of database folder',
            },
            {
              name: '@/database/databaseClient',
              message: 'You cannot directly import DatabaseClient. This is inner responsibility of database folder',
            },
          ],
          patterns: [
            {
              group: ['@/api/apiServices'],
              importNames: ['SwaggerIncludeClient'],
              message: 'Do not use swagger include client in the code. It is only for model propagation.',
            },
          ],
        },
      ],
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
  {
    files: ['api/axiosService.ts'],
    rules: {
      // Turn off the restricted import rule for files in the 'api/' directory
      'no-restricted-imports': 'off',
    },
  },
]);
