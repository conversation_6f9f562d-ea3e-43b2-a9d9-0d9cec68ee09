﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Application.Common.DomainServices;
using SwissRe.ADS.ESP.Backend.Application.Common.Encryption;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using Microsoft.AspNetCore.Http;

namespace SwissRe.ADS.ESP.Backend.Application.AnnotationAggregate.Commands
{
    public record AddAnnotationCommand(Guid annotationId, Guid annotationPdftronGeneratedId, string annotationXml);

    public class AddAnnotationEndpoint(UnitOfWork unitOfWork,
        IAgendaAuthorizationService agendaPermissionService,
        IAgendaInfoProviderService agendaInfoProviderService,
        IEspEncryptionService espEncryptionService,
        ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly IAgendaInfoProviderService _agendaInfoProviderService = agendaInfoProviderService;
        private readonly IEspEncryptionService _espEncryptionService = espEncryptionService;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<AnnotationRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
                       builder
            .MapPost("/files/{fileMetadataId}/annotations", (AddAnnotationCommand command, Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType, AddAnnotationEndpoint endpoint) => endpoint.HandleAsync(command, fileMetadataId, fileMetadataType))
            .WithSummary("Add an annotation to a file.")
            .WithDescription("Adds an annotation to the specified file (meeting or document). Requires permission to annotate the file. The annotation XML will be encrypted before storage.")
            .WithAngularName<AnnotationRouteGroup>("AddAnnotation");

        public async Task HandleAsync(AddAnnotationCommand command, Guid fileMetadataId, FileMetadataTypeEnum fileMetadataType)
        {
            var agendaInfo = await _agendaInfoProviderService.GetAgendaInfoAsync(fileMetadataId, fileMetadataType);

            AgendaTypeEnum agendaType = fileMetadataType switch
            {
                FileMetadataTypeEnum.Documents => AgendaTypeEnum.DocumentAgenda,
                FileMetadataTypeEnum.Meetings => AgendaTypeEnum.MeetingAgenda,
                _ => throw new ArgumentException("Invalid file metadata type.", nameof(fileMetadataType))
            };

            var canAddAnnotation = await _agendaPermissionService.CanUserAddAnnotationAsync(agendaInfo.ParentContainerId, agendaInfo.AgendaId, agendaType);
            if (canAddAnnotation == false)
                throw new UnauthorizedAccessException("Current user does not have permission to add annotations to this file.");

            var encryptedAnnotationText = _espEncryptionService.Encrypt(command.annotationXml);
            var annotation = Annotation.Create(fileMetadataId, fileMetadataType, command.annotationPdftronGeneratedId, _currentUser.SrUserId, encryptedAnnotationText, _currentUser.SrUserId);

            _unitOfWork.Repository<Annotation>().Insert(annotation);
            await _unitOfWork.CommitTransactionAsync();
        }
    }
}

