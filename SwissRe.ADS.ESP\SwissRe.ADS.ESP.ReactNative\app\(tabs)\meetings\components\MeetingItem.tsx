import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { MeetingEntity } from '@/database/types/MeetingEntity';
import { useEspAppTheme } from '@/hooks/useEspTheme';
import { router } from 'expo-router';
import { StyleSheet, View, Text, ViewStyle, TouchableOpacity } from 'react-native';

export default function MeetingItem({
  meeting,
  applyTopBorderRadius = false,
  applyBottomBorderRadius = false,
  showBottomBorder = true,
}: {
  meeting: MeetingEntity;
  applyTopBorderRadius?: boolean;
  applyBottomBorderRadius?: boolean;
  showBottomBorder?: boolean;
}) {
  const theme = useEspAppTheme();

  const cardBorderStyle: ViewStyle = {
    borderBottomWidth: 1,
    borderColor: showBottomBorder ? theme.colors.border : theme.colors.transparent,
  };

  return (
    <TouchableOpacity
      onPress={() => router.push(`/meetings/${meeting.id}`)}
      style={[
        styles.container,
        applyTopBorderRadius ? styles.topBorderStyle : {},
        applyBottomBorderRadius ? styles.bottomBorderStyle : {},
        { backgroundColor: theme.colors.backgroundColors.secondary },
      ]}
    >
      <View style={[styles.detailsContainer, cardBorderStyle]}>
        <ThemedText type="default">{meeting.name}</ThemedText>
        <ThemedText type="description">Group Management IT</ThemedText>
      </View>
      <View style={[styles.rightContainer, cardBorderStyle]}>
        <View style={styles.dateAndTypeContainer}>
          <ThemedText type="description">March 15, 2023</ThemedText>
          <View style={styles.locationWrapper}>
            <IconSymbol name="mappin.and.ellipse" size={14} color={theme.colors.link} />
            <ThemedText style={[styles.locationStyle, { color: theme.colors.link }]}>Mythenquai 50/60, Zürich</ThemedText>
          </View>
        </View>
        <View style={styles.rowType}>
          <Text>Meeting</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  bottomBorderStyle: { borderBottomLeftRadius: 8, borderBottomRightRadius: 8 },
  container: {
    flexDirection: 'row',
    paddingLeft: 16,
    paddingRight: 12,
  },
  dateAndTypeContainer: {
    alignItems: 'flex-end',
    borderRightColor: '#8080808C',
    borderRightWidth: 1,
    display: 'flex',
    justifyContent: 'center',
    paddingLeft: 10,
    paddingRight: 10,
  },
  detailsContainer: {
    borderBottomWidth: 1,
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
  },
  locationStyle: {
    fontSize: 13,
  },
  locationWrapper: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  rightContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'space-between',
    paddingBottom: 16,
    paddingTop: 16,
  },
  // eslint-disable-next-line react-native/no-color-literals
  rowType: {
    alignItems: 'center',
    backgroundColor: '#7878801F',
    borderRadius: 6,
    display: 'flex',
    fontSize: 15,
    justifyContent: 'center',
    paddingBottom: 6,
    paddingLeft: 11,
    paddingRight: 11,
    paddingTop: 6,
  },
  topBorderStyle: { borderTopLeftRadius: 8, borderTopRightRadius: 8 },
});
