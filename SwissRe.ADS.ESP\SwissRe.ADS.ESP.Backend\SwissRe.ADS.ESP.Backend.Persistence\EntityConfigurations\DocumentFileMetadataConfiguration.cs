﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate;
using SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class DocumentFileMetadataConfiguration : FileMetadataBaseConfiguration<DocumentFileMetadata>
    {
        public override void Configure(EntityTypeBuilder<DocumentFileMetadata> builder)
        {
            builder.ToTable("DocumentFileMetadatas");
            base.Configure(builder);
        }
    }
}
