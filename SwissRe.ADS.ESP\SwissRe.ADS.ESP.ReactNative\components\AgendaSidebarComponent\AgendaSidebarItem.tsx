import { GetMeetingAgendaResponse } from '@/api/apiServices';
import { View, StyleSheet, Pressable } from 'react-native';
import React, { memo } from 'react';
import { useEspAppTheme } from '@/hooks/useEspTheme';
import { ThemedText } from '../ThemedText';

const AgendaSidebarItem = ({
  agenda,
  onSelect,
  level = 0,
  activeAgendaId,
}: {
  agenda: GetMeetingAgendaResponse;
  onSelect: (_agendaId: string) => void;
  level?: number;
  activeAgendaId: string | null;
}) => {
  const isActive = agenda.id === activeAgendaId;
  const theme = useEspAppTheme();

  const activeItemStyle = isActive ? { borderLeftColor: theme.colors.link } : { borderLeftColor: 'transparent' };
  const activeTextStyle = isActive ? { color: theme.colors.link } : {};

  return (
    <View>
      <Pressable style={[styles.item, { paddingLeft: 16 + level * 20 }, activeItemStyle]} onPress={() => onSelect(agenda.id)}>
        <ThemedText type="default" style={[styles.text, activeTextStyle]}>
          {agenda.name}
        </ThemedText>
      </Pressable>

      {/* Render nested agendas recursively */}
      {agenda.children?.map((child) => (
        <AgendaSidebarItem key={child.id} agenda={child} level={level + 1} onSelect={onSelect} activeAgendaId={activeAgendaId} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  item: {
    borderLeftWidth: 2,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  text: {
    color: '#636773',
    fontSize: 17,
    letterSpacing: 0.43,
  },
});

export default memo(AgendaSidebarItem);
