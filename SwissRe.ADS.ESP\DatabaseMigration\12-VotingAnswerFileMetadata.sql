-- VotingAnswerFileMetadata table
select
    ae.answer_tid as ORIGINAL_DB_ID
    ,f.file_tid AS ORIGINAL_ONBEHALF_FILE_ID
    ,ae.edited_by as CreatedBy -- check this
    ,ae.edited as CreatedOn
    ,ae.user_id as OnBehalfOfUserId
    ,f.filename
    ,f.real_length as "<PERSON><PERSON>"
    ,* 
from 
    esp.t_cr_answer_evidence ae
    JOIN esp.t_file f ON f.file_tid = ae.file_tid
    JOIN esp.t_file_rel fr on fr.file_tid = ae.file_tid
where fr.rel_tid = -100 AND ae.answer_tid = 136282084;

-- FileContents table - document part
select
    f.file_tid as ORIGINAL_DB_ID
    ,f.document as ContentsEncrypted
    ,f.*
from 
    esp.t_cr_answer_evidence ae
    JOIN esp.t_file f ON f.file_tid = ae.file_tid
    JOIN esp.t_file_rel fr on fr.file_tid = ae.file_tid
where fr.rel_tid = -100 AND ae.answer_tid = 136282084;

-- TODO: FileContents table - thumbnail part: update FileContents where ORIGINAL_DB_ID = fr.file_id
select
    fr.file_tid
    ,fr.target_tid as ORIGINAL_DB_ID -- TODO: this will not be used as it's in the same table, same row, with the file contents
--    ,(select document from esp.t_file where file_tid = fr.target_tid) as ThumbnailEncrypted
    ,(select pkg_crypto.decrypt_blob(document) from esp.t_file where file_tid = fr.target_tid) as Thumbnail
from 
    esp.t_cr_answer_evidence ae
    JOIN esp.t_file f ON f.file_tid = ae.file_tid
    JOIN esp.t_file_rel fr on fr.file_tid = ae.file_tid
where fr.rel_tid = -103 AND ae.answer_tid = 136282084

-- update VotingAnswer table TODO: HOW!?

-- the table VotingAnswer will be created by the migration script above, so we can update it now
--select * from esp.t_cr_answer_evidence ae where ae.answer_tid = 136282084
update VotingAnswer va set SupportingDocumentFileMetatdataId = (select Id from VotingAnswerFileMetadata vafm WHERE va.ORIGINAL_ONBEHALF_FILE_ID = vafm.ORIGINAL_DB_ID)

