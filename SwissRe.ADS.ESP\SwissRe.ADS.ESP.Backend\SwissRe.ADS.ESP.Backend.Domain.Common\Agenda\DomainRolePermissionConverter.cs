﻿namespace SwissRe.ADS.ESP.Backend.Domain.Common.Agenda
{
    public static class DomainRolePermissionConverter
    {
        public static AgendaPermissionEnum ToAgendaPermission(this DomainRoleTypeEnum roleType)
        {
            return roleType switch
            {
                DomainRoleTypeEnum.Admin => AgendaPermissionEnum.Manage,
                DomainRoleTypeEnum.Member => AgendaPermissionEnum.Read | AgendaPermissionEnum.Write,
                DomainRoleTypeEnum.Guest => AgendaPermissionEnum.Read,
                _ => throw new ArgumentOutOfRangeException(nameof(roleType), $"Unknown role type: {roleType}")
            };
        }
    }
}
