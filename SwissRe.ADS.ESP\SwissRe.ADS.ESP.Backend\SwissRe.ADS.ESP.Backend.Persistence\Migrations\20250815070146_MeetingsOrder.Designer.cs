﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using SwissRe.ADS.ESP.Backend.Persistence;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250815070146_MeetingsOrder")]
    partial class MeetingsOrder
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("espv2")
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("SwissRe.ADS.Ddd.Events.Dispatching.IntegrationEvents.OutboxedIntegrationEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Event")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TypeCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("OutboxedIntegrationEvents", "events");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate.Annotation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("AnnotationUniqueId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<byte[]>("EncryptedAnnotationXML")
                        .IsRequired()
                        .HasMaxLength(5092)
                        .HasColumnType("bytea");

                    b.Property<Guid>("FileMetadataId")
                        .HasColumnType("uuid");

                    b.Property<string>("FileMetadataType")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("FileMetadataId", "CreatedByUserId");

                    b.ToTable("Annotations", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.DeviceSyncFeedTracker", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("DeviceId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<DateTime?>("LastSuccessfulSync")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastSyncRequestedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "DeviceId", "EntityType")
                        .IsUnique();

                    b.ToTable("DeviceSyncFeeds", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.EspSynchronizedChange", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DeviceSyncFeedTrackerId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("SyncedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("UserLostAccess")
                        .HasColumnType("boolean");

                    b.Property<string>("ValueAsJson")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DeviceSyncFeedTrackerId");

                    b.ToTable("SynchronizedChanges", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentAgenda", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("ParentContainerId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ParentContainerId");

                    b.ToTable("DocumentsAgenda", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentContainer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<Guid>("DomainId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("LastUpdatedOnUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("DomainId");

                    b.ToTable("DocumentsContainer", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentFileMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<Guid>("DocumentAgendaId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FileContentId")
                        .HasColumnType("uuid");

                    b.Property<string>("FileExtension")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid>("FileGroupId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLatest")
                        .HasColumnType("boolean");

                    b.Property<string>("LastModifiedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("NumberOfPages")
                        .HasColumnType("integer");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<int>("Size")
                        .HasColumnType("integer");

                    b.Property<bool>("Supplementary")
                        .HasColumnType("boolean");

                    b.Property<int>("VersionNumber")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("DocumentAgendaId");

                    b.HasIndex("FileContentId")
                        .IsUnique();

                    b.HasIndex("LastModifiedByUserId");

                    b.ToTable("DocumentFileMetadatas", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<int?>("AnnotationsExpirationAfter")
                        .HasColumnType("integer");

                    b.Property<int?>("ChangeStatusToPastAfter")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<int?>("HistoricalDocumentExpirationAfter")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<int?>("iPadDocumentsExpirationAfter")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("Domains", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomainRole", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DomainId")
                        .HasColumnType("uuid");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("RoleType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("DomainId");

                    b.HasIndex("RoleId");

                    b.ToTable("DomainRoles", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspRole", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.HasKey("Id");

                    b.ToTable("Roles", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<Guid>("AzureUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("JobTitle")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("LastModifiedOnUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<long>("SequenceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValueSql("nextval('\"User_SequenceId_seq\"')");

                    b.HasKey("Id");

                    b.HasIndex("AzureUserId");

                    b.ToTable("Users", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.UserEspRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRoles", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.UserSalt", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("Salt")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserSalt", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.FileContent", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<byte[]>("EncryptedContents")
                        .IsRequired()
                        .HasColumnType("bytea");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<byte[]>("ThumbnailImageContents")
                        .IsRequired()
                        .HasColumnType("bytea");

                    b.HasKey("Id");

                    b.ToTable("FileContents", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.JobExecutionAggregate.JobExecutionMetadata", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("JobRunWindowEndUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("LastProcessedSequenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("LastProcessedTimestampUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastSuccessfulRunUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("__JobExecutionMetadata", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingAgenda", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ParentAgendaId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ParentContainerId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ParentAgendaId");

                    b.HasIndex("ParentContainerId");

                    b.ToTable("MeetingsAgenda", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingContainer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<bool>("AutomaticallyExcludeNewUsers")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<Guid>("DomainId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IncludeGuestRole")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("LastUpdatedOnUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("DomainId");

                    b.ToTable("MeetingsContainer", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate.MeetingFileMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<Guid>("FileContentId")
                        .HasColumnType("uuid");

                    b.Property<string>("FileExtension")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid>("FileGroupId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLatest")
                        .HasColumnType("boolean");

                    b.Property<string>("LastModifiedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("MeetingAgendaId")
                        .HasColumnType("uuid");

                    b.Property<int>("NumberOfPages")
                        .HasColumnType("integer");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<int>("Size")
                        .HasColumnType("integer");

                    b.Property<bool>("Supplementary")
                        .HasColumnType("boolean");

                    b.Property<int>("VersionNumber")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("FileContentId")
                        .IsUnique();

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("MeetingAgendaId");

                    b.ToTable("MeetingFileMetadatas", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.VotingAnswer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<byte[]>("AnswerEncrypted")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("bytea");

                    b.Property<byte[]>("CommentEncrypted")
                        .HasMaxLength(5012)
                        .HasColumnType("bytea");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsLatest")
                        .HasColumnType("boolean");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<long?>("ORIGINAL_ONBEHALF_FILE_ID")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("OnBehalfOfDocumenMetadatatId")
                        .HasColumnType("uuid");

                    b.Property<string>("OnBehalfOfUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<Guid>("VotingQuestionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("OnBehalfOfDocumenMetadatatId")
                        .IsUnique();

                    b.HasIndex("OnBehalfOfUserId");

                    b.HasIndex("VotingQuestionId");

                    b.ToTable("VotingAnswers", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.VotingAnswerSupportingDocumentFileMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<Guid>("FileContentId")
                        .HasColumnType("uuid");

                    b.Property<string>("FileExtension")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid>("FileGroupId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLatest")
                        .HasColumnType("boolean");

                    b.Property<string>("LastModifiedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("NumberOfPages")
                        .HasColumnType("integer");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<int>("Size")
                        .HasColumnType("integer");

                    b.Property<bool>("Supplementary")
                        .HasColumnType("boolean");

                    b.Property<int>("VersionNumber")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("FileContentId")
                        .IsUnique();

                    b.HasIndex("LastModifiedByUserId");

                    b.ToTable("VotingAnswerSupportingDocumentFileMetadatas", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingAgenda", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("ParentContainerId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ParentContainerId");

                    b.ToTable("VotingsAgenda", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingContainer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("DefaultVotingOptions")
                        .IsRequired()
                        .HasColumnType("character varying(2056)");

                    b.Property<string>("Description")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<Guid>("DomainId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IncludeGuestRole")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastUpdatedOnUTC")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("DomainId");

                    b.ToTable("VotingsContainer", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("AgendaId")
                        .HasColumnType("uuid");

                    b.Property<string>("AvailableOptions")
                        .IsRequired()
                        .HasColumnType("character varying(2056)");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DescriptionEncrypted")
                        .HasMaxLength(5092)
                        .HasColumnType("character varying(5092)");

                    b.Property<string>("LastModifiedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("LockedForEditing")
                        .HasColumnType("boolean");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasMaxLength(2056)
                        .HasColumnType("character varying(2056)");

                    b.HasKey("Id");

                    b.HasIndex("AgendaId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.ToTable("VotingQuestions", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestionSupportingDocumentFileMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<Guid>("FileContentId")
                        .HasColumnType("uuid");

                    b.Property<string>("FileExtension")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid>("FileGroupId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLatest")
                        .HasColumnType("boolean");

                    b.Property<string>("LastModifiedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("LastModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("NumberOfPages")
                        .HasColumnType("integer");

                    b.Property<long?>("ORIGINAL_DB_ID")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<int>("Size")
                        .HasColumnType("integer");

                    b.Property<bool>("Supplementary")
                        .HasColumnType("boolean");

                    b.Property<int>("VersionNumber")
                        .HasColumnType("integer");

                    b.Property<Guid>("VotingQuestionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("VotingQuestionId1")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("FileContentId")
                        .IsUnique();

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("VotingQuestionId");

                    b.HasIndex("VotingQuestionId1")
                        .HasDatabaseName("IX_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1");

                    b.ToTable("VotingQuestionSupportingDocumentFileMetadatas", "espv2");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate.Annotation", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.DeviceSyncFeedTracker", b =>
                {
                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.EspChangeEntry", "BatchedItems", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .HasColumnType("uuid");

                            b1.Property<Guid>("DeviceSyncFeedTrackerId")
                                .HasColumnType("uuid");

                            b1.Property<Guid>("EntityId")
                                .HasColumnType("uuid");

                            b1.Property<long?>("ORIGINAL_DB_ID")
                                .HasColumnType("bigint");

                            b1.Property<DateTime>("UpdatedOn")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<bool>("UserLostAccess")
                                .HasColumnType("boolean");

                            b1.Property<string>("ValueAsJson")
                                .HasColumnType("text");

                            b1.HasKey("Id");

                            b1.HasIndex("DeviceSyncFeedTrackerId");

                            b1.ToTable("BatchedSyncChangeEntries", "espv2");

                            b1.WithOwner()
                                .HasForeignKey("DeviceSyncFeedTrackerId");
                        });

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.EspChangeEntry", "PendingChangedItems", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .HasColumnType("uuid");

                            b1.Property<Guid>("DeviceSyncFeedTrackerId")
                                .HasColumnType("uuid");

                            b1.Property<Guid>("EntityId")
                                .HasColumnType("uuid");

                            b1.Property<long?>("ORIGINAL_DB_ID")
                                .HasColumnType("bigint");

                            b1.Property<DateTime>("UpdatedOn")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<bool>("UserLostAccess")
                                .HasColumnType("boolean");

                            b1.Property<string>("ValueAsJson")
                                .HasColumnType("text");

                            b1.HasKey("Id");

                            b1.HasIndex("DeviceSyncFeedTrackerId");

                            b1.ToTable("PendingSyncChangeEntries", "espv2");

                            b1.WithOwner()
                                .HasForeignKey("DeviceSyncFeedTrackerId");
                        });

                    b.Navigation("BatchedItems");

                    b.Navigation("PendingChangedItems");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.EspSynchronizedChange", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.DeviceSyncFeedTracker", null)
                        .WithMany("SyncedItems")
                        .HasForeignKey("DeviceSyncFeedTrackerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentAgenda", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentContainer", null)
                        .WithMany("Agendas")
                        .HasForeignKey("ParentContainerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.Common.Agenda.AgendaPermission", "Permissions", b1 =>
                        {
                            b1.Property<Guid>("DocumentAgendaId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<int>("CurrentPermission")
                                .HasColumnType("integer");

                            b1.Property<int>("InheritedPermission")
                                .HasColumnType("integer");

                            b1.Property<string>("UserId")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("DocumentAgendaId", "__synthesizedOrdinal");

                            b1.ToTable("DocumentsAgenda", "espv2");

                            b1.ToJson("Permissions");

                            b1.WithOwner()
                                .HasForeignKey("DocumentAgendaId");
                        });

                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentContainer", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", "Domain")
                        .WithMany()
                        .HasForeignKey("DomainId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired();

                    b.Navigation("Domain");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentFileMetadata", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentAgenda", null)
                        .WithMany("Files")
                        .HasForeignKey("DocumentAgendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.FileContent", null)
                        .WithOne()
                        .HasForeignKey("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentFileMetadata", "FileContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata.ReadStatus", "ReadStatuses", b1 =>
                        {
                            b1.Property<Guid>("DocumentFileMetadataId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<bool>("IsRead")
                                .HasColumnType("boolean");

                            b1.Property<string>("UserId")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("DocumentFileMetadataId", "__synthesizedOrdinal");

                            b1.ToTable("DocumentFileMetadatas", "espv2");

                            b1.ToJson("ReadStatuses");

                            b1.WithOwner()
                                .HasForeignKey("DocumentFileMetadataId");
                        });

                    b.Navigation("ReadStatuses");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", "Parent")
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomainRole", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", "Domain")
                        .WithMany("DomainRoles")
                        .HasForeignKey("DomainId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspRole", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Domain");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.UserEspRole", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingAgenda", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingAgenda", null)
                        .WithMany()
                        .HasForeignKey("ParentAgendaId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingContainer", null)
                        .WithMany("Agendas")
                        .HasForeignKey("ParentContainerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.Common.Agenda.AgendaPermission", "Permissions", b1 =>
                        {
                            b1.Property<Guid>("MeetingAgendaId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<int>("CurrentPermission")
                                .HasColumnType("integer");

                            b1.Property<int>("InheritedPermission")
                                .HasColumnType("integer");

                            b1.Property<string>("UserId")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("MeetingAgendaId", "__synthesizedOrdinal");

                            b1.ToTable("MeetingsAgenda", "espv2");

                            b1.ToJson("Permissions");

                            b1.WithOwner()
                                .HasForeignKey("MeetingAgendaId");
                        });

                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingContainer", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", "Domain")
                        .WithMany()
                        .HasForeignKey("DomainId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired();

                    b.Navigation("Domain");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate.MeetingFileMetadata", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.FileContent", null)
                        .WithOne()
                        .HasForeignKey("SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate.MeetingFileMetadata", "FileContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingAgenda", null)
                        .WithMany("Files")
                        .HasForeignKey("MeetingAgendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata.ReadStatus", "ReadStatuses", b1 =>
                        {
                            b1.Property<Guid>("MeetingFileMetadataId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<bool>("IsRead")
                                .HasColumnType("boolean");

                            b1.Property<string>("UserId")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("MeetingFileMetadataId", "__synthesizedOrdinal");

                            b1.ToTable("MeetingFileMetadatas", "espv2");

                            b1.ToJson("ReadStatuses");

                            b1.WithOwner()
                                .HasForeignKey("MeetingFileMetadataId");
                        });

                    b.Navigation("ReadStatuses");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.VotingAnswer", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.VotingAnswerSupportingDocumentFileMetadata", "OnBehalfOfDocumentMetadata")
                        .WithOne()
                        .HasForeignKey("SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.VotingAnswer", "OnBehalfOfDocumenMetadatatId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("OnBehalfOfUserId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestion", null)
                        .WithMany()
                        .HasForeignKey("VotingQuestionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("OnBehalfOfDocumentMetadata");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.VotingAnswerSupportingDocumentFileMetadata", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.FileContent", null)
                        .WithOne()
                        .HasForeignKey("SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.VotingAnswerSupportingDocumentFileMetadata", "FileContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata.ReadStatus", "ReadStatuses", b1 =>
                        {
                            b1.Property<Guid>("VotingAnswerSupportingDocumentFileMetadataId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<bool>("IsRead")
                                .HasColumnType("boolean");

                            b1.Property<string>("UserId")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("VotingAnswerSupportingDocumentFileMetadataId", "__synthesizedOrdinal");

                            b1.ToTable("VotingAnswerSupportingDocumentFileMetadatas", "espv2");

                            b1.ToJson("ReadStatuses");

                            b1.WithOwner()
                                .HasForeignKey("VotingAnswerSupportingDocumentFileMetadataId");
                        });

                    b.Navigation("ReadStatuses");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingAgenda", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingContainer", null)
                        .WithMany("Agendas")
                        .HasForeignKey("ParentContainerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.Common.Agenda.AgendaPermission", "Permissions", b1 =>
                        {
                            b1.Property<Guid>("VotingAgendaId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<int>("CurrentPermission")
                                .HasColumnType("integer");

                            b1.Property<int>("InheritedPermission")
                                .HasColumnType("integer");

                            b1.Property<string>("UserId")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("VotingAgendaId", "__synthesizedOrdinal");

                            b1.ToTable("VotingsAgenda", "espv2");

                            b1.ToJson("Permissions");

                            b1.WithOwner()
                                .HasForeignKey("VotingAgendaId");
                        });

                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingContainer", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", "Domain")
                        .WithMany()
                        .HasForeignKey("DomainId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired();

                    b.Navigation("Domain");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestion", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingAgenda", null)
                        .WithMany("VotingQuestions")
                        .HasForeignKey("AgendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestionSupportingDocumentFileMetadata", b =>
                {
                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.FileContent", null)
                        .WithOne()
                        .HasForeignKey("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestionSupportingDocumentFileMetadata", "FileContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", null)
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestion", null)
                        .WithMany()
                        .HasForeignKey("VotingQuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestion", null)
                        .WithMany("SupportingDocuments")
                        .HasForeignKey("VotingQuestionId1")
                        .HasConstraintName("FK_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1");

                    b.OwnsMany("SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata.ReadStatus", "ReadStatuses", b1 =>
                        {
                            b1.Property<Guid>("VotingQuestionSupportingDocumentFileMetadataId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<bool>("IsRead")
                                .HasColumnType("boolean");

                            b1.Property<string>("UserId")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("VotingQuestionSupportingDocumentFileMetadataId", "__synthesizedOrdinal");

                            b1.ToTable("VotingQuestionSupportingDocumentFileMetadatas", "espv2");

                            b1.ToJson("ReadStatuses");

                            b1.WithOwner()
                                .HasForeignKey("VotingQuestionSupportingDocumentFileMetadataId");
                        });

                    b.Navigation("ReadStatuses");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate.DeviceSyncFeedTracker", b =>
                {
                    b.Navigation("SyncedItems");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentAgenda", b =>
                {
                    b.Navigation("Files");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate.DocumentContainer", b =>
                {
                    b.Navigation("Agendas");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.EspDomain", b =>
                {
                    b.Navigation("DomainRoles");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.DomainAggregate.User", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingAgenda", b =>
                {
                    b.Navigation("Files");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate.MeetingContainer", b =>
                {
                    b.Navigation("Agendas");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingAgenda", b =>
                {
                    b.Navigation("VotingQuestions");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingContainer", b =>
                {
                    b.Navigation("Agendas");
                });

            modelBuilder.Entity("SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate.VotingQuestion", b =>
                {
                    b.Navigation("SupportingDocuments");
                });
#pragma warning restore 612, 618
        }
    }
}
