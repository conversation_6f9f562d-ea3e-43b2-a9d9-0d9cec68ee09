-- Users Table
-- CHECK: There are many users without email
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."Users") <> 0 THEN
        RAISE NOTICE 'Delete from espv2.Users';
        DELETE FROM espv2."Users";
    END IF;
END $$;

INSERT INTO espv2."Users" (
    "Id"
    ,"AzureUserId"
    ,"Email"
    ,"FullName"
    ,"IsActive"
    ,"JobTitle"
    ,"LastModifiedOnUTC"
--  ,"SequenceId"
    ,"ORIGINAL_DB_ID"
)
select  
    user_id AS Id
    ,'00000000-0000-0000-0000-000000000000' AS AzureUserId
    ,(CASE WHEN email_address IS NULL THEN 'NONE' ELSE email_address END) AS Email
    ,full_name AS FullName
    ,(CASE WHEN life_cycle_state = 'ENABLED' THEN true else false END) AS IsActive
    ,job_title AS JobTitle
    ,modified_on AS LastModifiedOnUTC
    ,0 AS ORIGINAL_DB_ID
from esp.t_sr_user

--select distinct life_cycle_state from esp.t_sr_user
-- select * from esp.t_sr_user
-- select * from espv2."Users"