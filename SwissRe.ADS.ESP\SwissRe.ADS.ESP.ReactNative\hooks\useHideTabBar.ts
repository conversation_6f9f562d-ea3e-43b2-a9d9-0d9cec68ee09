import { useCallback } from 'react';
import { useFocusEffect, useNavigation } from 'expo-router';

/**
 * Hook: useHideTabBar
 * --------------------
 * Hides the bottom tab bar when the screen is focused and restores it on unfocus.
 *
 * Responsibilities:
 * - Uses useFocusEffect to detect when the screen is in focus.
 * - Modifies the parent navigator's tabBarStyle to hide/show the tab bar.
 *
 * Usage:
 * useHideTabBar();
 *
 * Notes:
 * - Fully type-safe with Expo Router's NavigationProp.
 * - Keeps screen components clean by moving navigation styling out of the component.
 */
export function useHideTabBar() {
  const navigation = useNavigation();

  useFocusEffect(
    useCallback(() => {
      // Hide tab bar
      navigation.getParent()?.setOptions({ tabBarStyle: { display: 'none' } });

      // Restore tab bar on unfocus
      return () => {
        navigation.getParent()?.setOptions({ tabBarStyle: undefined });
      };
    }, [navigation]),
  );
}
