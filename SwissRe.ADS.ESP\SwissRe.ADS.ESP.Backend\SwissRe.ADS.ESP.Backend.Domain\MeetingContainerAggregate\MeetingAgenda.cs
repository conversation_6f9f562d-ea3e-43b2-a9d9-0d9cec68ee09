﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.Events;
using SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate
{
    public class MeetingAgenda : AgendaBase
    {
        private List<MeetingFileMetadata> _files = [];

        public Guid ParentContainerId { get; private set; }

        public string Name { get; private set; } = null!;
        public string? Description { get; private set; }
        public int Order { get; private set; }

        /// <summary>
        /// ID of the parent agenda if this agenda is a child of another agenda. Allows for nesting agendas in the UI visually.
        /// Important: this is not a hierarchical structure, but rather a flat list of agendas with a parent-child relationship in the database.
        /// If you need to perform hierarchical operations, you should use the ParentAgendaId property to traverse the tree manually.
        /// </summary>
        public Guid? ParentAgendaId { get; private set; }

        public IReadOnlyCollection<MeetingFileMetadata> Files => _files;

        private MeetingAgenda() { }

        public static MeetingAgenda Create(Guid parentContainerId, string name, int order = 0, string? description = null, MeetingAgenda? parentAgenda = null)
        {
            return new MeetingAgenda()
            {
                ParentContainerId = parentContainerId,
                Name = name,
                Description = description,
                Order = order,
                ParentAgendaId = parentAgenda?.Id
            };
        }

        /// <summary>
        /// Adds a new file to the agenda and raise DomainEvent to store file contents in the database
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="extension"></param>
        /// <param name="size"></param>
        /// <param name="order"></param>
        /// <param name="numberOfPages"></param>
        /// <param name="createdByUserId"></param>
        /// <param name="isSupplementaryFile"></param>
        /// <returns></returns>
        internal void AddFile(byte[] fileContents, string fileName, string extension, int size, int order, int numberOfPages, string createdByUserId, bool isSupplementaryFile = false)
        {
            var fileContentId = SequentialGuidGenerator.Next();
            var file = MeetingFileMetadata.Create(Id, fileName, Guid.NewGuid(), fileContentId, 1, extension, size, numberOfPages, order, createdByUserId, isSupplementaryFile);
            _files.Add(file);

            // Raise domain event to store file contents in database
            RaiseEvent(new FileMetadataAddedEvent(fileContents, fileContentId));
        }

        internal void ReplaceFileWithNewVersion(byte[] fileContents, string fileName, string extension, Guid originalFileMetadataId, int size, int numberOfPages, string createdByUserId)
        {
            Guard.Against.Null(originalFileMetadataId, nameof(originalFileMetadataId), "Original file metadata ID cannot be null.");
            var originalFile = _files.FirstOrDefault(f => f.Id == originalFileMetadataId);
            if (originalFile == null)
                throw new ArgumentOutOfRangeException("Original file was not found in the agenda.");

            // Mark the original file as obsolete
            originalFile.MarkAsOld();

            // Create a new file metadata for the new version
            var fileContentId = SequentialGuidGenerator.Next();
            var newFile = MeetingFileMetadata.Create(Id, fileName, originalFile.FileGroupId, fileContentId, originalFile.VersionNumber + 1, extension, size, numberOfPages, originalFile.Order, createdByUserId, originalFile.Supplementary);
            _files.Add(newFile);

            // Raise domain event to store new file contents in database
            RaiseEvent(new FileMetadataAddedEvent(fileContents, fileContentId));
        }

        internal void SetOrder(int order)
        {
            Guard.Against.Negative(order, nameof(order), "Order cannot be less than zero.");
            Order = order;
        }

        internal void MarkFileAsRead(Guid fileId, string currentUserId)
        {
            var file = _files.FirstOrDefault(f => f.Id == fileId);

            if (file == null)
            {
                throw new ArgumentOutOfRangeException($"File with ID {fileId} does not exist in this agenda.");
            }

            file.MarkAsRead(currentUserId);
        }

        internal void MarkFileAsUnread(Guid fileId, string currentUserId)
        {
            var file = _files.FirstOrDefault(f => f.Id == fileId);

            if (file == null)
            {
                throw new ArgumentOutOfRangeException($"File with ID {fileId} does not exist in this agenda.");
            }

            file.MarkAsUnread(currentUserId);
        }
    }
}
