import { Component, inject, OnInit, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MeetingTableComponent } from './components/meeting-table/meeting-table.component';
import { IGetContainerResponseBase, MeetingContainerClient } from '../../shared/apiServices';
import { forkJoin } from 'rxjs';
import { GlobalLoaderService } from '../../shared/global-loader/global-loader.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-meetings',
  imports: [MatTabsModule, MatButtonModule, MatIconModule, MeetingTableComponent],
  templateUrl: './meetings.component.html',
  styleUrl: './meetings.component.scss'
})
export class MeetingsComponent implements OnInit {
  readonly meetingContainerClient = inject(MeetingContainerClient);
  readonly globalLoaderService = inject(GlobalLoaderService);
  readonly router = inject(Router);

  currentData = signal<IGetContainerResponseBase[]>([]);
  pastData = signal<IGetContainerResponseBase[]>([]);

  ngOnInit(): void {
    const hideLoader = this.globalLoaderService.show();

    forkJoin([this.meetingContainerClient.getAdminDashboardCurrentData(), this.meetingContainerClient.getUserDashboardPastData()]).subscribe({
      next: ([currentData, pastData]) => {
        console.log(currentData, pastData);
        this.currentData.set([...currentData.meetings, ...currentData.votings]);
        this.pastData.set([...pastData.meetings, ...pastData.votings]);
      },
      complete: () => hideLoader()
    });
  }

  onNewMeetingClicked() {
    this.router.navigate(['admin', 'new-meeting']);
  }

  onNewResolutionClicked() {
    this.router.navigate(['admin', 'new-resolution']);
  }
}
