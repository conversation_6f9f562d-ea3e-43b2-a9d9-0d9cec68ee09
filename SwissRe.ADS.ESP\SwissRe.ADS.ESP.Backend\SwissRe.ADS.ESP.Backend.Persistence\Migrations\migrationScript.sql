﻿DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'espv2') THEN
        CREATE SCHEMA espv2;
    END IF;
END $EF$;
CREATE TABLE IF NOT EXISTS espv2."__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

START TRANSACTION;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
        IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'espv2') THEN
            CREATE SCHEMA espv2;
        END IF;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
        IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'events') THEN
            CREATE SCHEMA events;
        END IF;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN

            CREATE SEQUENCE "User_SequenceId_seq"
                START WITH 1
                INCREMENT BY 1
                NO MINVALUE
                NO MAXVALUE
                CACHE 1;
        
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."__JobExecutionMetadata" (
        "Id" character varying(100) NOT NULL,
        "LastSuccessfulRunUtc" timestamp with time zone,
        "JobRunWindowEndUtc" timestamp with time zone,
        "LastProcessedTimestampUtc" timestamp with time zone,
        "LastProcessedSequenceId" bigint NOT NULL,
        "LastUpdatedAt" timestamp with time zone NOT NULL,
        CONSTRAINT "PK___JobExecutionMetadata" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."DeviceSyncFeeds" (
        "Id" uuid NOT NULL,
        "UserId" character varying(255) NOT NULL,
        "DeviceId" character varying(255) NOT NULL,
        "EntityType" character varying(512) NOT NULL,
        "LastSuccessfulSync" timestamp with time zone,
        "LastSyncRequestedAt" timestamp with time zone NOT NULL,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_DeviceSyncFeeds" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."Domains" (
        "Id" uuid NOT NULL,
        "ParentId" uuid,
        "Name" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "HistoricalDocumentExpirationAfter" integer,
        "AnnotationsExpirationAfter" integer,
        "iPadDocumentsExpirationAfter" integer,
        "ChangeStatusToPastAfter" integer,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_Domains" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_Domains_Domains_ParentId" FOREIGN KEY ("ParentId") REFERENCES espv2."Domains" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."FileContents" (
        "Id" uuid NOT NULL,
        "EncryptedContents" bytea NOT NULL,
        "ThumbnailImageContents" bytea NOT NULL,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_FileContents" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE events."OutboxedIntegrationEvents" (
        "Id" uuid NOT NULL,
        "Event" text NOT NULL,
        "TypeCode" text NOT NULL,
        CONSTRAINT "PK_OutboxedIntegrationEvents" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."Roles" (
        "Id" uuid NOT NULL,
        "Name" character varying(512) NOT NULL,
        "DisplayName" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_Roles" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."Users" (
        "Id" text NOT NULL,
        "AzureUserId" uuid NOT NULL,
        "Email" character varying(256) NOT NULL,
        "FullName" character varying(256) NOT NULL,
        "IsActive" boolean NOT NULL,
        "JobTitle" character varying(256),
        "LastModifiedOnUTC" timestamp with time zone NOT NULL,
        "SequenceId" bigint NOT NULL DEFAULT (nextval('"User_SequenceId_seq"')),
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_Users" PRIMARY KEY ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."BatchedSyncChangeEntries" (
        "Id" uuid NOT NULL,
        "EntityId" uuid NOT NULL,
        "UpdatedOn" timestamp with time zone NOT NULL,
        "ValueAsJson" text,
        "UserLostAccess" boolean NOT NULL,
        "DeviceSyncFeedTrackerId" uuid NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_BatchedSyncChangeEntries" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_BatchedSyncChangeEntries_DeviceSyncFeeds_DeviceSyncFeedTrac~" FOREIGN KEY ("DeviceSyncFeedTrackerId") REFERENCES espv2."DeviceSyncFeeds" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."PendingSyncChangeEntries" (
        "Id" uuid NOT NULL,
        "EntityId" uuid NOT NULL,
        "UpdatedOn" timestamp with time zone NOT NULL,
        "ValueAsJson" text,
        "UserLostAccess" boolean NOT NULL,
        "DeviceSyncFeedTrackerId" uuid NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_PendingSyncChangeEntries" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_PendingSyncChangeEntries_DeviceSyncFeeds_DeviceSyncFeedTrac~" FOREIGN KEY ("DeviceSyncFeedTrackerId") REFERENCES espv2."DeviceSyncFeeds" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."SynchronizedChanges" (
        "Id" uuid NOT NULL,
        "DeviceSyncFeedTrackerId" uuid NOT NULL,
        "EntityId" uuid NOT NULL,
        "UpdatedOn" timestamp with time zone NOT NULL,
        "SyncedOn" timestamp with time zone NOT NULL,
        "ValueAsJson" text,
        "UserLostAccess" boolean NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_SynchronizedChanges" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_SynchronizedChanges_DeviceSyncFeeds_DeviceSyncFeedTrackerId" FOREIGN KEY ("DeviceSyncFeedTrackerId") REFERENCES espv2."DeviceSyncFeeds" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."DocumentsContainer" (
        "Id" uuid NOT NULL,
        "DomainId" uuid NOT NULL,
        "Name" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "State" character varying(256) NOT NULL,
        "LastUpdatedOnUTC" timestamp with time zone NOT NULL,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_DocumentsContainer" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_DocumentsContainer_Domains_DomainId" FOREIGN KEY ("DomainId") REFERENCES espv2."Domains" ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."MeetingsContainer" (
        "Id" uuid NOT NULL,
        "DomainId" uuid NOT NULL,
        "Name" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "State" character varying(256) NOT NULL,
        "StartTime" timestamp with time zone,
        "EndTime" timestamp with time zone,
        "Location" text,
        "IncludeGuestRole" boolean NOT NULL DEFAULT FALSE,
        "AutomaticallyExcludeNewUsers" boolean NOT NULL,
        "LastUpdatedOnUTC" timestamp with time zone NOT NULL,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_MeetingsContainer" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_MeetingsContainer_Domains_DomainId" FOREIGN KEY ("DomainId") REFERENCES espv2."Domains" ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."VotingsContainer" (
        "Id" uuid NOT NULL,
        "DomainId" uuid NOT NULL,
        "Name" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "State" character varying(256) NOT NULL,
        "DefaultVotingOptions" character varying(2056) NOT NULL,
        "IncludeGuestRole" boolean NOT NULL,
        "LastUpdatedOnUTC" timestamp with time zone NOT NULL,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_VotingsContainer" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VotingsContainer_Domains_DomainId" FOREIGN KEY ("DomainId") REFERENCES espv2."Domains" ("Id") ON DELETE SET NULL
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."DomainRoles" (
        "Id" uuid NOT NULL,
        "RoleId" uuid NOT NULL,
        "DomainId" uuid NOT NULL,
        "RoleType" character varying(255) NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_DomainRoles" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_DomainRoles_Domains_DomainId" FOREIGN KEY ("DomainId") REFERENCES espv2."Domains" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_DomainRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES espv2."Roles" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."Annotations" (
        "Id" uuid NOT NULL,
        "FileMetadataId" uuid NOT NULL,
        "FileMetadataType" character varying(128) NOT NULL,
        "AnnotationUniqueId" uuid NOT NULL,
        "EncryptedAnnotationXML" bytea NOT NULL,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "LastModifiedOn" timestamp with time zone NOT NULL,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_Annotations" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_Annotations_Users_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."UserRoles" (
        "UserId" text NOT NULL,
        "RoleId" uuid NOT NULL,
        CONSTRAINT "PK_UserRoles" PRIMARY KEY ("UserId", "RoleId"),
        CONSTRAINT "FK_UserRoles_Roles_RoleId" FOREIGN KEY ("RoleId") REFERENCES espv2."Roles" ("Id") ON DELETE RESTRICT,
        CONSTRAINT "FK_UserRoles_Users_UserId" FOREIGN KEY ("UserId") REFERENCES espv2."Users" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."VotingAnswerSupportingDocumentFileMetadatas" (
        "Id" uuid NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        "FileContentId" uuid NOT NULL,
        "OriginalFileName" character varying(512) NOT NULL,
        "DisplayFileName" character varying(512) NOT NULL,
        "FileExtension" character varying(256) NOT NULL,
        "Supplementary" boolean NOT NULL,
        "FileGroupId" uuid NOT NULL,
        "VersionNumber" integer NOT NULL,
        "IsLatest" boolean NOT NULL,
        "Order" integer NOT NULL,
        "Size" integer NOT NULL,
        "NumberOfPages" integer NOT NULL,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "LastModifiedByUserId" text NOT NULL,
        "LastModifiedOn" timestamp with time zone NOT NULL,
        "ReadStatuses" jsonb,
        CONSTRAINT "PK_VotingAnswerSupportingDocumentFileMetadatas" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VotingAnswerSupportingDocumentFileMetadatas_FileContents_Fi~" FOREIGN KEY ("FileContentId") REFERENCES espv2."FileContents" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_VotingAnswerSupportingDocumentFileMetadatas_Users_CreatedBy~" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingAnswerSupportingDocumentFileMetadatas_Users_LastModif~" FOREIGN KEY ("LastModifiedByUserId") REFERENCES espv2."Users" ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."DocumentsAgenda" (
        "Id" uuid NOT NULL,
        "ParentContainerId" uuid NOT NULL,
        "Name" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "Order" integer NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        "Permissions" jsonb,
        CONSTRAINT "PK_DocumentsAgenda" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_DocumentsAgenda_DocumentsContainer_ParentContainerId" FOREIGN KEY ("ParentContainerId") REFERENCES espv2."DocumentsContainer" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."MeetingsAgenda" (
        "Id" uuid NOT NULL,
        "ParentContainerId" uuid NOT NULL,
        "Name" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "Order" integer NOT NULL,
        "ParentAgendaId" uuid,
        "ORIGINAL_DB_ID" bigint,
        "Permissions" jsonb,
        CONSTRAINT "PK_MeetingsAgenda" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_MeetingsAgenda_MeetingsAgenda_ParentAgendaId" FOREIGN KEY ("ParentAgendaId") REFERENCES espv2."MeetingsAgenda" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_MeetingsAgenda_MeetingsContainer_ParentContainerId" FOREIGN KEY ("ParentContainerId") REFERENCES espv2."MeetingsContainer" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."VotingsAgenda" (
        "Id" uuid NOT NULL,
        "ParentContainerId" uuid NOT NULL,
        "Name" character varying(512) NOT NULL,
        "Description" character varying(5092),
        "Order" integer NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        "Permissions" jsonb,
        CONSTRAINT "PK_VotingsAgenda" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VotingsAgenda_VotingsContainer_ParentContainerId" FOREIGN KEY ("ParentContainerId") REFERENCES espv2."VotingsContainer" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."DocumentFileMetadatas" (
        "Id" uuid NOT NULL,
        "DocumentAgendaId" uuid NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        "FileContentId" uuid NOT NULL,
        "OriginalFileName" character varying(512) NOT NULL,
        "DisplayFileName" character varying(512) NOT NULL,
        "FileExtension" character varying(256) NOT NULL,
        "Supplementary" boolean NOT NULL,
        "FileGroupId" uuid NOT NULL,
        "VersionNumber" integer NOT NULL,
        "IsLatest" boolean NOT NULL,
        "Order" integer NOT NULL,
        "Size" integer NOT NULL,
        "NumberOfPages" integer NOT NULL,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "LastModifiedByUserId" text NOT NULL,
        "LastModifiedOn" timestamp with time zone NOT NULL,
        "ReadStatuses" jsonb,
        CONSTRAINT "PK_DocumentFileMetadatas" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_DocumentFileMetadatas_DocumentsAgenda_DocumentAgendaId" FOREIGN KEY ("DocumentAgendaId") REFERENCES espv2."DocumentsAgenda" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_DocumentFileMetadatas_FileContents_FileContentId" FOREIGN KEY ("FileContentId") REFERENCES espv2."FileContents" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_DocumentFileMetadatas_Users_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_DocumentFileMetadatas_Users_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES espv2."Users" ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."MeetingFileMetadatas" (
        "Id" uuid NOT NULL,
        "MeetingAgendaId" uuid NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        "FileContentId" uuid NOT NULL,
        "OriginalFileName" character varying(512) NOT NULL,
        "DisplayFileName" character varying(512) NOT NULL,
        "FileExtension" character varying(256) NOT NULL,
        "Supplementary" boolean NOT NULL,
        "FileGroupId" uuid NOT NULL,
        "VersionNumber" integer NOT NULL,
        "IsLatest" boolean NOT NULL,
        "Order" integer NOT NULL,
        "Size" integer NOT NULL,
        "NumberOfPages" integer NOT NULL,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "LastModifiedByUserId" text NOT NULL,
        "LastModifiedOn" timestamp with time zone NOT NULL,
        "ReadStatuses" jsonb,
        CONSTRAINT "PK_MeetingFileMetadatas" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_MeetingFileMetadatas_FileContents_FileContentId" FOREIGN KEY ("FileContentId") REFERENCES espv2."FileContents" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_MeetingFileMetadatas_MeetingsAgenda_MeetingAgendaId" FOREIGN KEY ("MeetingAgendaId") REFERENCES espv2."MeetingsAgenda" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_MeetingFileMetadatas_Users_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_MeetingFileMetadatas_Users_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES espv2."Users" ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."VotingQuestions" (
        "Id" uuid NOT NULL,
        "AgendaId" uuid NOT NULL,
        "Question" character varying(2056) NOT NULL,
        "DescriptionEncrypted" character varying(5092),
        "AvailableOptions" character varying(2056) NOT NULL,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "LastModifiedOn" timestamp with time zone NOT NULL,
        "LastModifiedByUserId" character varying(128) NOT NULL,
        "LockedForEditing" boolean NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_VotingQuestions" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VotingQuestions_Users_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingQuestions_Users_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingQuestions_VotingsAgenda_AgendaId" FOREIGN KEY ("AgendaId") REFERENCES espv2."VotingsAgenda" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."VotingAnswers" (
        "Id" uuid NOT NULL,
        "VotingQuestionId" uuid NOT NULL,
        "AnswerEncrypted" bytea NOT NULL,
        "CommentEncrypted" bytea,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "IsLatest" boolean NOT NULL,
        "OnBehalfOfUserId" character varying(256),
        "ORIGINAL_ONBEHALF_FILE_ID" bigint,
        "OnBehalfOfDocumenMetadatatId" uuid,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_VotingAnswers" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VotingAnswers_Users_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingAnswers_Users_OnBehalfOfUserId" FOREIGN KEY ("OnBehalfOfUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingAnswers_VotingAnswerSupportingDocumentFileMetadatas_O~" FOREIGN KEY ("OnBehalfOfDocumenMetadatatId") REFERENCES espv2."VotingAnswerSupportingDocumentFileMetadatas" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_VotingAnswers_VotingQuestions_VotingQuestionId" FOREIGN KEY ("VotingQuestionId") REFERENCES espv2."VotingQuestions" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE TABLE espv2."VotingQuestionSupportingDocumentFileMetadatas" (
        "Id" uuid NOT NULL,
        "VotingQuestionId" uuid NOT NULL,
        "VotingQuestionId1" uuid,
        "ORIGINAL_DB_ID" bigint,
        "FileContentId" uuid NOT NULL,
        "OriginalFileName" character varying(512) NOT NULL,
        "DisplayFileName" character varying(512) NOT NULL,
        "FileExtension" character varying(256) NOT NULL,
        "Supplementary" boolean NOT NULL,
        "FileGroupId" uuid NOT NULL,
        "VersionNumber" integer NOT NULL,
        "IsLatest" boolean NOT NULL,
        "Order" integer NOT NULL,
        "Size" integer NOT NULL,
        "NumberOfPages" integer NOT NULL,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "LastModifiedByUserId" text NOT NULL,
        "LastModifiedOn" timestamp with time zone NOT NULL,
        "ReadStatuses" jsonb,
        CONSTRAINT "PK_VotingQuestionSupportingDocumentFileMetadatas" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VotingQuestionSupportingDocumentFileMetadatas_FileContents_~" FOREIGN KEY ("FileContentId") REFERENCES espv2."FileContents" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_VotingQuestionSupportingDocumentFileMetadatas_Users_Created~" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingQuestionSupportingDocumentFileMetadatas_Users_LastMod~" FOREIGN KEY ("LastModifiedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingQuestionSupportingDocumentFileMetadatas_VotingQuestio~" FOREIGN KEY ("VotingQuestionId") REFERENCES espv2."VotingQuestions" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1" FOREIGN KEY ("VotingQuestionId1") REFERENCES espv2."VotingQuestions" ("Id")
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_Annotations_CreatedByUserId" ON espv2."Annotations" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_Annotations_FileMetadataId_CreatedByUserId" ON espv2."Annotations" ("FileMetadataId", "CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_BatchedSyncChangeEntries_DeviceSyncFeedTrackerId" ON espv2."BatchedSyncChangeEntries" ("DeviceSyncFeedTrackerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE UNIQUE INDEX "IX_DeviceSyncFeeds_UserId_DeviceId_EntityType" ON espv2."DeviceSyncFeeds" ("UserId", "DeviceId", "EntityType");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_DocumentFileMetadatas_CreatedByUserId" ON espv2."DocumentFileMetadatas" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_DocumentFileMetadatas_DocumentAgendaId" ON espv2."DocumentFileMetadatas" ("DocumentAgendaId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE UNIQUE INDEX "IX_DocumentFileMetadatas_FileContentId" ON espv2."DocumentFileMetadatas" ("FileContentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_DocumentFileMetadatas_LastModifiedByUserId" ON espv2."DocumentFileMetadatas" ("LastModifiedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_DocumentsAgenda_ParentContainerId" ON espv2."DocumentsAgenda" ("ParentContainerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_DocumentsContainer_DomainId" ON espv2."DocumentsContainer" ("DomainId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_DomainRoles_DomainId" ON espv2."DomainRoles" ("DomainId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_DomainRoles_RoleId" ON espv2."DomainRoles" ("RoleId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE UNIQUE INDEX "IX_Domains_ParentId" ON espv2."Domains" ("ParentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_MeetingFileMetadatas_CreatedByUserId" ON espv2."MeetingFileMetadatas" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE UNIQUE INDEX "IX_MeetingFileMetadatas_FileContentId" ON espv2."MeetingFileMetadatas" ("FileContentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_MeetingFileMetadatas_LastModifiedByUserId" ON espv2."MeetingFileMetadatas" ("LastModifiedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_MeetingFileMetadatas_MeetingAgendaId" ON espv2."MeetingFileMetadatas" ("MeetingAgendaId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_MeetingsAgenda_ParentAgendaId" ON espv2."MeetingsAgenda" ("ParentAgendaId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_MeetingsAgenda_ParentContainerId" ON espv2."MeetingsAgenda" ("ParentContainerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_MeetingsContainer_DomainId" ON espv2."MeetingsContainer" ("DomainId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_PendingSyncChangeEntries_DeviceSyncFeedTrackerId" ON espv2."PendingSyncChangeEntries" ("DeviceSyncFeedTrackerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_SynchronizedChanges_DeviceSyncFeedTrackerId" ON espv2."SynchronizedChanges" ("DeviceSyncFeedTrackerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_UserRoles_RoleId" ON espv2."UserRoles" ("RoleId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_Users_AzureUserId" ON espv2."Users" ("AzureUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingAnswers_CreatedByUserId" ON espv2."VotingAnswers" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE UNIQUE INDEX "IX_VotingAnswers_OnBehalfOfDocumenMetadatatId" ON espv2."VotingAnswers" ("OnBehalfOfDocumenMetadatatId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingAnswers_OnBehalfOfUserId" ON espv2."VotingAnswers" ("OnBehalfOfUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingAnswers_VotingQuestionId" ON espv2."VotingAnswers" ("VotingQuestionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingAnswerSupportingDocumentFileMetadatas_CreatedByUserId" ON espv2."VotingAnswerSupportingDocumentFileMetadatas" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE UNIQUE INDEX "IX_VotingAnswerSupportingDocumentFileMetadatas_FileContentId" ON espv2."VotingAnswerSupportingDocumentFileMetadatas" ("FileContentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingAnswerSupportingDocumentFileMetadatas_LastModifiedByU~" ON espv2."VotingAnswerSupportingDocumentFileMetadatas" ("LastModifiedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingQuestions_AgendaId" ON espv2."VotingQuestions" ("AgendaId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingQuestions_CreatedByUserId" ON espv2."VotingQuestions" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingQuestions_LastModifiedByUserId" ON espv2."VotingQuestions" ("LastModifiedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingQuestionSupportingDocumentFileMetadatas_CreatedByUser~" ON espv2."VotingQuestionSupportingDocumentFileMetadatas" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE UNIQUE INDEX "IX_VotingQuestionSupportingDocumentFileMetadatas_FileContentId" ON espv2."VotingQuestionSupportingDocumentFileMetadatas" ("FileContentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingQuestionSupportingDocumentFileMetadatas_LastModifiedB~" ON espv2."VotingQuestionSupportingDocumentFileMetadatas" ("LastModifiedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingQuestionSupportingDocumentFileMetadatas_VotingQuesti~1" ON espv2."VotingQuestionSupportingDocumentFileMetadatas" ("VotingQuestionId1");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingQuestionSupportingDocumentFileMetadatas_VotingQuestio~" ON espv2."VotingQuestionSupportingDocumentFileMetadatas" ("VotingQuestionId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingsAgenda_ParentContainerId" ON espv2."VotingsAgenda" ("ParentContainerId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    CREATE INDEX "IX_VotingsContainer_DomainId" ON espv2."VotingsContainer" ("DomainId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250807122500_InitialMigrations') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250807122500_InitialMigrations', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814101858_Migrations1') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250814101858_Migrations1', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814102752_DomainsOrder') THEN
    ALTER TABLE espv2."Domains" ADD "Order" integer NOT NULL DEFAULT 0;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814102752_DomainsOrder') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250814102752_DomainsOrder', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814105808_Domains1') THEN
    DROP INDEX espv2."IX_Domains_ParentId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814105808_Domains1') THEN
    CREATE INDEX "IX_Domains_ParentId" ON espv2."Domains" ("ParentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814105808_Domains1') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250814105808_Domains1', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814110825_Domains2') THEN
    ALTER TABLE espv2."Domains" ALTER COLUMN "Order" SET DEFAULT 1;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250814110825_Domains2') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250814110825_Domains2', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815055926_UserSalt') THEN
    CREATE TABLE espv2."UserSalt" (
        "Id" uuid NOT NULL,
        "UserId" character varying(128) NOT NULL,
        "Salt" character varying(512) NOT NULL,
        "RowVersion" bytea,
        "ORIGINAL_DB_ID" bigint,
        CONSTRAINT "PK_UserSalt" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_UserSalt_Users_UserId" FOREIGN KEY ("UserId") REFERENCES espv2."Users" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815055926_UserSalt') THEN
    CREATE UNIQUE INDEX "IX_UserSalt_UserId" ON espv2."UserSalt" ("UserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815055926_UserSalt') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250815055926_UserSalt', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815061047_UserSalt1') THEN
    ALTER TABLE espv2."UserSalt" DROP CONSTRAINT "FK_UserSalt_Users_UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815061047_UserSalt1') THEN
    DROP INDEX espv2."IX_UserSalt_UserId";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815061047_UserSalt1') THEN
    CREATE INDEX "IX_UserSalt_UserId" ON espv2."UserSalt" ("UserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815061047_UserSalt1') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250815061047_UserSalt1', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815070146_MeetingsOrder') THEN
    ALTER TABLE espv2."MeetingsContainer" ADD "Order" integer NOT NULL DEFAULT 0;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815070146_MeetingsOrder') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250815070146_MeetingsOrder', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815070242_MeetingsOrder1') THEN
    ALTER TABLE espv2."MeetingsContainer" ALTER COLUMN "Order" SET DEFAULT -999999;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250815070242_MeetingsOrder1') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250815070242_MeetingsOrder1', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250820072022_VotingQuestions') THEN
    ALTER TABLE espv2."VotingQuestions" ADD "DueDate" timestamp with time zone;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250820072022_VotingQuestions') THEN
    ALTER TABLE espv2."VotingQuestions" ADD "VotesVisibleForPublic" boolean NOT NULL DEFAULT FALSE;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250820072022_VotingQuestions') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250820072022_VotingQuestions', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821075743_VotingQuestions1') THEN
    ALTER TABLE espv2."VotingQuestions" ALTER COLUMN "DescriptionEncrypted" TYPE text;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821075743_VotingQuestions1') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250821075743_VotingQuestions1', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    DROP TABLE espv2."VotingQuestionSupportingDocumentFileMetadatas";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    CREATE TABLE espv2."VotingFileMetadatas" (
        "Id" uuid NOT NULL,
        "VotingAgendaId" uuid NOT NULL,
        "VotingAgendaId1" uuid NOT NULL,
        "ORIGINAL_DB_ID" bigint,
        "FileContentId" uuid NOT NULL,
        "OriginalFileName" character varying(512) NOT NULL,
        "DisplayFileName" character varying(512) NOT NULL,
        "FileExtension" character varying(256) NOT NULL,
        "Supplementary" boolean NOT NULL,
        "FileGroupId" uuid NOT NULL,
        "VersionNumber" integer NOT NULL,
        "IsLatest" boolean NOT NULL,
        "Order" integer NOT NULL,
        "Size" integer NOT NULL,
        "NumberOfPages" integer NOT NULL,
        "CreatedOn" timestamp with time zone NOT NULL,
        "CreatedByUserId" character varying(128) NOT NULL,
        "LastModifiedByUserId" text NOT NULL,
        "LastModifiedOn" timestamp with time zone NOT NULL,
        "ReadStatuses" jsonb,
        CONSTRAINT "PK_VotingFileMetadatas" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_VotingFileMetadatas_FileContents_FileContentId" FOREIGN KEY ("FileContentId") REFERENCES espv2."FileContents" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_VotingFileMetadatas_Users_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingFileMetadatas_Users_LastModifiedByUserId" FOREIGN KEY ("LastModifiedByUserId") REFERENCES espv2."Users" ("Id"),
        CONSTRAINT "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId1" FOREIGN KEY ("VotingAgendaId1") REFERENCES espv2."VotingsAgenda" ("Id") ON DELETE CASCADE,
        CONSTRAINT "FK_VotingFileMetadatas_VotingsContainer_VotingAgendaId" FOREIGN KEY ("VotingAgendaId") REFERENCES espv2."VotingsContainer" ("Id") ON DELETE CASCADE
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    CREATE INDEX "IX_VotingFileMetadatas_CreatedByUserId" ON espv2."VotingFileMetadatas" ("CreatedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    CREATE UNIQUE INDEX "IX_VotingFileMetadatas_FileContentId" ON espv2."VotingFileMetadatas" ("FileContentId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    CREATE INDEX "IX_VotingFileMetadatas_LastModifiedByUserId" ON espv2."VotingFileMetadatas" ("LastModifiedByUserId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    CREATE INDEX "IX_VotingFileMetadatas_VotingAgendaId" ON espv2."VotingFileMetadatas" ("VotingAgendaId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    CREATE INDEX "IX_VotingFileMetadatas_VotingAgendaId1" ON espv2."VotingFileMetadatas" ("VotingAgendaId1");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821091744_VotingFiles') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250821091744_VotingFiles', '9.0.5');
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821101748_VotingQuestion2') THEN
    ALTER TABLE espv2."VotingQuestions" ALTER COLUMN "DescriptionEncrypted" TYPE bytea;
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM espv2."__EFMigrationsHistory" WHERE "MigrationId" = '20250821101748_VotingQuestion2') THEN
    INSERT INTO espv2."__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250821101748_VotingQuestion2', '9.0.5');
    END IF;
END $EF$;
COMMIT;

