// services/FileStorageService.ts
import RNFS from 'react-native-fs';

export class FileStorageService {
  public static getThumbnailFilePath(fileId: string): string {
    return `file://${this.getFilePath(`${fileId}.png`)}`;
  }

  private static getFilePath(fileName: string): string {
    return `${RNFS.DocumentDirectoryPath}/${fileName}`;
  }

  /**
   * Save a file from a byte array
   */
  static async saveFileFromBytes(bytes: Uint8Array, fileName: string): Promise<string> {
    const path = this.getFilePath(fileName);

    console.log('Selected path', path);
    // Convert Uint8Array → string (binary)
    const binary = Array.from(bytes)
      .map((b) => String.fromCharCode(b))
      .join('');

    await RNFS.write(path, binary, 0, 'ascii');
    return `file://${path}`;
  }

  /**
   * Read file as byte array
   */
  static async readFileAsBytes(fileName: string): Promise<Uint8Array> {
    const path = this.getFilePath(fileName);
    const binary = await RNFS.read(path, undefined, 0, 'ascii');
    const bytes = new Uint8Array(binary.split('').map((c) => c.charCodeAt(0)));
    return bytes;
  }

  static async deleteFile(fileName: string): Promise<void> {
    const path = this.getFilePath(fileName);
    if (await RNFS.exists(path)) {
      await RNFS.unlink(path);
    }
  }

  static async fileExists(fileName: string): Promise<boolean> {
    const path = this.getFilePath(fileName);
    return RNFS.exists(path);
  }
}
