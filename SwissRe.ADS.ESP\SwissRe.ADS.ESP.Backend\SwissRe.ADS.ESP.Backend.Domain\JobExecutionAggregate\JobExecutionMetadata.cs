﻿using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.JobExecutionAggregate
{
    /// <summary>
    /// Tracks execution metadata for a scheduled job, including state needed for delta synchronization
    /// and recovery from partial failures.
    /// </summary>
    public class JobExecutionMetadata : Entity<string>, IAggregateRoot
    {
        /// <summary>
        /// The UTC timestamp of the last time the job fully completed successfully.
        /// Acts as the lower bound (inclusive) for the delta window in the next run.
        /// </summary>
        public DateTime? LastSuccessfulRunUtc { get; set; }

        /// <summary>
        /// The UTC timestamp captured at the start of the current (or most recent) job execution.
        /// Acts as the upper bound (exclusive) for the delta window, ensuring a stable snapshot of data.
        /// Must be persisted at the beginning of a run to support consistent resumption after failure.
        /// </summary>
        public DateTime? JobRunWindowEndUtc { get; set; }

        /// <summary>
        /// The UTC timestamp of the most recently processed record during the current job execution.
        /// Used in combination with LastProcessedId to support paging and crash recovery.
        /// </summary>
        public DateTime? LastProcessedTimestampUtc { get; set; }

        /// <summary>
        /// The sequence ID of the most recently processed record during the current job execution.
        /// Combined with LastProcessedTimestampUtc to enable ordered pagination and resume support,
        /// especially when multiple records share the same timestamp.
        /// </summary>
        public long LastProcessedSequenceId { get; set; }

        /// <summary>
        /// Timestamp of the last time this metadata row was updated.
        /// Used for monitoring, auditing, or diagnostics.
        /// </summary>
        public DateTime LastUpdatedAt { get; set; }

        public static JobExecutionMetadata CreateBlank(string jobName)
        {
            return new JobExecutionMetadata
            {
                Id = jobName,
                LastSuccessfulRunUtc = DateTime.MinValue,
                LastUpdatedAt = DateTime.UtcNow,
                LastProcessedSequenceId = 0,
            };
        }
    }

}
