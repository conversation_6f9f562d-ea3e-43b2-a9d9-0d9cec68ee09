﻿using SwissRe.ADS.Ddd.Events.Dispatching.Abstractions;
using SwissRe.ADS.ESP.Backend.Domain.VotingAnswerAggregate.Events;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAggregate.Events
{
    public class LockVotingQuestionOnUserVotedEventHandler(UnitOfWork unitOfWork) : DomainEventHandler<UserVotedEvent>
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;

        public override async Task HandleAsync(UserVotedEvent evt)
        {
            var questionContainer = await _unitOfWork.Repository<VotingContainer>().GetFirstOrNullAsync(
                                    containers => containers.Where(x => x.Agendas.Any(a => a.VotingQuestions.Any(o => o.Id == evt.VotingQuestionId))));

            if (questionContainer is null)
                throw new ArgumentNullException("Voting question container not found. Cannot lock voting question.");

            var agenda = questionContainer.Agendas.First(x => x.VotingQuestions.Any(o => o.Id == evt.VotingQuestionId));
            if (agenda is null)
                throw new ArgumentNullException("Agenda not found for the voting question. Cannot lock voting question.");

            questionContainer.LockQuestionOnUserVote(agenda.Id, evt.VotingQuestionId);
        }
    }
}
