﻿using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Application.Common.Persistence
{
    public static class RepositoryExtensions
    {
        public static async Task<TAggregateRoot> GetAsync<TAggregateRoot, TId>(this IRepository<TAggregateRoot> repository, TId id, bool forUpdate = false, RowVersion? expectedRowVersion = null)
            where TAggregateRoot : Entity<TId>, IAggregateRoot
            where TId : notnull
        {
            var entity = await repository.GetFirstAsync(aggregateRoot => aggregateRoot.Id.Equals(id), forUpdate);
            if (expectedRowVersion != null)
            {
                repository.CheckRowVersionUnchanged(entity, expectedRowVersion.Value);
            }
            return entity;
        }

        public static Task<TAggregateRoot?> GetOrNullAsync<TAggregateRoot, TId>(this IRepository<TAggregateRoot> repository, TId id, bool forUpdate = false)
            where TAggregateRoot : Entity<TId>, IAggregateRoot
            where TId : notnull
        {
            return repository.GetFirstOrNullAsync(aggregateRoot => aggregateRoot.Id.Equals(id), forUpdate);
        }

        public static async Task<List<TAggregateRoot>> GetAllAsync<TAggregateRoot, TId>(this IRepository<TAggregateRoot> repository, IEnumerable<TId> ids, bool forUpdate = false)
            where TAggregateRoot : Entity<TId>, IAggregateRoot
            where TId : notnull
        {
            var distinctIds = ids.Distinct().ToList();
            var aggregateRoots = await repository.GetAllAsync(aggregateRoot => distinctIds.Contains(aggregateRoot.Id), forUpdate);

            if (aggregateRoots.Count < distinctIds.Count)
            {
                throw new AggregateRootNotFoundException<TAggregateRoot>();
            }
            return aggregateRoots;
        }
    }
}
