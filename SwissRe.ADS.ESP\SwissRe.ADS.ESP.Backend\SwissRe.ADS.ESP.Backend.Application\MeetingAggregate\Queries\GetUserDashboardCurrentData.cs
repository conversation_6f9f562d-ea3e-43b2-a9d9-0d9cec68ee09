﻿using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.VotingContnainerAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.MeetingAggregate.Queries
{

    public class GetUserDashboardCurrentDataResponse
    {
        public List<GetUserMeetingResponse> Meetings { get; set; } = [];
        public List<GetUserVotingResponse> Votings { get; set; } = [];
    }

    public class GetUserDashboardCurrentDataEndpoint(UnitOfWork unitOfWork) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;

        public static void BuildRoute([EndpointRouteBuilder<MeetingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/userDashboard/current", (GetUserDashboardCurrentDataEndpoint endpoint, CancellationToken cancellationToken) => endpoint.HandleAsync(cancellationToken))
                .WithSummary("Get all current and future meetings that user can read.")
                .WithDescription("Returns a list of all meeting containers and their agendas that the current user has read access to, where the meetings are current or upcoming. The response includes meeting metadata and agenda information.")
                .WithAngularName<MeetingContainerRouteGroup>("GetUserDashboardCurrentData");
        }

        public async Task<GetUserDashboardCurrentDataResponse> HandleAsync(CancellationToken cancellationToken)
        {
            var allMeetings = await _unitOfWork.ReadRepository<IGetUserDashboardMeetingsRepository>().GetCurrentAndFutureUserMeetings(cancellationToken);
            var allVotings = await _unitOfWork.ReadRepository<IGetUserDashboardVotingsRepository>().GetCurrentAndFutureUserVotings(cancellationToken);

            return new()
            {
                Meetings = allMeetings,
                Votings = allVotings
            };
        }
    }
}
