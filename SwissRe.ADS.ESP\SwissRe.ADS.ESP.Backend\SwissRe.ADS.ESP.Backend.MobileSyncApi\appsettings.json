{"AllowedHosts": "*", "Persistence": {"LogToConsole": false, "ConnectionString": "Server=(localdb)\\mssqllocaldb;Database=AppSkeleton;Trusted_Connection=True;ConnectRetryCount=0"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "swissre.onmicrosoft.com", "TenantId": "45597f60-6e37-4be7-acfb-4c9e23b261ea", "ClientId": "c81f7bbf-cfa5-4304-922c-dd7aa9789b40", "ClientSecret": ""}, "Authentication": {"ProxyUrl": "http://gate.swissre.com:8080", "ShouldUseProxyForLocalDevelopment": true}, "ServiceBus": {"InMemory": true, "TopicName": "TODO: ie. appdevsvc-skeleton-dev", "DefaultMaxConcurrentCalls": 2}, "PDFTron": {"LicenseKey": "<KEY>"}, "UmiClientId": "TODO", "AngularUrl": "http://localhost:4200/"}