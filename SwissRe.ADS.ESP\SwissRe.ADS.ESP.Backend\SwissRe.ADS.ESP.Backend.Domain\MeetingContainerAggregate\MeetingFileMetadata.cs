﻿using SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata;

namespace SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate
{
    public class MeetingFileMetadata : FileMetadataBase
    {
        public Guid MeetingAgendaId { get; private set; }

        private MeetingFileMetadata() { }

        public static MeetingFileMetadata Create(Guid meetingAgendaId,
            string originalFileName,
            Guid fileGroupId,
            Guid fileContentId,
            int versionNumber,
            string fileExtension,
            int size,
            int numberOfPages,
            int order,
            string createdByUserId,
            bool supplementary = false)
        {
            var result = new MeetingFileMetadata();
            result.Initialize(originalFileName, fileGroupId, fileContentId, versionNumber, fileExtension, size, numberOfPages, order, createdByUserId, supplementary);
            result.MeetingAgendaId = meetingAgendaId;

            return result;
        }


    }
}
