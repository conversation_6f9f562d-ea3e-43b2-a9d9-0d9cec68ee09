﻿using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;
using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.VotingsAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;

namespace SwissRe.ADS.ESP.Backend.MobileSyncApi.Endpoints.VotingsAggregate.Queries
{
    public class GetPagedVotingDetailsForUserEndpoint(UnitOfWork unitOfWork, ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<VotingsAggregateRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services)
        {
            builder
                .MapGet("/users/me", (GetPagedVotingDetailsForUserEndpoint endpoint, int skip, DateTime lastSyncedOn, CancellationToken cancellationToken) => endpoint.HandleAsync(skip, lastSyncedOn, cancellationToken))
                .WithSummary("Get paged votings for the current user")
                .WithDescription("Retrieves a paginated list of votings associated with the currently authenticated user. Use the 'skip' parameter to paginate through results.")
                .WithAngularName<VotingsAggregateRouteGroup>("GetAllUserVotingsPaged");
        }

        public async Task<PagedVotingDetailResponse> HandleAsync(int skip, DateTime lastSyncedOn, CancellationToken cancellationToken)
        {
            return await _unitOfWork.ReadRepository<IVotingReadModelRepository>().GetPagedVotingDetailsForUser(_currentUser.SrUserId, lastSyncedOn, 50, skip, cancellationToken);
        }
    }
}
