﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingContainerAggregate.Events;

namespace SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate
{
    public class MeetingContainer : GuidEntity, IAggregateRoot
    {
        private List<MeetingAgenda> agendas = [];

        public Guid DomainId { get; private set; }

        public EspDomain Domain { get; private set; } = null!;
        public string Name { get; private set; } = null!;
        public string? Description { get; private set; }
        public ContainerStateEnum State { get; private set; }
        public DateTime? StartTime { get; private set; }
        public DateTime? EndTime { get; private set; }
        public string? Location { get; private set; }
        public int Order { get; private set; } = -999999;

        /// <summary>
        /// Flag that determines, whether Guest role should be included in the Agenda Permission
        /// </summary>
        public bool IncludeGuestRole { get; private set; } = false;

        /// <summary>
        /// Flag that determines that if the new user is added to Access product that belongs to the Domain
        /// whether they can be automatically added to respective agendas as dedicated roles or should be marked as Access.Denied
        /// </summary>
        public bool AutomaticallyExcludeNewUsers { get; private set; }

        public DateTime LastUpdatedOnUTC { get; private set; } = DateTime.UtcNow;

        public IReadOnlyCollection<MeetingAgenda> Agendas => agendas.AsReadOnly();

        internal MeetingAgenda GetAgenda(Guid agendaId) => agendas.First(a => a.Id == agendaId) ?? throw new ArgumentOutOfRangeException($"Agenda with ID {agendaId} does not exist in this container.");

        private MeetingContainer() { }

        public static MeetingContainer Create(string name, Guid domainId, DateTime? startTime, DateTime? endTime, string? location = null, string? description = null)
        {
            Guard.Against.NullOrWhiteSpace(name, nameof(Name), "Name cannot be blank.");

            if (startTime >= endTime)
            {
                throw new ArgumentException("Start time must be before end time.");
            }

            var meeting = new MeetingContainer
            {
                Name = name,
                DomainId = domainId,
                Description = description,
                State = ContainerStateEnum.Draft,
                StartTime = startTime,
                EndTime = endTime,
                Location = location,
            };

            return meeting;
        }

        public MeetingAgenda AddAgenda(string name, string? description, int order, List<(AgendaPermissionEnum accessType, string userId)> agendaUserAccess, MeetingAgenda? parentAgenda = null)
        {
            var newAgenda = MeetingAgenda.Create(Id, name, order, description, parentAgenda);
            foreach (var userAccess in agendaUserAccess)
                newAgenda.UpsertUserPermission(userAccess.userId, userAccess.accessType);

            agendas.Add(newAgenda);

            UpdateLastModifiedOn();
            RaiseEvent(new MeetingMetadataChangedEvent(Id));

            return newAgenda;
        }

        public void ReorderAgendas(IReadOnlyList<Guid> orderedAgendaIds)
        {
            AgendaReorderService.ReorderAgendaItems(agendas, orderedAgendaIds, (agenda, order) => agenda.SetOrder(order));

            UpdateLastModifiedOn();
            RaiseEvent(new MeetingMetadataChangedEvent(Id));
        }

        public void RemoveAgenda(Guid agendaId)
        {
            var agenda = agendas.FirstOrDefault(a => a.Id == agendaId);
            if (agenda == null)
            {
                throw new ArgumentException($"Agenda with ID {agendaId} does not exist in this meeting container.");
            }
            agendas.Remove(agenda);

            UpdateLastModifiedOn();
            RaiseEvent(new MeetingMetadataChangedEvent(Id));
        }

        /// <summary>
        /// Method used from sync job that ensures, user has access to all agendas in the meeting container.
        /// This has to work also for past meetings, so it does not check the state of the container.
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="roleType"></param>
        /// <exception cref="Exception"></exception>
        public void SynchronizeUserAccessForAllAgendas(string userId, AgendaPermissionEnum roleType)
        {
            //overwrite user access for a freshly added user to existing agenda via a sync job
            if (AutomaticallyExcludeNewUsers && agendas.All(o => o.Permissions.All(p => p.UserId != userId)))
                roleType = AgendaPermissionEnum.Deny;

            bool anyChanges = false;

            foreach (var agenda in agendas)
                if (agenda.UpsertUserPermission(userId, roleType))
                    anyChanges = true;

            if (anyChanges)
            {
                UpdateLastModifiedOn();
                RaiseEvent(new MeetingAccessRightsChangedForUserEvent(Id, userId));
            }
        }

        public void RemoveUserAccessFromAllAgendas(string userId)
        {
            foreach (var agenda in agendas)
                agenda.RemoveAllUserAccess(userId);

            UpdateLastModifiedOn();
            RaiseEvent(new MeetingAccessRightsChangedForUserEvent(Id, userId));
        }

        /// <summary>
        /// Overwrites AgendaPermission for the specified agenda and all its child agendas.
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="roleType"></param>
        /// <param name="agendaId"></param>
        public void OverwriteUserAccessForAgendaAndItsChildren(Guid agendaId, string userId, AgendaPermissionEnum roleType)
        {
            var agenda = GetAgenda(agendaId);
            if (agenda.ParentAgendaId.HasValue && ParentAgendaIsDenied(agenda.ParentAgendaId.Value, userId))
                throw new InvalidOperationException($"Cannot overwrite user access for requested agenda as it is not allowed by parent agenda permissions.");

            agenda.ManuallyOverwriteUserAccess(userId, roleType);

            // Update all child agendas recursively
            var childAgendas = agendas.Where(a => a.ParentAgendaId == agendaId).ToList();
            foreach (var child in childAgendas)
            {
                OverwriteUserAccessForAgendaAndItsChildren(child.Id, userId, roleType);
            }

            UpdateLastModifiedOn();
            RaiseEvent(new MeetingAccessRightsChangedForUserEvent(Id, userId));
        }

        private bool ParentAgendaIsDenied(Guid parentAgendaId, string userId)
        {
            var agenda = GetAgenda(parentAgendaId);
            var userPermissions = agenda.Permissions.First(o => o.UserId == userId);
            if (userPermissions is null || (userPermissions.CurrentPermission & AgendaPermissionEnum.Deny) == 0)
                return true;

            if (agenda.ParentAgendaId.HasValue)
            {
                // If the agenda has a parent, check if we can overwrite permissions for the parent agenda
                return ParentAgendaIsDenied(agenda.ParentAgendaId.Value, userId);
            }

            return false;
        }

        public void RenewMeeting()
        {
            if (State != ContainerStateEnum.PastMeeting)
            {
                throw new InvalidOperationException("Only past meetings can be renewed.");
            }
            // Logic to renew the meeting, e.g., updating the start and end times
            // For now, we will just throw a NotImplementedException
            throw new NotImplementedException("Renewal logic is not implemented yet.");

            UpdateLastModifiedOn();
        }

        public void SendNotificationMail()
        {
            // Logic to send notification email about the meeting
            // This could involve integration with an email service
            // For now, we will just throw a NotImplementedException
            throw new NotImplementedException("Email notification logic is not implemented yet.");

            UpdateLastModifiedOn();
        }

        /// <summary>
        /// Adds a new file to the agenda
        /// </summary>
        /// <param name="agendaId"></param>
        /// <param name="fileContents"></param>
        /// <param name="fileName"></param>
        /// <param name="extension"></param>
        /// <param name="size"></param>
        /// <param name="order"></param>
        /// <param name="numberOfPages"></param>
        /// <param name="createdByUserId"></param>
        /// <param name="isSupplementaryFile"></param>
        public void AddNewFile(Guid agendaId, byte[] fileContents, string fileName, string extension, int size, int order, int numberOfPages, string createdByUserId, bool isSupplementaryFile = false)
        {
            var agenda = GetAgenda(agendaId);
            agenda.AddFile(fileContents, fileName, extension, size, order, numberOfPages, createdByUserId, isSupplementaryFile);

            RaiseEvent(new MeetingMetadataChangedEvent(Id));
            UpdateLastModifiedOn();
        }

        /// <summary>
        /// Replaces an existing file with a new version in the target agenda.
        /// </summary>
        /// <param name="agendaId"></param>
        /// <param name="fileContents"></param>
        /// <param name="fileName"></param>
        /// <param name="extension"></param>
        /// <param name="originalFileMetadataId"></param>
        /// <param name="size"></param>
        /// <param name="numberOfPages"></param>
        /// <param name="createdByUserId"></param>
        public void ReplaceFileWithNewVersion(Guid agendaId, byte[] fileContents, string fileName, string extension, Guid originalFileMetadataId, int size, int numberOfPages, string createdByUserId)
        {
            var agenda = GetAgenda(agendaId);
            agenda.ReplaceFileWithNewVersion(fileContents, fileName, extension, originalFileMetadataId, size, numberOfPages, createdByUserId);

            RaiseEvent(new MeetingMetadataChangedEvent(Id));
            UpdateLastModifiedOn();
        }

        /// <summary>
        /// Marks a file as read for the current user in the specified agenda.
        /// This method does not update `LastUpdatedOnUTC` timestamp on aggregate, as it is not necessary for sync job
        /// </summary>
        /// <param name="agendaId"></param>
        /// <param name="fileId"></param>
        /// <param name="currentUserId"></param>
        public void MarkFileAsRead(Guid agendaId, Guid fileId, string currentUserId)
        {
            var agenda = GetAgenda(agendaId);
            agenda.MarkFileAsRead(fileId, currentUserId);
        }


        /// <summary>
        /// Marks a file as unread for the current user in the specified agenda.
        /// This method does not update `LastUpdatedOnUTC` timestamp on aggregate, as it is not necessary for sync job
        /// </summary>
        /// <param name="agendaId"></param>
        /// <param name="fileId"></param>
        /// <param name="currentUserId"></param>
        public void MarkFileAsUnread(Guid agendaId, Guid fileId, string currentUserId)
        {
            var agenda = GetAgenda(agendaId);
            agenda.MarkFileAsUnread(fileId, currentUserId);
        }

        /// <summary>
        /// Updates LastUpdatedOnUTC to indicate that the container or any of it's child entities have been modified.
        /// </summary>
        private void UpdateLastModifiedOn() => LastUpdatedOnUTC = DateTime.UtcNow;
    }
}