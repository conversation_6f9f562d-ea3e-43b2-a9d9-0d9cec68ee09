﻿namespace SwissRe.ADS.ESP.DataMigration.Utils
{
    public enum InstrumentType
    {
        COMMENT = 1,
        ERASER = 2,
        MARKER = 3,
        PENCIL = 4,
        ARROW = 5,
        FREE_TEXT = 31
    }

    public class OldAnnotationDocument
    {
        public AnnotationType documentMeta { get; set; }
    }

    public class AnnotationType
    {
        public List<AnnotationPage> pages { get; set; }
    }

    public class AnnotationPage
    {
        public List<AnnotationLayer> layers { get; set; }
        public AnnotationPageMeta pageMeta { get; set; }
    }

    public class AnnotationLayer
    {
        public List<DrawOperation> objects { get; set; }
    }

    public class AnnotationPageMeta
    {
        public int pageNumber { get; set; }
    }

    public class DrawOperation
    {
        public List<List<string>> points { get; set; } // corresponds to AnnotationPoint
        public ObjectMeta objectMeta { get; set; }
    }

    // Use this class if you need a version of points with numbers instead of strings
    public class DrawOperationNumeric
    {
        public List<List<double>> points { get; set; } // corresponds to AnnotationPointN
        public ObjectMeta objectMeta { get; set; }
    }

    public class ObjectMeta
    {
        public InstrumentType instrument { get; set; }
        public string color { get; set; }
        public int style { get; set; }
        public long timestampt { get; set; }
        public double width { get; set; }
        public double scale { get; set; }
        public string? text { get; set; }
    }

}
