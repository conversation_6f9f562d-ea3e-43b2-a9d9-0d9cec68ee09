﻿using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.MeetingAgendaAggregate;

namespace SwissRe.ADS.ESP.Backend.Domain.DocumentContainerAggregate
{
    public class DocumentContainer : GuidEntity, IAggregateRoot
    {
        private List<DocumentAgenda> agendas = [];
        public Guid DomainId { get; protected set; }

        public EspDomain Domain { get; set; } = null!;
        public string Name { get; protected set; } = null!;
        public string? Description { get; protected set; }
        public ContainerStateEnum State { get; protected set; }
        
        public DateTime LastUpdatedOnUTC { get; private set; } = DateTime.UtcNow;
        
        public IReadOnlyCollection<DocumentAgenda> Agendas => agendas.AsReadOnly();
        internal DocumentAgenda GetAgenda(Guid agendaId) => agendas.First(a => a.Id == agendaId) ?? throw new ArgumentException($"Agenda with ID {agendaId} does not exist in this container.");

        private DocumentContainer() { }

      
        public static DocumentContainer Create(string name, Guid domainId, string? description = null)
        {
            var repository = new DocumentContainer()
            {
                Name = name,
                DomainId = domainId,
                Description = description,
                State = ContainerStateEnum.Draft,
            };

            return repository;
        }

        public void AddAgenda(DocumentAgenda agenda)
        {
            agendas.Add(agenda);

            UpdateLastModifiedOn();
        }

        public void ReorderAgendas(IReadOnlyList<Guid> orderedAgendaIds)
        {
            AgendaReorderService.ReorderAgendaItems(agendas, orderedAgendaIds, (agenda, order) => agenda.SetOrder(order));

            UpdateLastModifiedOn();
        }

        public void RemoveAgenda(Guid agendaId)
        {
            var agenda = agendas.FirstOrDefault(a => a.Id == agendaId);
            if (agenda == null)
            {
                throw new ArgumentException($"Agenda with ID {agendaId} does not exist in this document container.");
            }
            agendas.Remove(agenda);

            UpdateLastModifiedOn();
        }

        /// <summary>
        /// Updates LastUpdatedOnUTC to indicate that the container or any of it's child entities have been modified.
        /// </summary>
        private void UpdateLastModifiedOn() => LastUpdatedOnUTC = DateTime.UtcNow;

    }
}
