@use "sass:map";

// Min widths in pixels per state/device. These values are used in both scss and ts (it's the single source of thruth for the whole application).
// In case you add/remove a state, do the same for APP_MEDIA_STATE_NAMES in typescript. APP_MEDIA_STATE_NAMES should mirror this map.
$statesMinWidths: (
  mobile: 0,
  tablet: 600,
  desktop: 1140
);

// Media query mixin.
//
// Params
//    $from - optional inclusive - state name (i.e. tablet)
//    $until - optional exclusive - state name (i.e. tablet)
//
// Examples:
//    @include media($until: desktop) - everything smaller than desktop
//    @include media($from: tablet)   - tablet and everything larger
//    @include media(mobile, desktop) - from mobile (inclusive) up to desktop (exclusive)
@mixin media($from: null, $until: null) {
  $fromMinWidth: map.get($statesMinWidths, $from);
  $untilMinWidth: map.get($statesMinWidths, $until);

  @if($from !=null and $fromMinWidth ==null) {
    @error "'#{$from}' is not a valid media state.";
  }

  @if($until !=null and $untilMinWidth ==null) {
    @error "'#{$until}' is not a valid media state.";
  }

  $minWidth: if($fromMinWidth, $fromMinWidth, 0);
  $maxWidth: if($untilMinWidth, max($untilMinWidth - 0.02, 0), null);

  @if($maxWidth !=null) {
    @media (min-width: #{$minWidth}px) and (max-width: #{$maxWidth}px) {
      @content;
    }
  }

  @else {
    @media (min-width: #{$minWidth}px) {
      @content;
    }
  }
}

// Exposing states to typescript.
body {
  @each $stateName, $minWidth in $statesMinWidths {
    --media-state-#{$stateName}: #{$minWidth};
  }
}