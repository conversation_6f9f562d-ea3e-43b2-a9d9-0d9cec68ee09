﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SwissRe.ADS.ESP.Backend.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class VotingAgenda : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId1",
                schema: "espv2",
                table: "VotingFileMetadatas");

            migrationBuilder.DropForeignKey(
                name: "FK_VotingFileMetadatas_VotingsContainer_VotingAgendaId",
                schema: "espv2",
                table: "VotingFileMetadatas");

            migrationBuilder.AlterColumn<Guid>(
                name: "VotingAgendaId1",
                schema: "espv2",
                table: "VotingFileMetadatas",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddForeignKey(
                name: "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "VotingAgendaId",
                principalSchema: "espv2",
                principalTable: "VotingsAgenda",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId1",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "VotingAgendaId1",
                principalSchema: "espv2",
                principalTable: "VotingsAgenda",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId",
                schema: "espv2",
                table: "VotingFileMetadatas");

            migrationBuilder.DropForeignKey(
                name: "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId1",
                schema: "espv2",
                table: "VotingFileMetadatas");

            migrationBuilder.AlterColumn<Guid>(
                name: "VotingAgendaId1",
                schema: "espv2",
                table: "VotingFileMetadatas",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_VotingFileMetadatas_VotingsAgenda_VotingAgendaId1",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "VotingAgendaId1",
                principalSchema: "espv2",
                principalTable: "VotingsAgenda",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_VotingFileMetadatas_VotingsContainer_VotingAgendaId",
                schema: "espv2",
                table: "VotingFileMetadatas",
                column: "VotingAgendaId",
                principalSchema: "espv2",
                principalTable: "VotingsContainer",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
