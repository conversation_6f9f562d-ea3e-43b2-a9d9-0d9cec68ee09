<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.TestHost" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.0" />
    <PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Api\SwissRe.ADS.ESP.Backend.Api.csproj" />
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Application\SwissRe.ADS.ESP.Backend.Application.csproj" />
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Domain\SwissRe.ADS.ESP.Backend.Domain.csproj" />
    <ProjectReference Include="..\SwissRe.ADS.ESP.Backend.Persistence\SwissRe.ADS.ESP.Backend.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
