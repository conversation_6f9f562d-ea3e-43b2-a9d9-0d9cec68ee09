﻿namespace SwissRe.ADS.ESP.Backend.Application.Common.Persistence
{
    /// <summary>
    /// A service that allows for queriyng database using raw sql.
    /// </summary>
    public interface ISqlQueryService
    {
        Task<IEnumerable<TResult>> QueryAsync<TResult>(string sql, object? parameters = null);
        Task<TResult> QueryFirstAsync<TResult>(string sql, object? parameters = null);
        Task<TResult?> QueryFirstOrDefaultAsync<TResult>(string sql, object? parameters = null);
        Task<TResult?> QueryScalarAsync<TResult>(string sql, object? parameters = null);
    }
}
