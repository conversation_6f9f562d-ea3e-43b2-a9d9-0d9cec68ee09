import { Component } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';

@Component({
    imports: [MatDialogModule, MatButtonModule],
    template: `
    <mat-dialog-content>
      <p>Due to security reasons you have been logged out of this application.</p>
      <p>We will now refresh your current window.</p>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button mat-button mat-dialog-close>OK</button>
    </mat-dialog-actions>
  `
})
export class Error401Component {}
