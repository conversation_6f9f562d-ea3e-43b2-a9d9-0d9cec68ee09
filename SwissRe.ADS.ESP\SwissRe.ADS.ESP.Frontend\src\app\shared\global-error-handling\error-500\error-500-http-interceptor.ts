import { inject } from '@angular/core';
import { HttpErrorResponse, HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { EMPTY, catchError } from 'rxjs';
import { Router } from '@angular/router';

export const error500HttpInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {
  const router = inject(Router);

  return next(req).pipe(
    catchError(err => {
      if (err instanceof HttpErrorResponse && err.status === 500) {
        router.navigate(['/error'], { info: true });
        return EMPTY;
      }
      throw err;
    })
  );
};
