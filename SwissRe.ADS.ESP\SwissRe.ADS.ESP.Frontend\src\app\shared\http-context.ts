import { HttpContext } from '@angular/common/http';

/**
 * Creates a new HttpContext and applies the specified modifiers to it.
 * A modifier is a function that takes an HttpContext and modifies it.
 */
export const useHttpContext = (...modifiers: HttpContextModifier[]) => {
  const httpContext = new HttpContext();
  modifiers.forEach(modifier => modifier(httpContext));
  return httpContext;
};

/** Function that takes an HttpContext and modifies it. */
export type HttpContextModifier = (httpContext: HttpContext) => void;
