﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class EspRoleConfiguration : IEntityTypeConfiguration<EspRole>
    {
        public void Configure(EntityTypeBuilder<EspRole> builder)
        {
            builder.ToTable("Roles");
            builder.Has<PERSON>ey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion(); // All aggregate roots should have a row version.

            builder.Property(x => x.Name).IsRequired().HasMaxLength(512);
            builder.Property(x => x.DisplayName).IsRequired().HasMaxLength(512);
            builder.Property(x => x.Description).HasMaxLength(5092);

            builder.HasMany<UserEspRole>()
                .WithOne()
                .HasForeignKey(x => x.RoleId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
