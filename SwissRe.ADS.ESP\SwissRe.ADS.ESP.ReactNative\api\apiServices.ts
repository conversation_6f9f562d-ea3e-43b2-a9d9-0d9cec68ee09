//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.5.0.0 (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* eslint-disable */
// ReSharper disable InconsistentNaming

import api from './axiosService';import axios, { AxiosError } from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, CancelToken } from 'axios';

export class SwaggerIncludeClient {
    protected instance: AxiosInstance;
    protected baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor() {
        this.instance = api;
        this.baseUrl = '';
    }

    meetingDetailResponse(signal?: AbortSignal): Promise<GetMeetingDetailResponse> {
        let url_ = this.baseUrl + "/__InternalModelExposure__/model-exposure/MeetingDetailResponse";
        url_ = url_.replace(/[?&]$/, "");

        let options_: AxiosRequestConfig = {
            method: "GET",
            url: url_,
            headers: {
                "Accept": "application/json"
            },
            signal
        };

        return this.instance.request(options_).catch((_error: any) => {
            if (isAxiosError(_error) && _error.response) {
                return _error.response;
            } else {
                throw _error;
            }
        }).then((_response: AxiosResponse) => {
            return this.processMeetingDetailResponse(_response);
        });
    }

    protected processMeetingDetailResponse(response: AxiosResponse): Promise<GetMeetingDetailResponse> {
        const status = response.status;
        let _headers: any = {};
        if (response.headers && typeof response.headers === "object") {
            for (const k in response.headers) {
                if (response.headers.hasOwnProperty(k)) {
                    _headers[k] = response.headers[k];
                }
            }
        }
        if (status === 200) {
            const _responseText = response.data;
            let result200: any = null;
            let resultData200  = _responseText;
            result200 = GetMeetingDetailResponse.fromJS(resultData200);
            return Promise.resolve<GetMeetingDetailResponse>(result200);

        } else if (status !== 200 && status !== 204) {
            const _responseText = response.data;
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
        }
        return Promise.resolve<GetMeetingDetailResponse>(null as any);
    }
}

export class VotingsAggregateClient {
    protected instance: AxiosInstance;
    protected baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor() {
        this.instance = api;
        this.baseUrl = '';
    }

    /**
     * Get paged votings for the current user
     */
    getAllUserVotingsPaged(skip: number, lastSyncedOn: Date, signal?: AbortSignal): Promise<PagedVotingDetailResponse> {
        let url_ = this.baseUrl + "/api/Votings/users/me?";
        if (skip === undefined || skip === null)
            throw new globalThis.Error("The parameter 'skip' must be defined and cannot be null.");
        else
            url_ += "skip=" + encodeURIComponent("" + skip) + "&";
        if (lastSyncedOn === undefined || lastSyncedOn === null)
            throw new globalThis.Error("The parameter 'lastSyncedOn' must be defined and cannot be null.");
        else
            url_ += "lastSyncedOn=" + encodeURIComponent(lastSyncedOn ? "" + lastSyncedOn.toISOString() : "") + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_: AxiosRequestConfig = {
            method: "GET",
            url: url_,
            headers: {
                "Accept": "application/json"
            },
            signal
        };

        return this.instance.request(options_).catch((_error: any) => {
            if (isAxiosError(_error) && _error.response) {
                return _error.response;
            } else {
                throw _error;
            }
        }).then((_response: AxiosResponse) => {
            return this.processGetAllUserVotingsPaged(_response);
        });
    }

    protected processGetAllUserVotingsPaged(response: AxiosResponse): Promise<PagedVotingDetailResponse> {
        const status = response.status;
        let _headers: any = {};
        if (response.headers && typeof response.headers === "object") {
            for (const k in response.headers) {
                if (response.headers.hasOwnProperty(k)) {
                    _headers[k] = response.headers[k];
                }
            }
        }
        if (status === 200) {
            const _responseText = response.data;
            let result200: any = null;
            let resultData200  = _responseText;
            result200 = PagedVotingDetailResponse.fromJS(resultData200);
            return Promise.resolve<PagedVotingDetailResponse>(result200);

        } else if (status !== 200 && status !== 204) {
            const _responseText = response.data;
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
        }
        return Promise.resolve<PagedVotingDetailResponse>(null as any);
    }

    /**
     * Get revoked votings for the current user
     */
    getRevokedVotingsForUser(request: GetRevokedVotingsForUserCommand, signal?: AbortSignal): Promise<string[]> {
        let url_ = this.baseUrl + "/api/Votings/users/me/revoked";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(request);

        let options_: AxiosRequestConfig = {
            data: content_,
            method: "POST",
            url: url_,
            headers: {
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            signal
        };

        return this.instance.request(options_).catch((_error: any) => {
            if (isAxiosError(_error) && _error.response) {
                return _error.response;
            } else {
                throw _error;
            }
        }).then((_response: AxiosResponse) => {
            return this.processGetRevokedVotingsForUser(_response);
        });
    }

    protected processGetRevokedVotingsForUser(response: AxiosResponse): Promise<string[]> {
        const status = response.status;
        let _headers: any = {};
        if (response.headers && typeof response.headers === "object") {
            for (const k in response.headers) {
                if (response.headers.hasOwnProperty(k)) {
                    _headers[k] = response.headers[k];
                }
            }
        }
        if (status === 200) {
            const _responseText = response.data;
            let result200: any = null;
            let resultData200  = _responseText;
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(item);
            }
            else {
                result200 = null as any;
            }
            return Promise.resolve<string[]>(result200);

        } else if (status !== 200 && status !== 204) {
            const _responseText = response.data;
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
        }
        return Promise.resolve<string[]>(null as any);
    }
}

export class MeetingV1Client {
    protected instance: AxiosInstance;
    protected baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor() {
        this.instance = api;
        this.baseUrl = '';
    }

    /**
     * Retrieves the next batch of meeting changes for the current user and device.
     */
    getNextMeetingBatch(userDeviceId: string, signal?: AbortSignal): Promise<EspChangeEntryResponse[]> {
        let url_ = this.baseUrl + "/api/MeetingSync/v1/users/me/getNextBatch?";
        if (userDeviceId === undefined || userDeviceId === null)
            throw new globalThis.Error("The parameter 'userDeviceId' must be defined and cannot be null.");
        else
            url_ += "userDeviceId=" + encodeURIComponent("" + userDeviceId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_: AxiosRequestConfig = {
            method: "GET",
            url: url_,
            headers: {
                "Accept": "application/json"
            },
            signal
        };

        return this.instance.request(options_).catch((_error: any) => {
            if (isAxiosError(_error) && _error.response) {
                return _error.response;
            } else {
                throw _error;
            }
        }).then((_response: AxiosResponse) => {
            return this.processGetNextMeetingBatch(_response);
        });
    }

    protected processGetNextMeetingBatch(response: AxiosResponse): Promise<EspChangeEntryResponse[]> {
        const status = response.status;
        let _headers: any = {};
        if (response.headers && typeof response.headers === "object") {
            for (const k in response.headers) {
                if (response.headers.hasOwnProperty(k)) {
                    _headers[k] = response.headers[k];
                }
            }
        }
        if (status === 200) {
            const _responseText = response.data;
            let result200: any = null;
            let resultData200  = _responseText;
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(EspChangeEntryResponse.fromJS(item));
            }
            else {
                result200 = null as any;
            }
            return Promise.resolve<EspChangeEntryResponse[]>(result200);

        } else if (status !== 200 && status !== 204) {
            const _responseText = response.data;
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
        }
        return Promise.resolve<EspChangeEntryResponse[]>(null as any);
    }
}

export class FileContentsV1Client {
    protected instance: AxiosInstance;
    protected baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor() {
        this.instance = api;
        this.baseUrl = '';
    }

    /**
     * Retrieves the file PDF contents for the current user and device.
     */
    getFilePdfContents(fileContentsId: string, signal?: AbortSignal): Promise<FileResponse> {
        let url_ = this.baseUrl + "/api/FileContentsSync/v1/users/filePdfContents/{fileContentsId}";
        if (fileContentsId === undefined || fileContentsId === null)
            throw new globalThis.Error("The parameter 'fileContentsId' must be defined.");
        url_ = url_.replace("{fileContentsId}", encodeURIComponent("" + fileContentsId));
        url_ = url_.replace(/[?&]$/, "");

        let options_: AxiosRequestConfig = {
            responseType: "arraybuffer",
            method: "GET",
            url: url_,
            headers: {
                "Accept": "application/json"
            },
            signal
        };

        return this.instance.request(options_).catch((_error: any) => {
            if (isAxiosError(_error) && _error.response) {
                return _error.response;
            } else {
                throw _error;
            }
        }).then((_response: AxiosResponse) => {
            return this.processGetFilePdfContents(_response);
        });
    }

    protected processGetFilePdfContents(response: AxiosResponse): Promise<FileResponse> {
        const status = response.status;
        let _headers: any = {};
        if (response.headers && typeof response.headers === "object") {
            for (const k in response.headers) {
                if (response.headers.hasOwnProperty(k)) {
                    _headers[k] = response.headers[k];
                }
            }
        }
        if (status === 200 || status === 206) {
            const contentDisposition = response.headers ? response.headers["content-disposition"] : undefined;
            let fileNameMatch = contentDisposition ? /filename\*=(?:(\\?['"])(.*?)\1|(?:[^\s]+'.*?')?([^;\n]*))/g.exec(contentDisposition) : undefined;
            let fileName = fileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[3] || fileNameMatch[2] : undefined;
            if (fileName) {
                fileName = decodeURIComponent(fileName);
            } else {
                fileNameMatch = contentDisposition ? /filename="?([^"]*?)"?(;|$)/g.exec(contentDisposition) : undefined;
                fileName = fileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[1] : undefined;
            }
            return Promise.resolve({ fileName: fileName, status: status, data: new Uint8Array(response.data), headers: _headers });
        } else if (status !== 200 && status !== 204) {
            const _responseText = response.data;
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
        }
        return Promise.resolve<FileResponse>(null as any);
    }

    /**
     * Retrieves the file thumbnail contents for the current user and device.
     */
    getFileThumbnailContents(fileContentsId: string, signal?: AbortSignal): Promise<FileResponse> {
        let url_ = this.baseUrl + "/api/FileContentsSync/v1/users/fileThumbnailContents/{fileContentsId}";
        if (fileContentsId === undefined || fileContentsId === null)
            throw new globalThis.Error("The parameter 'fileContentsId' must be defined.");
        url_ = url_.replace("{fileContentsId}", encodeURIComponent("" + fileContentsId));
        url_ = url_.replace(/[?&]$/, "");

        let options_: AxiosRequestConfig = {
            responseType: "arraybuffer",
            method: "GET",
            url: url_,
            headers: {
                "Accept": "application/json"
            },
            signal
        };

        return this.instance.request(options_).catch((_error: any) => {
            if (isAxiosError(_error) && _error.response) {
                return _error.response;
            } else {
                throw _error;
            }
        }).then((_response: AxiosResponse) => {
            return this.processGetFileThumbnailContents(_response);
        });
    }

    protected processGetFileThumbnailContents(response: AxiosResponse): Promise<FileResponse> {
        const status = response.status;
        let _headers: any = {};
        if (response.headers && typeof response.headers === "object") {
            for (const k in response.headers) {
                if (response.headers.hasOwnProperty(k)) {
                    _headers[k] = response.headers[k];
                }
            }
        }
        if (status === 200 || status === 206) {
            const contentDisposition = response.headers ? response.headers["content-disposition"] : undefined;
            let fileNameMatch = contentDisposition ? /filename\*=(?:(\\?['"])(.*?)\1|(?:[^\s]+'.*?')?([^;\n]*))/g.exec(contentDisposition) : undefined;
            let fileName = fileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[3] || fileNameMatch[2] : undefined;
            if (fileName) {
                fileName = decodeURIComponent(fileName);
            } else {
                fileNameMatch = contentDisposition ? /filename="?([^"]*?)"?(;|$)/g.exec(contentDisposition) : undefined;
                fileName = fileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[1] : undefined;
            }
            return Promise.resolve({ fileName: fileName, status: status, data: new Uint8Array(response.data), headers: _headers });
        } else if (status !== 200 && status !== 204) {
            const _responseText = response.data;
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
        }
        return Promise.resolve<FileResponse>(null as any);
    }
}

export class GetMeetingDetailResponse implements IGetMeetingDetailResponse {
    id!: string;
    name!: string;
    description?: string | null;
    state!: ContainerStateEnum;
    lastUpdatedOn!: Date;
    agendas!: GetMeetingAgendaResponse[];

    constructor(data?: IGetMeetingDetailResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
        if (!data) {
            this.agendas = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : null as any;
            this.name = _data["name"] !== undefined ? _data["name"] : null as any;
            this.description = _data["description"] !== undefined ? _data["description"] : null as any;
            this.state = _data["state"] !== undefined ? _data["state"] : null as any;
            this.lastUpdatedOn = _data["lastUpdatedOn"] ? new Date(_data["lastUpdatedOn"].toString()) : null as any;
            if (Array.isArray(_data["agendas"])) {
                this.agendas = [] as any;
                for (let item of _data["agendas"])
                    this.agendas!.push(GetMeetingAgendaResponse.fromJS(item));
            }
            else {
                this.agendas = null as any;
            }
        }
    }

    static fromJS(data: any): GetMeetingDetailResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetMeetingDetailResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : null as any;
        data["name"] = this.name !== undefined ? this.name : null as any;
        data["description"] = this.description !== undefined ? this.description : null as any;
        data["state"] = this.state !== undefined ? this.state : null as any;
        data["lastUpdatedOn"] = this.lastUpdatedOn ? this.lastUpdatedOn.toISOString() : null as any;
        if (Array.isArray(this.agendas)) {
            data["agendas"] = [];
            for (let item of this.agendas)
                data["agendas"].push(item ? item.toJSON() : null as any);
        }
        return data;
    }
}

export interface IGetMeetingDetailResponse {
    id: string;
    name: string;
    description?: string | null;
    state: ContainerStateEnum;
    lastUpdatedOn: Date;
    agendas: GetMeetingAgendaResponse[];
}

export enum ContainerStateEnum {
    Archived = "Archived",
    Deleted = "Deleted",
    Draft = "Draft",
    Published = "Published",
    PastMeeting = "PastMeeting",
}

export class GetMeetingAgendaResponse implements IGetMeetingAgendaResponse {
    id!: string;
    parentAgendaId?: string | null;
    name!: string;
    description?: string | null;
    order!: number;
    files!: FileMetadataResponse[];
    children!: GetMeetingAgendaResponse[];

    constructor(data?: IGetMeetingAgendaResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
        if (!data) {
            this.files = [];
            this.children = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : null as any;
            this.parentAgendaId = _data["parentAgendaId"] !== undefined ? _data["parentAgendaId"] : null as any;
            this.name = _data["name"] !== undefined ? _data["name"] : null as any;
            this.description = _data["description"] !== undefined ? _data["description"] : null as any;
            this.order = _data["order"] !== undefined ? _data["order"] : null as any;
            if (Array.isArray(_data["files"])) {
                this.files = [] as any;
                for (let item of _data["files"])
                    this.files!.push(FileMetadataResponse.fromJS(item));
            }
            else {
                this.files = null as any;
            }
            if (Array.isArray(_data["children"])) {
                this.children = [] as any;
                for (let item of _data["children"])
                    this.children!.push(GetMeetingAgendaResponse.fromJS(item));
            }
            else {
                this.children = null as any;
            }
        }
    }

    static fromJS(data: any): GetMeetingAgendaResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetMeetingAgendaResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : null as any;
        data["parentAgendaId"] = this.parentAgendaId !== undefined ? this.parentAgendaId : null as any;
        data["name"] = this.name !== undefined ? this.name : null as any;
        data["description"] = this.description !== undefined ? this.description : null as any;
        data["order"] = this.order !== undefined ? this.order : null as any;
        if (Array.isArray(this.files)) {
            data["files"] = [];
            for (let item of this.files)
                data["files"].push(item ? item.toJSON() : null as any);
        }
        if (Array.isArray(this.children)) {
            data["children"] = [];
            for (let item of this.children)
                data["children"].push(item ? item.toJSON() : null as any);
        }
        return data;
    }
}

export interface IGetMeetingAgendaResponse {
    id: string;
    parentAgendaId?: string | null;
    name: string;
    description?: string | null;
    order: number;
    files: FileMetadataResponse[];
    children: GetMeetingAgendaResponse[];
}

export class FileMetadataResponse implements IFileMetadataResponse {
    id!: string;
    displayName!: string;
    extension!: string;
    supplementary!: boolean;
    versionNumber!: number;
    isLatest!: boolean;
    order!: number;
    size!: number;
    numberOfPages!: number;
    isRead!: boolean;

    constructor(data?: IFileMetadataResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : null as any;
            this.displayName = _data["displayName"] !== undefined ? _data["displayName"] : null as any;
            this.extension = _data["extension"] !== undefined ? _data["extension"] : null as any;
            this.supplementary = _data["supplementary"] !== undefined ? _data["supplementary"] : null as any;
            this.versionNumber = _data["versionNumber"] !== undefined ? _data["versionNumber"] : null as any;
            this.isLatest = _data["isLatest"] !== undefined ? _data["isLatest"] : null as any;
            this.order = _data["order"] !== undefined ? _data["order"] : null as any;
            this.size = _data["size"] !== undefined ? _data["size"] : null as any;
            this.numberOfPages = _data["numberOfPages"] !== undefined ? _data["numberOfPages"] : null as any;
            this.isRead = _data["isRead"] !== undefined ? _data["isRead"] : null as any;
        }
    }

    static fromJS(data: any): FileMetadataResponse {
        data = typeof data === 'object' ? data : {};
        let result = new FileMetadataResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : null as any;
        data["displayName"] = this.displayName !== undefined ? this.displayName : null as any;
        data["extension"] = this.extension !== undefined ? this.extension : null as any;
        data["supplementary"] = this.supplementary !== undefined ? this.supplementary : null as any;
        data["versionNumber"] = this.versionNumber !== undefined ? this.versionNumber : null as any;
        data["isLatest"] = this.isLatest !== undefined ? this.isLatest : null as any;
        data["order"] = this.order !== undefined ? this.order : null as any;
        data["size"] = this.size !== undefined ? this.size : null as any;
        data["numberOfPages"] = this.numberOfPages !== undefined ? this.numberOfPages : null as any;
        data["isRead"] = this.isRead !== undefined ? this.isRead : null as any;
        return data;
    }
}

export interface IFileMetadataResponse {
    id: string;
    displayName: string;
    extension: string;
    supplementary: boolean;
    versionNumber: number;
    isLatest: boolean;
    order: number;
    size: number;
    numberOfPages: number;
    isRead: boolean;
}

export class PagedVotingDetailResponse implements IPagedVotingDetailResponse {
    meetings!: GetVotingDetailResponse[];
    hasMore!: boolean;

    constructor(data?: IPagedVotingDetailResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
        if (!data) {
            this.meetings = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["meetings"])) {
                this.meetings = [] as any;
                for (let item of _data["meetings"])
                    this.meetings!.push(GetVotingDetailResponse.fromJS(item));
            }
            else {
                this.meetings = null as any;
            }
            this.hasMore = _data["hasMore"] !== undefined ? _data["hasMore"] : null as any;
        }
    }

    static fromJS(data: any): PagedVotingDetailResponse {
        data = typeof data === 'object' ? data : {};
        let result = new PagedVotingDetailResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.meetings)) {
            data["meetings"] = [];
            for (let item of this.meetings)
                data["meetings"].push(item ? item.toJSON() : null as any);
        }
        data["hasMore"] = this.hasMore !== undefined ? this.hasMore : null as any;
        return data;
    }
}

export interface IPagedVotingDetailResponse {
    meetings: GetVotingDetailResponse[];
    hasMore: boolean;
}

export class GetVotingDetailResponse implements IGetVotingDetailResponse {
    id!: string;
    name!: string;
    description?: string | null;
    state!: ContainerStateEnum;
    agendas!: GetVotingAgendaResponse[];

    constructor(data?: IGetVotingDetailResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
        if (!data) {
            this.agendas = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : null as any;
            this.name = _data["name"] !== undefined ? _data["name"] : null as any;
            this.description = _data["description"] !== undefined ? _data["description"] : null as any;
            this.state = _data["state"] !== undefined ? _data["state"] : null as any;
            if (Array.isArray(_data["agendas"])) {
                this.agendas = [] as any;
                for (let item of _data["agendas"])
                    this.agendas!.push(GetVotingAgendaResponse.fromJS(item));
            }
            else {
                this.agendas = null as any;
            }
        }
    }

    static fromJS(data: any): GetVotingDetailResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetVotingDetailResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : null as any;
        data["name"] = this.name !== undefined ? this.name : null as any;
        data["description"] = this.description !== undefined ? this.description : null as any;
        data["state"] = this.state !== undefined ? this.state : null as any;
        if (Array.isArray(this.agendas)) {
            data["agendas"] = [];
            for (let item of this.agendas)
                data["agendas"].push(item ? item.toJSON() : null as any);
        }
        return data;
    }
}

export interface IGetVotingDetailResponse {
    id: string;
    name: string;
    description?: string | null;
    state: ContainerStateEnum;
    agendas: GetVotingAgendaResponse[];
}

export class GetVotingAgendaResponse implements IGetVotingAgendaResponse {
    id!: string;
    name!: string;
    description?: string | null;
    order!: number;

    constructor(data?: IGetVotingAgendaResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"] !== undefined ? _data["id"] : null as any;
            this.name = _data["name"] !== undefined ? _data["name"] : null as any;
            this.description = _data["description"] !== undefined ? _data["description"] : null as any;
            this.order = _data["order"] !== undefined ? _data["order"] : null as any;
        }
    }

    static fromJS(data: any): GetVotingAgendaResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetVotingAgendaResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id !== undefined ? this.id : null as any;
        data["name"] = this.name !== undefined ? this.name : null as any;
        data["description"] = this.description !== undefined ? this.description : null as any;
        data["order"] = this.order !== undefined ? this.order : null as any;
        return data;
    }
}

export interface IGetVotingAgendaResponse {
    id: string;
    name: string;
    description?: string | null;
    order: number;
}

export class GetRevokedVotingsForUserCommand implements IGetRevokedVotingsForUserCommand {
    votingContainerIdsOnMobileDevice!: string[];

    constructor(data?: IGetRevokedVotingsForUserCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
        if (!data) {
            this.votingContainerIdsOnMobileDevice = [];
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["votingContainerIdsOnMobileDevice"])) {
                this.votingContainerIdsOnMobileDevice = [] as any;
                for (let item of _data["votingContainerIdsOnMobileDevice"])
                    this.votingContainerIdsOnMobileDevice!.push(item);
            }
            else {
                this.votingContainerIdsOnMobileDevice = null as any;
            }
        }
    }

    static fromJS(data: any): GetRevokedVotingsForUserCommand {
        data = typeof data === 'object' ? data : {};
        let result = new GetRevokedVotingsForUserCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.votingContainerIdsOnMobileDevice)) {
            data["votingContainerIdsOnMobileDevice"] = [];
            for (let item of this.votingContainerIdsOnMobileDevice)
                data["votingContainerIdsOnMobileDevice"].push(item);
        }
        return data;
    }
}

export interface IGetRevokedVotingsForUserCommand {
    votingContainerIdsOnMobileDevice: string[];
}

export class EspChangeEntryResponse implements IEspChangeEntryResponse {
    entityId!: string;
    userLostAccess!: boolean;
    valueAsJson?: string | null;

    constructor(data?: IEspChangeEntryResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.entityId = _data["entityId"] !== undefined ? _data["entityId"] : null as any;
            this.userLostAccess = _data["userLostAccess"] !== undefined ? _data["userLostAccess"] : null as any;
            this.valueAsJson = _data["valueAsJson"] !== undefined ? _data["valueAsJson"] : null as any;
        }
    }

    static fromJS(data: any): EspChangeEntryResponse {
        data = typeof data === 'object' ? data : {};
        let result = new EspChangeEntryResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["entityId"] = this.entityId !== undefined ? this.entityId : null as any;
        data["userLostAccess"] = this.userLostAccess !== undefined ? this.userLostAccess : null as any;
        data["valueAsJson"] = this.valueAsJson !== undefined ? this.valueAsJson : null as any;
        return data;
    }
}

export interface IEspChangeEntryResponse {
    entityId: string;
    userLostAccess: boolean;
    valueAsJson?: string | null;
}

export interface FileResponse {
    data: Uint8Array;
    status: number;
    fileName?: string;
    headers?: { [name: string]: any };
}

export class ApiException extends Error {
    override message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): any {
    if (result !== null && result !== undefined)
        throw result;
    else
        throw new ApiException(message, status, response, headers, null);
}

function isAxiosError(obj: any): obj is AxiosError {
    return obj && obj.isAxiosError === true;
}