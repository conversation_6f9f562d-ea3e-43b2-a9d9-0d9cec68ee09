using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;

namespace SwissRe.ADS.ESP.Backend.Domain.Common.Tests.Agenda
{
    internal class AgendaBaseMock : IAgendaBase
    {
        private readonly List<AgendaPermission> _permissions = new List<AgendaPermission>();

        public AgendaBaseMock(AgendaPermission permission)
        {
            _permissions.Add(permission);
        }

        public AgendaBaseMock(List<AgendaPermission> permissions)
        {
            _permissions.AddRange(permissions);
        }

        public IReadOnlyList<AgendaPermission> Permissions => _permissions.AsReadOnly();
    }

    // Use AgendaPermissionEnum from main project

    [TestFixture]
    public class AgendaPermissionExtensionsTests
    {
        [Test]
        public void HasAccessFor_MultiplePermissions_UserHasAccessAmongOthers_ReturnsTrue()
        {
            var userId = "user4";
            var permissions = new List<AgendaPermission>()
            {
                AgendaPermission.Create("other1", AgendaPermissionEnum.Deny),
                AgendaPermission.Create("other2", AgendaPermissionEnum.Read),
                AgendaPermission.Create(userId, AgendaPermissionEnum.Read)
            };
            
            var agenda = new AgendaBaseMock(permissions);

            var expr = AgendaPermissionExtensions.HasReadAccessFor<AgendaBaseMock>(userId);
            var func = expr.Compile();
            Assert.That(func(agenda), Is.True);
        }

        [Test]
        public void HasAccessFor_UserHasAccess_ReturnsTrue()
        {
            var userId = "user1";
            var agenda = new AgendaBaseMock(AgendaPermission.Create(userId, AgendaPermissionEnum.Read));

            var expr = AgendaPermissionExtensions.HasReadAccessFor<AgendaBaseMock>(userId);
            var func = expr.Compile();

            // Use Assert.That with Is.True for NUnit 3 compatibility
            Assert.That(func(agenda), Is.True);
        }

        [Test]
        public void HasAccessFor_UserHasDenyPermission_ReturnsFalse()
        {
            var userId = "user2";
            var agenda = new AgendaBaseMock(AgendaPermission.Create(userId, AgendaPermissionEnum.Deny));

            var expr = AgendaPermissionExtensions.HasReadAccessFor<AgendaBaseMock>(userId);
            var func = expr.Compile();

            // Use Assert.That with Is.False for NUnit 3 compatibility
            Assert.That(func(agenda), Is.False);
        }

        [Test]
        public void HasAccessFor_UserNotInPermissions_ReturnsFalse()
        {
            var userId = "user3";
            var agenda = new AgendaBaseMock(AgendaPermission.Create("other", AgendaPermissionEnum.Read));

            var expr = AgendaPermissionExtensions.HasReadAccessFor<AgendaBaseMock>(userId);
            var func = expr.Compile();

            // Use Assert.That with Is.False for NUnit 3 compatibility
            Assert.That(func(agenda), Is.False);
        }
    }
}