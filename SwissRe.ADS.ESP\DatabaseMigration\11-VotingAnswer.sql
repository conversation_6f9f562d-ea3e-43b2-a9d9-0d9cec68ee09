-- VotingAnswers table
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM espv2."VotingAnswers") <> 0 THEN
        RAISE NOTICE 'Delete FROM espv2.VotingAnswers';
        DELETE FROM espv2."VotingAnswers";
    END IF;
END $$;

select
    gen_random_uuid() AS Id
    ,(SELECT "Id" from espv2."VotingQuestions" WHERE "ORIGINAL_DB_ID" = a.question_tid) AS VotingQuestionId
    ,a.answer_enc as AnswerEncrypted
    ,a.cmt_enc as CommentEncrypted
    ,a.user_id AS CreatedByUserId
    ,a.edited AS CreatedOn
    ,NULL AS IsLatest -- TODO: Discuss with <PERSON><PERSON>
    ,a.edited_by AS OnBehalfOfUserId
    ,NULL AS ORIGINAL_ONBEHALF_FILE_ID -- TODO
    ,NULL AS OnBehalfOfDocumentMetadataId
    ,a.answer_tid as ORIGINAL_DB_ID
    ,* 
    -- TODO: ask <PERSON> which of those columns (user_id and edited_by) are used for voting by and voting on behalf of
from 
    esp.t_cr_answer a
ORDER BY a.question_tid, a.user_id