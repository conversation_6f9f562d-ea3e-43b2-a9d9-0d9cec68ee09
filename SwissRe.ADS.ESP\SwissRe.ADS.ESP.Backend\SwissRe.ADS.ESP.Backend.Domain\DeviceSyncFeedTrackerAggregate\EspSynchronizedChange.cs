﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DeviceSyncFeedTrackerAggregate
{
    public class EspSynchronizedChange : GuidEntity
    {
        /// <summary>
        /// Foreign key to the DeviceSyncFeedTracker that this change entry belongs to.
        /// </summary>
        public Guid DeviceSyncFeedTrackerId { get; private set; }
        
        /// <summary>
        /// Primary key of the Entity that we are syncinng (DocumentContainer, MeetingContainer, VotingContainer, etc.).
        /// </summary>
        public Guid EntityId { get; private set; }

        /// <summary>
        /// Last updated on timestamp of the entity.
        /// TODO: Do we really need this value?
        /// </summary>
        public DateTime UpdatedOn { get; private set; } //LastUpdatedOn of the entity from the sync

        public DateTime SyncedOn { get; private set; }// Timestamp when the change was synchronized with the client device

        /// <summary>
        /// Stringified data object representing the entity.
        /// </summary>
        public string? ValueAsJson { get; set; } //stringified DTO object 

        /// <summary>
        /// Indicates whether the user has lost access to this entity and it should be deleted on the target device.
        /// </summary>
        public bool UserLostAccess { get; set; }

        private EspSynchronizedChange() { }

        /// <summary>
        /// Creates a new instance of SyncEntityBatchData with the specified entity ID and JSON value. Use this when user still has value to the target entity  
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="valueAsJson"></param>
        /// <returns></returns>
        public static EspSynchronizedChange CreateWithValue(Guid deviceSyncFeedTrackerId, Guid entityId, DateTime entityLastUpdatedOn, string valueAsJson)
        {
            Guard.Against.NullOrEmpty(valueAsJson, nameof(valueAsJson), "ValueAsJson cannot be null or empty.");

            return new EspSynchronizedChange
            {
                DeviceSyncFeedTrackerId = deviceSyncFeedTrackerId,
                EntityId = entityId,
                UpdatedOn = entityLastUpdatedOn,
                UserLostAccess = false,
                ValueAsJson = valueAsJson,
                SyncedOn = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a new instance of SyncEntityBatchData with the specified entity ID indicating that the user has lost access to this entity.
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        public static EspSynchronizedChange CreateUserLostAccess(Guid deviceSyncFeedTrackerId, Guid entityId, DateTime entityLastUpdatedOn)
        {
            return new EspSynchronizedChange
            {
                DeviceSyncFeedTrackerId = deviceSyncFeedTrackerId,
                EntityId = entityId,
                UpdatedOn = entityLastUpdatedOn,
                UserLostAccess = true,
                ValueAsJson = null,
                SyncedOn = DateTime.UtcNow
            };
        }
    }
}
