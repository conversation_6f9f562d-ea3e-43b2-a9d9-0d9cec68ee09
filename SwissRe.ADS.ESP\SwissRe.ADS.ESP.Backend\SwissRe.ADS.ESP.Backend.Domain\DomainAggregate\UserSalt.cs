﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;

namespace SwissRe.ADS.ESP.Backend.Domain.DomainAggregate
{
    public class UserSalt : GuidEntity, IAggregateRoot
    {
        public string UserId { get; private set; } = string.Empty;
        public string Salt { get; private set; } = null!;

        public static UserSalt Create(string userId, string salt)
        {
            Guard.Against.NullOrWhiteSpace(userId, nameof(UserId), "UserId cannot be null or empty.");
            Guard.Against.NullOrWhiteSpace(salt, nameof(Salt), "Salt cannot be null or empty.");

            return new UserSalt() { UserId = userId, Salt = salt };
        }

        public static UserSalt Create(string userId, string salt, Guid id)
        {
            Guard.Against.NullOrWhiteSpace(userId, nameof(UserId), "UserId cannot be null or empty.");
            Guard.Against.NullOrWhiteSpace(salt, nameof(Salt), "Salt cannot be null or empty.");
            return new UserSalt() { UserId = userId, Salt = salt, Id = id };
        }

        private UserSalt() { }
    }
}
