﻿using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Domain.Common.Containers;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate
{
    /// <summary>
    /// Abstract base class for user container response DTOs.
    /// Encapsulates common metadata shared by all container types (e.g., meetings, votings).
    /// </summary>
    public abstract class GetContainerResponseBase
    {
        /// <summary>
        /// Specifies the type of container (e.g., Meeting, Voting, Document).
        /// Used to distinguish between different aggregate roots in the UI.
        /// </summary>
        public ContainerTypeEnum ContainerType { get; set; }

        /// <summary>
        /// Domain information associated with the container.
        /// Contains domain metadata such as ID, name, and parent domain.
        /// </summary>
        public GetDomainResponse Domain { get; set; } = null!;

        /// <summary>
        /// Unique identifier of the container (meeting, voting, etc.).
        /// </summary>
        public Guid ContainerId { get; set; }

        /// <summary>
        /// Title or display name of the container.
        /// Used for UI display purposes.
        /// </summary>
        public string Title { get; set; } = null!;

        /// <summary>
        /// StartTime of the event
        /// Used for UI display purposes.
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// State of the container
        /// Used for UI display purposes.
        /// </summary>
        public ContainerStateEnum State { get; set; }
    }
}
