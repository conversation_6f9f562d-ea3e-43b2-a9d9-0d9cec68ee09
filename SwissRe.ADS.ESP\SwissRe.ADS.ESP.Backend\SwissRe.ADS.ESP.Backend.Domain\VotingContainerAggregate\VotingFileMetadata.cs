﻿using SwissRe.ADS.ESP.Backend.Domain.Common.FileMetadata;

namespace SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate
{
    public class VotingFileMetadata : FileMetadataBase
    {
        public Guid VotingAgendaId { get; private set; }

        private VotingFileMetadata() { }

        public static VotingFileMetadata Create(Guid votingAgendaId,
           string originalFileName,
           Guid fileGroupId,
           Guid fileContentId,
           int versionNumber,
           string fileExtension,
           int size,
           int numberOfPages,
           int order,
           string createdByUserId,
           bool supplementary = false)
        {
            var result = new VotingFileMetadata();
            result.Initialize(originalFileName, fileGroupId, fileContentId, versionNumber, fileExtension, size, numberOfPages, order, createdByUserId, supplementary);
            result.VotingAgendaId = votingAgendaId;
            return result;
        }
    }
}
