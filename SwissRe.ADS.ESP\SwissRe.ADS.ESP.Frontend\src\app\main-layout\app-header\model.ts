import { Environment } from "../../shared/environment-enum";

export interface AppHeader {
  appName: string;
  environmentName?: string;
  appVersion?: string;
  environment?: Environment;
  mainActions?: AppHeaderMainAction[];
  menuItems?: AppHeaderMenuItem[];
}

export interface AppHeaderMainAction {
  /** Unique constant identifier of the main action among all main actions. */
  key: AppHeaderMainActionKey;
  icon: string;
  title: string;
  enabled?: boolean;
  visible?: boolean;
  action: () => void;
}
export type AppHeaderMainActionKey = unknown;

export interface AppHeaderMenuItem {
  /** Unique constant identifier of the menu item among all menu items. */
  key: AppHeaderMenuItemKey;
  icon: string;
  title: string;
  enabled?: boolean;
  visible?: boolean;
  action: () => void;
}
export type AppHeaderMenuItemKey = unknown;
