﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using System.Text.Json;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers
{
    internal static class VotingOptionValueConverter
    {
        internal static PropertyBuilder<VotingOptions> HasVotingOptionsConverter(this PropertyBuilder<VotingOptions> propertyBuilder)
        {
            var converter = new ValueConverter<VotingOptions, string>(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<VotingOptions>(v, (JsonSerializerOptions?)null)!
            );

            return propertyBuilder.HasConversion(converter)
                                  .HasColumnType("character varying(2056)");

        }
    }
}
