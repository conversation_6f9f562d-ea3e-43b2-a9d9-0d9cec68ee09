﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.ToTable("Users");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion(); // All aggregate roots should have a row version.

            builder.Property(x => x.AzureUserId).IsRequired();
            builder.Property(x => x.Email).IsRequired().HasMaxLength(256);
            builder.Property(x => x.FullName).IsRequired().HasMaxLength(256);
            builder.Property(x => x.JobTitle).HasMaxLength(256);

            builder.Property(x => x.SequenceId)
               .IsRequired()
               .ValueGeneratedOnAdd()
               .HasColumnType("bigint")
               .HasDefaultValueSql("nextval('\"User_SequenceId_seq\"')");

            builder.HasMany(x => x.UserRoles)
                .WithOne()
                .HasForeignKey(x => x.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasIndex(x => x.AzureUserId);

            builder.Navigation(p => p.UserRoles).AutoInclude();
        }
    }
}
