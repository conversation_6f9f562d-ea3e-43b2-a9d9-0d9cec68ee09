<div class="row">
  <mat-tab-group class="meetings-tab" mat-stretch-tabs="false" mat-align-tabs="start">
    <mat-tab label="Current meetings"><app-meeting-table [data]="currentData()" /></mat-tab>
    <mat-tab label="Past meetings"><app-meeting-table [data]="pastData()" /></mat-tab>
  </mat-tab-group>

  <div class="action-buttons">
    <button matButton="filled" color="primary" (click)="onNewMeetingClicked()">
      <mat-icon>add</mat-icon>
      Meeting
    </button>

    <button matButton="filled" color="primary" (click)="onNewResolutionClicked()">
      <mat-icon>add</mat-icon>
      Resolution / Attestation
    </button>

    <button matButton="outlined" color="primary">
      Manage properties
      <mat-icon iconPositionEnd>arrow_drop_down</mat-icon>
    </button>
  </div>
</div>
