﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class VotingFileMetadataConfiguration : FileMetadataBaseConfiguration<VotingFileMetadata>
    {
        public override void Configure(EntityTypeBuilder<VotingFileMetadata> builder)
        {
            builder.ToTable("VotingFileMetadatas");
            base.Configure(builder);
        }
    }
}
