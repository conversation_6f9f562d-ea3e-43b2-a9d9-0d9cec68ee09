﻿using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.MeetingAggregate;
using SwissRe.ADS.ESP.Backend.Domain.ContainerBaseAggregate;

namespace SwissRe.ADS.ESP.Backend.Application.Common.ReadModels.VotingsAggregate
{
    public class GetVotingDetailResponse
    {
        public Guid Id { get; init; }
        public string Name { get; init; } = null!;
        public string? Description { get; init; }
        public ContainerStateEnum State { get; init; }
        public IEnumerable<GetVotingAgendaResponse> Agendas { get; init; } = [];
    }
}
