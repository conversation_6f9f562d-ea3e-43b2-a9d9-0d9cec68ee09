﻿using SwissRe.ADS.ESP.Backend.Application.Common.ReadModels;

namespace SwissRe.ADS.ESP.Backend.Application.Common.MeetingContainerAggregate
{
    public interface IGetUserDashboardMeetingsRepository : IReadModelRepository
    {
        /// <summary>
        /// Retrieves all meeting containers accessible to the specified user that are in the current or future state.
        /// Only containers with <see cref="ContainerStateEnum.Published"/> are included.
        /// The result includes meeting metadata, agenda information where applicable.
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token for async operation.</param>
        /// <returns>
        /// A list of <see cref="GetUserMeetingResponse"/> objects representing current and future meetings to the user.
        /// </returns>
        Task<List<GetUserMeetingResponse>> GetCurrentAndFutureUserMeetings(CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves all meeting containers accessible to the specified user that are in the completed/past state.
        /// Only containers with <see cref="ContainerStateEnum.PastMeeting"/> are included.
        /// The result includes meeting metadata, agenda information where applicable.
        /// </summary>
        /// <param name="cancellationToken">Optional cancellation token for async operation.</param>
        /// <returns>
        /// A list of <see cref="GetUserMeetingResponse"/> objects representing completed meetings to the user.
        /// </returns>
        Task<List<GetUserMeetingResponse>> GetPastUserMeetings(CancellationToken cancellationToken = default);

    }
}
