using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class UserSaltConfiguration : IEntityTypeConfiguration<UserSalt>
    {
        public void Configure(EntityTypeBuilder<UserSalt> builder)
        {
            builder.ToTable("UserSalt");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();

            builder.HasRowVersion(); // All aggregate roots should have a row version.

            builder.Property(x => x.UserId)
                .IsRequired()
                .HasMaxLength(128);

            builder.Property(x => x.Salt)
                .IsRequired()
                .HasMaxLength(512);

            builder.HasIndex(x => x.UserId);
        }
    }
}