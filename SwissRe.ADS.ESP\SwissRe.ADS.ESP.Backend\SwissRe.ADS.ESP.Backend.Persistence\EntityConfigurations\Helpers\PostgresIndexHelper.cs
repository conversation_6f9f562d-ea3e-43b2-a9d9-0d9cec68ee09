﻿namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations.Helpers
{
    /// <summary>
    /// How to use in Migrations file. In Up method:
    ///     migrationBuilder.Sql(SqlIndexHelper.CreateGinIndex(
    ///        tableName: "DocumentsAgenda",
    ///        columnName: "Permissions"
    ///     ));
    ///     
    /// in Down method:
    ///     migrationBuilder.Sql(SqlIndexHelper.DropGinIndex(
    ///         tableName: "DocumentsAgenda",
    ///         columnName: "Permissions"
    ///     ));
    /// </summary>
    internal static class PostgresIndexHelper
    {
        public static string CreateGinIndex(
        string tableName,
        string columnName,
        string ginOps = "jsonb_path_ops")
        {
            var indexName = GenerateIndexName(tableName, columnName, "gin");

            return $"""
            CREATE INDEX IF NOT EXISTS {Quote(indexName)}
            ON espv2.{Quote(tableName)}
            USING GIN ({Quote(columnName)} {ginOps});
        """;
        }

        public static string DropGinIndex(string tableName, string columnName)
        {
            var indexName = GenerateIndexName(tableName, columnName, "gin");

            return $"""
            DROP INDEX IF EXISTS espv2.{Quote(indexName)};
        """;
        }

        private static string GenerateIndexName(string table, string column, string type)
        {
            return $"idx_{table.ToLower()}_{column.ToLower()}_{type}";
        }

        private static string Quote(string name)
        {
            return "\"" + name.Replace("\"", "\"\"") + "\"";
        }
    }
}
