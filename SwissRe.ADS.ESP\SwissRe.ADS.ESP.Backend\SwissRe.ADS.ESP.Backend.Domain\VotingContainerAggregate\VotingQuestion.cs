﻿using Ardalis.GuardClauses;
using SwissRe.ADS.ESP.Backend.Domain.Common;
using SwissRe.ADS.ESP.Backend.Domain.FileContentAggregate.Events;

namespace SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate
{
    public class VotingQuestion : GuidEntity, IModificationTracking
    {
        public Guid AgendaId { get; private set; }

        //TODO: Should we encrypt questions as well?
        public string Question { get; private set; } = null!;

        public byte[]? DescriptionEncrypted { get; private set; }
        public VotingOptions AvailableOptions { get; private set; } = null!;
        public DateTime CreatedOn { get; private set; }
        public string CreatedByUserId { get; private set; } = null!; // from t_mp_meeting
        public DateTime LastModifiedOn { get; private set; }
        public string LastModifiedByUserId { get; private set; } = null!;
        public DateTime? DueDate { get; private set; } = null;
        public bool VotesVisibleForPublic { get; private set; } = false; // Ask <PERSON> about NULLS


        /// <summary>
        /// Flag that indicates whether the question is locked for editing. This is turned on as soon as there is at least one user answer on this question.
        /// </summary>
        public bool LockedForEditing { get; private set; } = false;

        private VotingQuestion() { }
        public static VotingQuestion Create(Guid agendaId, string question, byte[]? descriptionEncrypted, VotingOptions availableOptions, string createdByUserId)
        {
            Guard.Against.NullOrWhiteSpace(question, nameof(question), "Question cannot be null or empty.");
            Guard.Against.Null(availableOptions, nameof(availableOptions), "Question need to have available options");
            Guard.Against.Zero(availableOptions.Options.Count, nameof(availableOptions.Options), "Question need to have at least one available option.");

            return new VotingQuestion
            {
                AgendaId = agendaId,
                Question = question,
                DescriptionEncrypted = descriptionEncrypted,
                AvailableOptions = availableOptions,
                CreatedOn = DateTime.UtcNow,
                CreatedByUserId = createdByUserId,
                LastModifiedOn = DateTime.UtcNow,
                LastModifiedByUserId = createdByUserId,
            };
        }

        internal void UpdateQuestion(string newQuestion, byte[] newDescriptionEncrypted, string updatedByUserId)
        {
            if (string.IsNullOrWhiteSpace(newQuestion))
            {
                throw new ArgumentException("New question cannot be null or empty.", nameof(newQuestion));
            }

            Question = newQuestion;
            DescriptionEncrypted = newDescriptionEncrypted;
            LastModifiedOn = DateTime.UtcNow;
            LastModifiedByUserId = updatedByUserId;
        }

        internal void UpdateVotingOptions(VotingOptions newAvailableOptions, string updatedByUserId)
        {
            Guard.Against.Null(newAvailableOptions, nameof(newAvailableOptions), "New available options cannot be null.");
            Guard.Against.Zero(newAvailableOptions.Options.Count, nameof(newAvailableOptions.Options), "New available options must have at least one option.");

            //TODO change to guard
            if (LockedForEditing)
                throw new ArgumentException("Cannot update voting options after answers have been submitted.", nameof(newAvailableOptions));

            AvailableOptions = newAvailableOptions;
            LastModifiedOn = DateTime.UtcNow;
            LastModifiedByUserId = updatedByUserId;
        }

        internal void AddSupportingDocument(byte[] unencryptedFileContents, string fileName, string extension, int size, int order, int numberOfPages, string createdByUserId, bool isSupplementaryFile = false)
        {
            Guard.Against.Null(unencryptedFileContents, "FileContents", "File contents cannot be null.");
            Guard.Against.Zero(unencryptedFileContents.Length, "FileContents", "File contents cannot be empty.");
            Guard.Against.NullOrWhiteSpace(fileName, "FileName", "File name cannot be null or empty.");

            var fileContentId = SequentialGuidGenerator.Next();
            var document = VotingFileMetadata.Create(Id, fileName, Guid.NewGuid(), fileContentId, 1, extension, size, numberOfPages, order, createdByUserId, isSupplementaryFile);
//TODO:             _supportingDocuments.Add(document);

            RaiseEvent(new FileMetadataAddedEvent(unencryptedFileContents, fileContentId));

            LastModifiedOn = DateTime.UtcNow;
            LastModifiedByUserId = createdByUserId;
        }

        internal void RemoveSupportingDocument(Guid supportingDocumentId, string updatedByUserId)
        {
            /*
             * TODO
            var document = _supportingDocuments.FirstOrDefault(d => d.Id == supportingDocumentId);
            if (document == null)
                throw new NullReferenceException("Supporting document was not found.");

            _supportingDocuments.Remove(document);
            */
            LastModifiedOn = DateTime.UtcNow;
            LastModifiedByUserId = updatedByUserId;
        }

        internal void LockForEditing()
        {
            if (LockedForEditing)
                return;

            LockedForEditing = true;
            LastModifiedOn = DateTime.UtcNow;
        }
    }
}
