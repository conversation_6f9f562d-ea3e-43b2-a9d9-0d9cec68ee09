﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;

namespace SwissRe.ADS.ESP.Backend.Application
{
    public static class GlobalExceptionHandler
    {
        /// <summary>
        /// Called for any unhandled exception.
        /// Allows for transforming the HTTP response according to the unhandled exception.
        /// </summary>
        public static async ValueTask OnExceptionAsync(Exception exception, HttpContext context, IHostEnvironment env)
        {
            switch (exception)
            {
                case ArgumentException ex:
                    var errors = new Dictionary<string, string[]>();
                    if (!string.IsNullOrWhiteSpace(ex.ParamName))
                    {
                        errors.Add(ex.ParamName, [ex.Message]);
                    }
                    await context.WriteProblemDetailsAsync("Validation failed.", errors, StatusCodes.Status400BadRequest, ex.Message);
                    break;

                case AggregateRootNotFoundException ex:
                    await context.WriteProblemDetailsAsync($"Aggregate root of type {ex.AggregateRootTypeFullName} was not found.", StatusCodes.Status404NotFound);
                    break;

                case InvalidOperationException:
                    context.Response.StatusCode = StatusCodes.Status409Conflict;
                    await WriteExceptionIfDevelopmentAsync(exception, context, env);
                    break;

                case UnauthorizedException:
                    context.Response.StatusCode = StatusCodes.Status403Forbidden;
                    await WriteExceptionIfDevelopmentAsync(exception, context, env);
                    break;

                case BadHttpRequestException ex:
                    context.Response.StatusCode = ex.StatusCode;
                    await WriteExceptionIfDevelopmentAsync(exception, context, env);
                    break;

                default:
                    context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                    await WriteExceptionIfDevelopmentAsync(exception, context, env);
                    break;
            };
        }

        private static Task WriteExceptionIfDevelopmentAsync(Exception exception, HttpContext context, IHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                context.Response.ContentType = "text/plain";
                return context.Response.WriteAsync(exception.ToString());
            }
            return Task.CompletedTask;
        }
    }
}
