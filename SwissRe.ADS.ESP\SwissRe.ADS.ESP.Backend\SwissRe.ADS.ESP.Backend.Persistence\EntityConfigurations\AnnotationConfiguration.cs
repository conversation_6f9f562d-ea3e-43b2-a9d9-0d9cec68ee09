﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using SwissRe.ADS.ESP.Backend.Domain.DomainAggregate;
using SwissRe.ADS.ESP.Backend.Domain.AnnotationAggregate;

namespace SwissRe.ADS.ESP.Backend.Persistence.EntityConfigurations
{
    public class AnnotationConfiguration : IEntityTypeConfiguration<Annotation>
    {
        public void Configure(EntityTypeBuilder<Annotation> builder)
        {
            builder.ToTable("Annotations");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedNever();
            builder.HasRowVersion();

            builder.Property(x => x.AnnotationUniqueId).IsRequired();
            builder.Property(x => x.EncryptedAnnotationXML).IsRequired().HasMaxLength(5092);

            builder.Property(x => x.FileMetadataType).IsRequired().HasConversion<string>().HasMaxLength(128);
            builder.Property(x => x.CreatedOn).IsRequired();
            builder.Property(x => x.CreatedByUserId).IsRequired().HasMaxLength(128);
            builder.Property(x => x.LastModifiedOn).IsRequired();

            //index by metadata and userid for performance improvement
            builder.HasIndex(x => new { x.FileMetadataId, x.CreatedByUserId });

            builder.HasOne<User>()
                .WithMany()
                .HasForeignKey(x => x.CreatedByUserId)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
