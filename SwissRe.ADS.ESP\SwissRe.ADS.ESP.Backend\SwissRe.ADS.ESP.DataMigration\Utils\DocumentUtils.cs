﻿using Ardalis.GuardClauses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SwissRe.ADS.ESP.DataMigration.Utils
{
    internal static class DocumentUtils
    {
        internal static string[] DocumentAllowedExtension = [".pdf"];


        internal static string RemoveDocumentNameInvalidCharacters(this string originalDocumentName)
        {
            Guard.Against.NullOrWhiteSpace(originalDocumentName, "DocumentName", "Document name cannot be blank or empty.");

            return string.Concat(originalDocumentName.Split(Path.GetInvalidFileNameChars()));
        }
    }
}
