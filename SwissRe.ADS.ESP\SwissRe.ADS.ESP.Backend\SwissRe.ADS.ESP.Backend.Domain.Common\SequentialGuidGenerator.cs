﻿using MassTransit;
using MassTransit.NewIdProviders;

namespace SwissRe.ADS.ESP.Backend.Domain.Common
{
    public static class SequentialGuidGenerator
    {
        static SequentialGuidGenerator() =>
            NewId.SetProcessIdProvider(new CurrentProcessIdProvider()); // https://masstransit.io/documentation/patterns/newid#processid

        /// <summary>
        /// Generates a sequential (ordered) GUID. Great for entity IDs.
        /// As opposed to Guid.NewGuid(), sequential GUID does not cause fragmentation in SQL server's clustered indexes.
        /// These GUIDs are guessable - don't use them for generating passwords, tokens or anything you don't want anyone to guess.
        /// </summary>
        /// 
        /// <remarks>
        /// Some resources on the topic:
        /// - https://www.sqlshack.com/designing-effective-sql-server-clustered-indexes/
        /// - https://www.sqlskills.com/blogs/kimberly/guids-as-primary-keys-andor-the-clustering-key/
        /// - https://masstransit.io/documentation/patterns/newid
        /// </remarks>
        public static Guid Next() =>
            NewId.NextGuid();
    }
}
