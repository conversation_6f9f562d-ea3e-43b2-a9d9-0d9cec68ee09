﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using SwissRe.ADS.MinimalApi.Endpoints;
using SwissRe.ADS.App.Application.Routing;
using SwissRe.ADS.ESP.Backend.Domain.VotingContainerAggregate;
using SwissRe.ADS.ESP.Backend.Application.Common.Persistence;
using SwissRe.ADS.ESP.Backend.Application.Common.ApplicationServices;
using SwissRe.ADS.ESP.Backend.Domain.Common.Agenda;
using Microsoft.AspNetCore.Http;
using SwissRe.ADS.ESP.Backend.Application.Common.PDFTron;
using SwissRe.ADS.ESP.Backend.Application.Common.CurrentUser;

namespace SwissRe.ADS.ESP.Backend.Application.VotingAggregate.Commands
{
    public class CreateVotingQuestionSupportingDocumentEndpoint(UnitOfWork unitOfWork,
        IAgendaAuthorizationService agendaPermissionService,
        IPdfPageCounterService pdfPageCounterService,
        ICurrentUser currentUser) : IEndpoint
    {
        private readonly UnitOfWork _unitOfWork = unitOfWork;
        private readonly IAgendaAuthorizationService _agendaPermissionService = agendaPermissionService;
        private readonly IPdfPageCounterService _pdfPageCounterService = pdfPageCounterService;
        private readonly ICurrentUser _currentUser = currentUser;

        public static void BuildRoute([EndpointRouteBuilder<VotingContainerRouteGroup>] IEndpointRouteBuilder builder, IServiceProvider services) =>
                builder
                .MapPost("/containers/{containerId}/agendas/{agendaId}/questions/{questionId}/documents", (Guid containerId, Guid agendaId, Guid questionId, IFormFile file, CreateVotingQuestionSupportingDocumentEndpoint endpoint) => endpoint.HandleAsync(containerId, agendaId, questionId, file))
                .WithSummary("Add a supporting document to a voting question.")
                .WithDescription("Uploads a supporting document for a specific voting question within a voting agenda. Requires manage permission on the agenda.")
                .DisableAntiforgery()
                .WithAngularName<VotingContainerRouteGroup>("CreateVotingQuestionSupportingDocument");

        public async Task HandleAsync(Guid containerId, Guid agendaId, Guid questionId, IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File cannot be null or empty", nameof(file));

            var canManageAgenda = await _agendaPermissionService.CanUserManageAgendaAsync(containerId, agendaId, AgendaTypeEnum.VotingAgenda);
            if (!canManageAgenda)
                throw new UnauthorizedAccessException("Current user does not have permission to add a file to agenda");


            var container = await _unitOfWork.Repository<VotingContainer>().GetAsync(containerId, true);
            using var stream = new MemoryStream();
            file.CopyTo(stream);
            stream.Position = 0;

            var numberOfPages = _pdfPageCounterService.GetTotalPages(stream);
            var fileExtension = Path.GetExtension(file.FileName) ?? "";
            if (string.IsNullOrWhiteSpace(fileExtension))
                throw new InvalidOperationException("File extension cannot be empty");

            container.AddVotingQuestionSupportingDocument(
                agendaId,
                questionId,
                stream.ToArray(),
                file.FileName,
                fileExtension,
                (int)file.Length,
                0,
                numberOfPages,
                _currentUser.SrUserId,
                false
            );

            await _unitOfWork.CommitTransactionAsync();
        }
    }
}
